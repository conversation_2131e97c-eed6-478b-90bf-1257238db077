{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\BuildProgramOverviewPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from \"react\";\nimport axios from \"axios\";\nimport { Container, Typography, Divider, Box, Paper, List, ListItem, ListItemText, IconButton, Collapse, TextField, Badge, Stack, Chip, Button } from \"@mui/material\";\nimport { ExpandLess, ExpandMore } from \"@mui/icons-material\";\nimport { useTranslation } from 'react-i18next';\nimport { toast } from \"react-toastify\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Section = ({\n  title,\n  items,\n  renderItem,\n  expanded,\n  onToggle,\n  t\n}) => /*#__PURE__*/_jsxDEV(Box, {\n  component: Paper,\n  elevation: 2,\n  sx: {\n    p: 2,\n    mb: 4\n  },\n  children: [/*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Badge, {\n      badgeContent: items.length,\n      color: \"primary\",\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: onToggle,\n      children: expanded ? /*#__PURE__*/_jsxDEV(ExpandLess, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(ExpandMore, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 38\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n    in: expanded,\n    children: items.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"text.secondary\",\n      sx: {\n        mt: 1\n      },\n      children: t('common.noItemsFound', 'No items found')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: items.map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n        alignItems: \"flex-start\",\n        children: /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: renderItem(item)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 15\n        }, this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 26,\n  columnNumber: 3\n}, this);\n_c = Section;\nexport default function BuildProgramOverviewPage() {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const [buildProgram, setbuildProgram] = useState([]);\n  const [search, setSearch] = useState(\"\");\n  const [showbuildProgram, setShowbuildProgram] = useState(true);\n  const navigate = useNavigate();\n  const {\n    programId\n  } = useParams();\n  const fetchbuildProgram = useCallback(async () => {\n    try {\n      const res = await axios.get(\"http://localhost:8000/buildProgram\");\n      const all = res.data;\n      setbuildProgram(programId ? all.filter(s => s.programId === Number(programId)) : all);\n    } catch (err) {\n      toast.error(t('buildProgram.loadError'));\n    }\n  }, [programId, t]);\n  useEffect(() => {\n    fetchbuildProgram();\n  }, [fetchbuildProgram]);\n  const filterBySearch = s => {\n    var _s$program, _s$program$name;\n    return (_s$program = s.program) === null || _s$program === void 0 ? void 0 : (_s$program$name = _s$program.name) === null || _s$program$name === void 0 ? void 0 : _s$program$name.toLowerCase().includes(search.toLowerCase());\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    sx: {\n      py: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        children: [\"\\uD83C\\uDF93 \", t('buildProgram.overviewTitle')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: () => navigate(\"/programs\"),\n        children: [\"\\u21A9\\uFE0F \", t('common.back')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n      fullWidth: true,\n      label: t('buildProgram.searchProgram'),\n      variant: \"outlined\",\n      size: \"small\",\n      value: search,\n      onChange: e => setSearch(e.target.value),\n      sx: {\n        mb: 3\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Section, {\n      title: t('common.programs'),\n      items: buildProgram.filter(filterBySearch),\n      expanded: showbuildProgram,\n      onToggle: () => setShowbuildProgram(prev => !prev),\n      t: t,\n      renderItem: buildProgram => /*#__PURE__*/_jsxDEV(Box, {\n        component: Paper,\n        elevation: 1,\n        sx: {\n          p: 2,\n          borderRadius: 2,\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          fontWeight: \"bold\",\n          gutterBottom: true,\n          children: [\"\\uD83D\\uDCD8 \", t('buildProgram.program'), \": \", buildProgram.program.name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), buildProgram.level && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 1\n          },\n          children: [\"\\uD83C\\uDFAF \", t('buildProgram.level'), \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: buildProgram.level\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 47\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), buildProgram.modules.map(m => /*#__PURE__*/_jsxDEV(Box, {\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            fontWeight: \"bold\",\n            color: \"primary.main\",\n            children: [\"\\uD83D\\uDCE6 \", m.module.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 17\n          }, this), (m.courses || []).map(c => /*#__PURE__*/_jsxDEV(Box, {\n            ml: 2,\n            mt: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: [\"\\uD83D\\uDCD8 \", c.course.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 1,\n              mt: 1,\n              flexWrap: \"wrap\",\n              children: (c.contenus || []).map(ct => /*#__PURE__*/_jsxDEV(Chip, {\n                label: `📄 ${ct.contenu.title}`,\n                size: \"small\",\n                variant: \"outlined\",\n                color: \"secondary\",\n                onClick: () => window.open(ct.contenu.fileUrl, \"_blank\"),\n                sx: {\n                  cursor: \"pointer\"\n                }\n              }, ct.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 21\n            }, this)]\n          }, c.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 19\n          }, this))]\n        }, m.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 15\n        }, this)), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          mt: 2,\n          gap: 2,\n          flexWrap: \"wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"info\",\n            onClick: () => navigate(`/programs/edit/${buildProgram.program.id}`),\n            children: [\"\\uD83D\\uDEE0\\uFE0F \", t('common.edit')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), buildProgram.program.published && /*#__PURE__*/_jsxDEV(Chip, {\n            label: t('buildProgram.published'),\n            icon: /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: 14\n              },\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 25\n            }, this),\n            sx: {\n              fontWeight: \"bold\",\n              px: 1.5,\n              borderRadius: \"12px\",\n              backgroundColor: \"#e6f4ea\",\n              color: \"#1b5e20\",\n              border: \"1px solid #1b5e20\",\n              height: 36\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: buildProgram.program.published ? \"outlined\" : \"contained\",\n            color: buildProgram.program.published ? \"warning\" : \"success\",\n            onClick: async () => {\n              try {\n                await axios.patch(`http://localhost:8000/programs/${buildProgram.program.id}/publish`);\n                toast.success(buildProgram.program.published ? t('buildProgram.unpublishSuccess') : t('buildProgram.publishSuccess'));\n                fetchbuildProgram(); // refresh list\n              } catch (err) {\n                toast.error(t('buildProgram.publishError'));\n              }\n            },\n            sx: {\n              borderRadius: \"12px\",\n              fontWeight: \"bold\",\n              textTransform: \"none\",\n              fontSize: \"0.875rem\",\n              px: 2.5,\n              py: 0.8,\n              height: 36,\n              backgroundColor: buildProgram.program.published ? \"#fff3e0\" : \"#e8f5e9\",\n              color: buildProgram.program.published ? \"#ef6c00\" : \"#2e7d32\",\n              border: `1px solid ${buildProgram.program.published ? \"#ef6c00\" : \"#2e7d32\"}`,\n              \"&:hover\": {\n                backgroundColor: buildProgram.program.published ? \"#ffe0b2\" : \"#c8e6c9\"\n              }\n            },\n            children: buildProgram.program.published ? `❌ ${t('buildProgram.unpublish')}` : `📤 ${t('buildProgram.publish')}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(BuildProgramOverviewPage, \"VptWjRTE6JbToiJ29MVIo7/75xs=\", false, function () {\n  return [useTranslation, useNavigate, useParams];\n});\n_c2 = BuildProgramOverviewPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"Section\");\n$RefreshReg$(_c2, \"BuildProgramOverviewPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "axios", "Container", "Typography", "Divider", "Box", "Paper", "List", "ListItem", "ListItemText", "IconButton", "Collapse", "TextField", "Badge", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "ExpandLess", "ExpandMore", "useTranslation", "toast", "useNavigate", "useParams", "jsxDEV", "_jsxDEV", "Section", "title", "items", "renderItem", "expanded", "onToggle", "t", "component", "elevation", "sx", "p", "mb", "children", "display", "justifyContent", "alignItems", "badgeContent", "length", "color", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "in", "mt", "dense", "map", "item", "primary", "id", "_c", "BuildProgramOverviewPage", "_s", "buildProgram", "setbuildProgram", "search", "setSearch", "showbuildProgram", "setShowbuildProgram", "navigate", "programId", "fetchbuildProgram", "res", "get", "all", "data", "filter", "s", "Number", "err", "error", "filterBySearch", "_s$program", "_s$program$name", "program", "name", "toLowerCase", "includes", "py", "fullWidth", "label", "size", "value", "onChange", "e", "target", "prev", "borderRadius", "fontWeight", "gutterBottom", "level", "my", "modules", "m", "module", "courses", "c", "ml", "course", "direction", "spacing", "flexWrap", "contenus", "ct", "contenu", "window", "open", "fileUrl", "cursor", "gap", "published", "icon", "style", "fontSize", "px", "backgroundColor", "border", "height", "patch", "success", "textTransform", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/BuildProgramOverviewPage.js"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from \"react\";\r\nimport axios from \"axios\";\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Divider,\r\n  Box,\r\n  Paper,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  IconButton,\r\n  Collapse,\r\n  TextField,\r\n  Badge,\r\n  Stack,\r\n  Chip,\r\n  Button\r\n} from \"@mui/material\";\r\nimport { ExpandLess, ExpandMore } from \"@mui/icons-material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport { toast } from \"react-toastify\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nconst Section = ({ title, items, renderItem, expanded, onToggle, t }) => (\r\n  <Box component={Paper} elevation={2} sx={{ p: 2, mb: 4 }}>\r\n    <Box sx={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\" }}>\r\n      <Badge badgeContent={items.length} color=\"primary\">\r\n        <Typography variant=\"h6\">{title}</Typography>\r\n      </Badge>\r\n      <IconButton onClick={onToggle}>\r\n        {expanded ? <ExpandLess /> : <ExpandMore />}\r\n      </IconButton>\r\n    </Box>\r\n    <Collapse in={expanded}>\r\n      {items.length === 0 ? (\r\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\r\n          {t('common.noItemsFound', 'No items found')}\r\n        </Typography>\r\n      ) : (\r\n        <List dense>\r\n          {items.map((item) => (\r\n            <ListItem key={item.id} alignItems=\"flex-start\">\r\n              <ListItemText primary={renderItem(item)} />\r\n            </ListItem>\r\n          ))}\r\n        </List>\r\n      )}\r\n    </Collapse>\r\n  </Box>\r\n);\r\n\r\nexport default function BuildProgramOverviewPage() {\r\n  const { t } = useTranslation();\r\n  const [buildProgram, setbuildProgram] = useState([]);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [showbuildProgram, setShowbuildProgram] = useState(true);\r\n  const navigate = useNavigate();\r\n  const { programId } = useParams();\r\n\r\n  const fetchbuildProgram = useCallback(async () => {\r\n    try {\r\n      const res = await axios.get(\"http://localhost:8000/buildProgram\");\r\n      const all = res.data;\r\n      setbuildProgram(programId ? all.filter(s => s.programId === Number(programId)) : all);\r\n    } catch (err) {\r\n      toast.error(t('buildProgram.loadError'));\r\n    }\r\n  }, [programId, t]);\r\n\r\n  useEffect(() => {\r\n    fetchbuildProgram();\r\n  }, [fetchbuildProgram]);\r\n\r\n  const filterBySearch = (s) =>\r\n    s.program?.name?.toLowerCase().includes(search.toLowerCase());\r\n\r\n  return (\r\n    <Container sx={{ py: 4 }}>\r\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n        <Typography variant=\"h4\">\r\n          🎓 {t('buildProgram.overviewTitle')}\r\n        </Typography>\r\n        <Button variant=\"outlined\" onClick={() => navigate(\"/programs\")}>\r\n          ↩️ {t('common.back')}\r\n        </Button>\r\n      </Box>\r\n\r\n      <Divider sx={{ mb: 2 }} />\r\n\r\n      <TextField\r\n        fullWidth\r\n        label={t('buildProgram.searchProgram')}\r\n        variant=\"outlined\"\r\n        size=\"small\"\r\n        value={search}\r\n        onChange={(e) => setSearch(e.target.value)}\r\n        sx={{ mb: 3 }}\r\n      />\r\n\r\n      <Section\r\n        title={t('common.programs')}\r\n        items={buildProgram.filter(filterBySearch)}\r\n        expanded={showbuildProgram}\r\n        onToggle={() => setShowbuildProgram((prev) => !prev)}\r\n        t={t}\r\n        renderItem={(buildProgram) => (\r\n          <Box component={Paper} elevation={1} sx={{ p: 2, borderRadius: 2, mb: 2 }}>\r\n            <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom>\r\n              📘 {t('buildProgram.program')}: {buildProgram.program.name}\r\n            </Typography>\r\n\r\n            {buildProgram.level && (\r\n              <Typography variant=\"body2\" sx={{ mb: 1 }}>\r\n                🎯 {t('buildProgram.level')}: <strong>{buildProgram.level}</strong>\r\n              </Typography>\r\n            )}\r\n\r\n            <Divider sx={{ my: 1 }} />\r\n\r\n            {buildProgram.modules.map((m) => (\r\n              <Box key={m.id} mb={2}>\r\n                <Typography fontWeight=\"bold\" color=\"primary.main\">📦 {m.module.name}</Typography>\r\n\r\n                {(m.courses || []).map((c) => (\r\n                  <Box key={c.id} ml={2} mt={1}>\r\n                    <Typography variant=\"body2\" fontWeight=\"bold\">📘 {c.course.title}</Typography>\r\n                    <Stack direction=\"row\" spacing={1} mt={1} flexWrap=\"wrap\">\r\n                      {(c.contenus || []).map((ct) => (\r\n                        <Chip\r\n                          key={ct.id}\r\n                          label={`📄 ${ct.contenu.title}`}\r\n                          size=\"small\"\r\n                          variant=\"outlined\"\r\n                          color=\"secondary\"\r\n                          onClick={() => window.open(ct.contenu.fileUrl, \"_blank\")}\r\n                          sx={{ cursor: \"pointer\" }}\r\n                        />\r\n                      ))}\r\n                    </Stack>\r\n                  </Box>\r\n                ))}\r\n              </Box>\r\n            ))}\r\n\r\n            <Box display=\"flex\" justifyContent=\"flex-end\" mt={2} gap={2} flexWrap=\"wrap\">\r\n              <Button\r\n                variant=\"outlined\"\r\n                color=\"info\"\r\n                onClick={() => navigate(`/programs/edit/${buildProgram.program.id}`)}\r\n              >\r\n                🛠️ {t('common.edit')}\r\n              </Button>\r\n\r\n              {buildProgram.program.published && (\r\n                <Chip\r\n                  label={t('buildProgram.published')}\r\n                  icon={<span style={{ fontSize: 14 }}>✅</span>}\r\n                  sx={{\r\n                    fontWeight: \"bold\",\r\n                    px: 1.5,\r\n                    borderRadius: \"12px\",\r\n                    backgroundColor: \"#e6f4ea\",\r\n                    color: \"#1b5e20\",\r\n                    border: \"1px solid #1b5e20\",\r\n                    height: 36,\r\n                  }}\r\n                />\r\n              )}\r\n\r\n              <Button\r\n                variant={buildProgram.program.published ? \"outlined\" : \"contained\"}\r\n                color={buildProgram.program.published ? \"warning\" : \"success\"}\r\n                onClick={async () => {\r\n                  try {\r\n                    await axios.patch(`http://localhost:8000/programs/${buildProgram.program.id}/publish`);\r\n                    toast.success(\r\n                      buildProgram.program.published\r\n                        ? t('buildProgram.unpublishSuccess')\r\n                        : t('buildProgram.publishSuccess')\r\n                    );\r\n                    fetchbuildProgram(); // refresh list\r\n                  } catch (err) {\r\n                    toast.error(t('buildProgram.publishError'));\r\n                  }\r\n                }}\r\n                sx={{\r\n                  borderRadius: \"12px\",\r\n                  fontWeight: \"bold\",\r\n                  textTransform: \"none\",\r\n                  fontSize: \"0.875rem\",\r\n                  px: 2.5,\r\n                  py: 0.8,\r\n                  height: 36,\r\n                  backgroundColor: buildProgram.program.published ? \"#fff3e0\" : \"#e8f5e9\",\r\n                  color: buildProgram.program.published ? \"#ef6c00\" : \"#2e7d32\",\r\n                  border: `1px solid ${buildProgram.program.published ? \"#ef6c00\" : \"#2e7d32\"}`,\r\n                  \"&:hover\": {\r\n                    backgroundColor: buildProgram.program.published ? \"#ffe0b2\" : \"#c8e6c9\",\r\n                  },\r\n                }}\r\n              >\r\n                {buildProgram.program.published ? `❌ ${t('buildProgram.unpublish')}` : `📤 ${t('buildProgram.publish')}`}\r\n              </Button>\r\n\r\n            </Box>\r\n          </Box>\r\n        )}\r\n      />\r\n    </Container>\r\n  );\r\n\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,UAAU,EACVC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,MAAM,QACD,eAAe;AACtB,SAASC,UAAU,EAAEC,UAAU,QAAQ,qBAAqB;AAC5D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,OAAO,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,UAAU;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC;AAAE,CAAC,kBAClEP,OAAA,CAACnB,GAAG;EAAC2B,SAAS,EAAE1B,KAAM;EAAC2B,SAAS,EAAE,CAAE;EAACC,EAAE,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,EAAE,EAAE;EAAE,CAAE;EAAAC,QAAA,gBACvDb,OAAA,CAACnB,GAAG;IAAC6B,EAAE,EAAE;MAAEI,OAAO,EAAE,MAAM;MAAEC,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAAH,QAAA,gBAClFb,OAAA,CAACX,KAAK;MAAC4B,YAAY,EAAEd,KAAK,CAACe,MAAO;MAACC,KAAK,EAAC,SAAS;MAAAN,QAAA,eAChDb,OAAA,CAACrB,UAAU;QAACyC,OAAO,EAAC,IAAI;QAAAP,QAAA,EAAEX;MAAK;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eACRxB,OAAA,CAACd,UAAU;MAACuC,OAAO,EAAEnB,QAAS;MAAAO,QAAA,EAC3BR,QAAQ,gBAAGL,OAAA,CAACP,UAAU;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGxB,OAAA,CAACN,UAAU;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC,eACNxB,OAAA,CAACb,QAAQ;IAACuC,EAAE,EAAErB,QAAS;IAAAQ,QAAA,EACpBV,KAAK,CAACe,MAAM,KAAK,CAAC,gBACjBlB,OAAA,CAACrB,UAAU;MAACyC,OAAO,EAAC,OAAO;MAACD,KAAK,EAAC,gBAAgB;MAACT,EAAE,EAAE;QAAEiB,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,EAC9DN,CAAC,CAAC,qBAAqB,EAAE,gBAAgB;IAAC;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,gBAEbxB,OAAA,CAACjB,IAAI;MAAC6C,KAAK;MAAAf,QAAA,EACRV,KAAK,CAAC0B,GAAG,CAAEC,IAAI,iBACd9B,OAAA,CAAChB,QAAQ;QAAegC,UAAU,EAAC,YAAY;QAAAH,QAAA,eAC7Cb,OAAA,CAACf,YAAY;UAAC8C,OAAO,EAAE3B,UAAU,CAAC0B,IAAI;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAD9BM,IAAI,CAACE,EAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EACP;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACR,CACN;AAACS,EAAA,GA1BIhC,OAAO;AA4Bb,eAAe,SAASiC,wBAAwBA,CAAA,EAAG;EAAAC,EAAA;EACjD,MAAM;IAAE5B;EAAE,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC9B,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+D,MAAM,EAAEC,SAAS,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAMmE,QAAQ,GAAG7C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8C;EAAU,CAAC,GAAG7C,SAAS,CAAC,CAAC;EAEjC,MAAM8C,iBAAiB,GAAGpE,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMqE,GAAG,GAAG,MAAMpE,KAAK,CAACqE,GAAG,CAAC,oCAAoC,CAAC;MACjE,MAAMC,GAAG,GAAGF,GAAG,CAACG,IAAI;MACpBX,eAAe,CAACM,SAAS,GAAGI,GAAG,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACP,SAAS,KAAKQ,MAAM,CAACR,SAAS,CAAC,CAAC,GAAGI,GAAG,CAAC;IACvF,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZxD,KAAK,CAACyD,KAAK,CAAC9C,CAAC,CAAC,wBAAwB,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,CAACoC,SAAS,EAAEpC,CAAC,CAAC,CAAC;EAElBjC,SAAS,CAAC,MAAM;IACdsE,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,MAAMU,cAAc,GAAIJ,CAAC;IAAA,IAAAK,UAAA,EAAAC,eAAA;IAAA,QAAAD,UAAA,GACvBL,CAAC,CAACO,OAAO,cAAAF,UAAA,wBAAAC,eAAA,GAATD,UAAA,CAAWG,IAAI,cAAAF,eAAA,uBAAfA,eAAA,CAAiBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtB,MAAM,CAACqB,WAAW,CAAC,CAAC,CAAC;EAAA;EAE/D,oBACE3D,OAAA,CAACtB,SAAS;IAACgC,EAAE,EAAE;MAAEmD,EAAE,EAAE;IAAE,CAAE;IAAAhD,QAAA,gBACvBb,OAAA,CAACnB,GAAG;MAACiC,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACJ,EAAE,EAAE,CAAE;MAAAC,QAAA,gBAC3Eb,OAAA,CAACrB,UAAU;QAACyC,OAAO,EAAC,IAAI;QAAAP,QAAA,GAAC,eACpB,EAACN,CAAC,CAAC,4BAA4B,CAAC;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACbxB,OAAA,CAACR,MAAM;QAAC4B,OAAO,EAAC,UAAU;QAACK,OAAO,EAAEA,CAAA,KAAMiB,QAAQ,CAAC,WAAW,CAAE;QAAA7B,QAAA,GAAC,eAC5D,EAACN,CAAC,CAAC,aAAa,CAAC;MAAA;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENxB,OAAA,CAACpB,OAAO;MAAC8B,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE;IAAE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1BxB,OAAA,CAACZ,SAAS;MACR0E,SAAS;MACTC,KAAK,EAAExD,CAAC,CAAC,4BAA4B,CAAE;MACvCa,OAAO,EAAC,UAAU;MAClB4C,IAAI,EAAC,OAAO;MACZC,KAAK,EAAE3B,MAAO;MACd4B,QAAQ,EAAGC,CAAC,IAAK5B,SAAS,CAAC4B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;MAC3CvD,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE;IAAE;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAEFxB,OAAA,CAACC,OAAO;MACNC,KAAK,EAAEK,CAAC,CAAC,iBAAiB,CAAE;MAC5BJ,KAAK,EAAEiC,YAAY,CAACa,MAAM,CAACK,cAAc,CAAE;MAC3CjD,QAAQ,EAAEmC,gBAAiB;MAC3BlC,QAAQ,EAAEA,CAAA,KAAMmC,mBAAmB,CAAE4B,IAAI,IAAK,CAACA,IAAI,CAAE;MACrD9D,CAAC,EAAEA,CAAE;MACLH,UAAU,EAAGgC,YAAY,iBACvBpC,OAAA,CAACnB,GAAG;QAAC2B,SAAS,EAAE1B,KAAM;QAAC2B,SAAS,EAAE,CAAE;QAACC,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE2D,YAAY,EAAE,CAAC;UAAE1D,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxEb,OAAA,CAACrB,UAAU;UAACyC,OAAO,EAAC,WAAW;UAACmD,UAAU,EAAC,MAAM;UAACC,YAAY;UAAA3D,QAAA,GAAC,eAC1D,EAACN,CAAC,CAAC,sBAAsB,CAAC,EAAC,IAAE,EAAC6B,YAAY,CAACqB,OAAO,CAACC,IAAI;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EAEZY,YAAY,CAACqC,KAAK,iBACjBzE,OAAA,CAACrB,UAAU;UAACyC,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAC,QAAA,GAAC,eACtC,EAACN,CAAC,CAAC,oBAAoB,CAAC,EAAC,IAAE,eAAAP,OAAA;YAAAa,QAAA,EAASuB,YAAY,CAACqC;UAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CACb,eAEDxB,OAAA,CAACpB,OAAO;UAAC8B,EAAE,EAAE;YAAEgE,EAAE,EAAE;UAAE;QAAE;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAEzBY,YAAY,CAACuC,OAAO,CAAC9C,GAAG,CAAE+C,CAAC,iBAC1B5E,OAAA,CAACnB,GAAG;UAAY+B,EAAE,EAAE,CAAE;UAAAC,QAAA,gBACpBb,OAAA,CAACrB,UAAU;YAAC4F,UAAU,EAAC,MAAM;YAACpD,KAAK,EAAC,cAAc;YAAAN,QAAA,GAAC,eAAG,EAAC+D,CAAC,CAACC,MAAM,CAACnB,IAAI;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,EAEjF,CAACoD,CAAC,CAACE,OAAO,IAAI,EAAE,EAAEjD,GAAG,CAAEkD,CAAC,iBACvB/E,OAAA,CAACnB,GAAG;YAAYmG,EAAE,EAAE,CAAE;YAACrD,EAAE,EAAE,CAAE;YAAAd,QAAA,gBAC3Bb,OAAA,CAACrB,UAAU;cAACyC,OAAO,EAAC,OAAO;cAACmD,UAAU,EAAC,MAAM;cAAA1D,QAAA,GAAC,eAAG,EAACkE,CAAC,CAACE,MAAM,CAAC/E,KAAK;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC9ExB,OAAA,CAACV,KAAK;cAAC4F,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAACxD,EAAE,EAAE,CAAE;cAACyD,QAAQ,EAAC,MAAM;cAAAvE,QAAA,EACtD,CAACkE,CAAC,CAACM,QAAQ,IAAI,EAAE,EAAExD,GAAG,CAAEyD,EAAE,iBACzBtF,OAAA,CAACT,IAAI;gBAEHwE,KAAK,EAAE,MAAMuB,EAAE,CAACC,OAAO,CAACrF,KAAK,EAAG;gBAChC8D,IAAI,EAAC,OAAO;gBACZ5C,OAAO,EAAC,UAAU;gBAClBD,KAAK,EAAC,WAAW;gBACjBM,OAAO,EAAEA,CAAA,KAAM+D,MAAM,CAACC,IAAI,CAACH,EAAE,CAACC,OAAO,CAACG,OAAO,EAAE,QAAQ,CAAE;gBACzDhF,EAAE,EAAE;kBAAEiF,MAAM,EAAE;gBAAU;cAAE,GANrBL,EAAE,CAACtD,EAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAdAuD,CAAC,CAAC/C,EAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeT,CACN,CAAC;QAAA,GApBMoD,CAAC,CAAC5C,EAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBT,CACN,CAAC,eAEFxB,OAAA,CAACnB,GAAG;UAACiC,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,UAAU;UAACY,EAAE,EAAE,CAAE;UAACiE,GAAG,EAAE,CAAE;UAACR,QAAQ,EAAC,MAAM;UAAAvE,QAAA,gBAC1Eb,OAAA,CAACR,MAAM;YACL4B,OAAO,EAAC,UAAU;YAClBD,KAAK,EAAC,MAAM;YACZM,OAAO,EAAEA,CAAA,KAAMiB,QAAQ,CAAC,kBAAkBN,YAAY,CAACqB,OAAO,CAACzB,EAAE,EAAE,CAAE;YAAAnB,QAAA,GACtE,qBACK,EAACN,CAAC,CAAC,aAAa,CAAC;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAERY,YAAY,CAACqB,OAAO,CAACoC,SAAS,iBAC7B7F,OAAA,CAACT,IAAI;YACHwE,KAAK,EAAExD,CAAC,CAAC,wBAAwB,CAAE;YACnCuF,IAAI,eAAE9F,OAAA;cAAM+F,KAAK,EAAE;gBAAEC,QAAQ,EAAE;cAAG,CAAE;cAAAnF,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAE;YAC9Cd,EAAE,EAAE;cACF6D,UAAU,EAAE,MAAM;cAClB0B,EAAE,EAAE,GAAG;cACP3B,YAAY,EAAE,MAAM;cACpB4B,eAAe,EAAE,SAAS;cAC1B/E,KAAK,EAAE,SAAS;cAChBgF,MAAM,EAAE,mBAAmB;cAC3BC,MAAM,EAAE;YACV;UAAE;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACF,eAEDxB,OAAA,CAACR,MAAM;YACL4B,OAAO,EAAEgB,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,UAAU,GAAG,WAAY;YACnE1E,KAAK,EAAEiB,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,SAAS,GAAG,SAAU;YAC9DpE,OAAO,EAAE,MAAAA,CAAA,KAAY;cACnB,IAAI;gBACF,MAAMhD,KAAK,CAAC4H,KAAK,CAAC,kCAAkCjE,YAAY,CAACqB,OAAO,CAACzB,EAAE,UAAU,CAAC;gBACtFpC,KAAK,CAAC0G,OAAO,CACXlE,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAC1BtF,CAAC,CAAC,+BAA+B,CAAC,GAClCA,CAAC,CAAC,6BAA6B,CACrC,CAAC;gBACDqC,iBAAiB,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,OAAOQ,GAAG,EAAE;gBACZxD,KAAK,CAACyD,KAAK,CAAC9C,CAAC,CAAC,2BAA2B,CAAC,CAAC;cAC7C;YACF,CAAE;YACFG,EAAE,EAAE;cACF4D,YAAY,EAAE,MAAM;cACpBC,UAAU,EAAE,MAAM;cAClBgC,aAAa,EAAE,MAAM;cACrBP,QAAQ,EAAE,UAAU;cACpBC,EAAE,EAAE,GAAG;cACPpC,EAAE,EAAE,GAAG;cACPuC,MAAM,EAAE,EAAE;cACVF,eAAe,EAAE9D,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,SAAS,GAAG,SAAS;cACvE1E,KAAK,EAAEiB,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,SAAS,GAAG,SAAS;cAC7DM,MAAM,EAAE,aAAa/D,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,SAAS,GAAG,SAAS,EAAE;cAC7E,SAAS,EAAE;gBACTK,eAAe,EAAE9D,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,SAAS,GAAG;cAChE;YACF,CAAE;YAAAhF,QAAA,EAEDuB,YAAY,CAACqB,OAAO,CAACoC,SAAS,GAAG,KAAKtF,CAAC,CAAC,wBAAwB,CAAC,EAAE,GAAG,MAAMA,CAAC,CAAC,sBAAsB,CAAC;UAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACL;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAGhB;AAACW,EAAA,CAhKuBD,wBAAwB;EAAA,QAChCvC,cAAc,EAIXE,WAAW,EACNC,SAAS;AAAA;AAAA0G,GAAA,GANTtE,wBAAwB;AAAA,IAAAD,EAAA,EAAAuE,GAAA;AAAAC,YAAA,CAAAxE,EAAA;AAAAwE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}