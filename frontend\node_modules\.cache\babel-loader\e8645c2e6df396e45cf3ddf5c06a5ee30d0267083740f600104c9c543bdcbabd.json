{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\components\\\\Session2ChatPopup.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Paper, Typography, Stack, TextField, IconButton, Button, Avatar } from \"@mui/material\";\nimport AddPhotoAlternateIcon from \"@mui/icons-material/AddPhotoAlternate\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport EmojiPicker from \"emoji-picker-react\";\nimport io from \"socket.io-client\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SOCKET_URL = \"http://localhost:8000\";\nexport default function UnifiedSessionChatPopup({\n  user\n}) {\n  _s();\n  // const globalSocketRef = useRef(null);\n\n  const [generalChatMessages, setGeneralChatMessages] = useState([]);\n  const generalSocketRef = useRef(null);\n\n  // Which chat is active: \"session\" or \"seance\"\n  const [selectedTab, setSelectedTab] = useState(\"session\");\n\n  // Session2 data\n  const [session2s, setSession2s] = useState([]); // All sessions user is in\n  const [session2Id, setSession2Id] = useState(null);\n\n  // Seance data\n  const [seances, setSeances] = useState([]); // All seances for current session\n  const [seanceId, setSeanceId] = useState(null);\n\n  // Chat states (messages, input, etc)\n  const [sessionChatMessages, setSessionChatMessages] = useState([]);\n  const [seanceChatMessages, setSeanceChatMessages] = useState([]);\n  const [newMsg, setNewMsg] = useState(\"\");\n  const [newFile, setNewFile] = useState(null);\n  const [showEmoji, setShowEmoji] = useState(false);\n  const chatBottomRef = useRef();\n  const fileInputRef = useRef();\n  const sessionSocketRef = useRef(null);\n  const seanceSocketRef = useRef(null);\n\n  // UI popup\n  const [open, setOpen] = useState(false);\n  useEffect(() => {\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [sessionChatMessages, seanceChatMessages, generalChatMessages, selectedTab, open]);\n\n  //genralechat \n  useEffect(() => {\n    // Only connect once\n    if (generalSocketRef.current) {\n      generalSocketRef.current.disconnect();\n    }\n    const s = io(`${SOCKET_URL}/general-chat`, {\n      transports: [\"websocket\"]\n    });\n    generalSocketRef.current = s;\n\n    // Fetch messages\n    s.emit(\"fetchGeneralMessages\");\n    s.on(\"generalMessages\", msgs => setGeneralChatMessages(msgs));\n    s.on(\"newGeneralMessage\", msg => setGeneralChatMessages(prev => [...prev, msg]));\n    s.on(\"deleteGeneralMessage\", ({\n      id\n    }) => setGeneralChatMessages(prev => prev.filter(m => m.id !== id)));\n    return () => s.disconnect();\n  }, []); // []: only once\n\n  // Fetch session2s for the user\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user.id)) return;\n    fetch(`http://localhost:8000/users/${user.id}/sessions2`).then(res => res.json()).then(data => {\n      var _sessions$0$id, _sessions$;\n      // Normalize (handles both array of userSession2 or array of session2)\n      const sessions = data.map(s => {\n        var _s$session;\n        return s.session2 ? {\n          id: s.session2.id,\n          name: s.session2.name\n        } : s.session2Id ? {\n          id: s.session2Id,\n          name: ((_s$session = s.session2) === null || _s$session === void 0 ? void 0 : _s$session.name) || \"\"\n        } : s;\n      });\n      setSession2s(sessions);\n      setSession2Id((_sessions$0$id = (_sessions$ = sessions[0]) === null || _sessions$ === void 0 ? void 0 : _sessions$.id) !== null && _sessions$0$id !== void 0 ? _sessions$0$id : null);\n    });\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Fetch seances for current session2\n  useEffect(() => {\n    if (!session2Id) {\n      setSeances([]);\n      setSeanceId(null);\n      return;\n    }\n    fetch(`http://localhost:8000/seance-formateur/session/${session2Id}`).then(res => res.json()).then(data => {\n      var _data$0$id, _data$;\n      setSeances(data);\n      setSeanceId((_data$0$id = (_data$ = data[0]) === null || _data$ === void 0 ? void 0 : _data$.id) !== null && _data$0$id !== void 0 ? _data$0$id : null); // pick first as default, or let user choose if multiple\n    });\n  }, [session2Id]);\n\n  // Fetch messages for both chats\n  useEffect(() => {\n    if (session2Id) {\n      fetch(`http://localhost:8000/session2-chat-messages/${session2Id}`).then(res => res.json()).then(msgs => setSessionChatMessages(msgs));\n    }\n    if (seanceId) {\n      fetch(`http://localhost:8000/chat-messages/${seanceId}`).then(res => res.json()).then(msgs => setSeanceChatMessages(msgs));\n    }\n  }, [session2Id, seanceId]);\n\n  // Session chat socket\n  useEffect(() => {\n    if (!session2Id) return;\n    if (sessionSocketRef.current) {\n      sessionSocketRef.current.emit(\"leaveSession2\", {\n        session2Id: Number(session2Id)\n      });\n      sessionSocketRef.current.disconnect();\n    }\n    const s = io(SOCKET_URL, {\n      transports: [\"websocket\"]\n    });\n    sessionSocketRef.current = s;\n    s.emit(\"joinSession2\", {\n      session2Id: Number(session2Id)\n    });\n    s.on(\"newSession2Message\", msg => setSessionChatMessages(prev => [...prev, msg]));\n    s.on(\"deleteSession2Message\", payload => setSessionChatMessages(prev => prev.filter(m => m.id !== payload.id)));\n    s.on(\"clearSession2Messages\", () => setSessionChatMessages([]));\n    return () => {\n      s.emit(\"leaveSession2\", {\n        session2Id: Number(session2Id)\n      });\n      s.disconnect();\n    };\n  }, [session2Id]);\n\n  // Seance chat socket\n  useEffect(() => {\n    if (!seanceId) return;\n    if (seanceSocketRef.current) {\n      seanceSocketRef.current.emit(\"leaveSeance\", {\n        seanceId: Number(seanceId)\n      });\n      seanceSocketRef.current.disconnect();\n    }\n    const s = io(SOCKET_URL, {\n      transports: [\"websocket\"]\n    });\n    seanceSocketRef.current = s;\n    s.emit(\"joinSeance\", {\n      seanceId: Number(seanceId)\n    });\n    s.on(\"newSeanceMessage\", msg => setSeanceChatMessages(prev => [...prev, msg]));\n    s.on(\"deleteSeanceMessage\", payload => setSeanceChatMessages(prev => prev.filter(m => m.id !== payload.id)));\n    s.on(\"clearSeanceMessages\", () => setSeanceChatMessages([]));\n    return () => {\n      s.emit(\"leaveSeance\", {\n        seanceId: Number(seanceId)\n      });\n      s.disconnect();\n    };\n  }, [seanceId]);\n\n  // Scroll to bottom\n  useEffect(() => {\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [sessionChatMessages, seanceChatMessages, selectedTab, open]);\n\n  // Tab switch resets input\n  useEffect(() => {\n    setNewMsg(\"\");\n    setNewFile(null);\n    setShowEmoji(false);\n  }, [selectedTab, session2Id, seanceId]);\n\n  // Emoji\n  const handleEmoji = e => {\n    setNewMsg(prev => prev + e.emoji);\n    setShowEmoji(false);\n  };\n\n  // Send message (text/file) for current tab\n  const handleChatSend = async () => {\n    let socket;\n    if (selectedTab === \"session\") socket = sessionSocketRef.current;else if (selectedTab === \"seance\") socket = seanceSocketRef.current;else if (selectedTab === \"general\") socket = generalSocketRef.current;\n    if (!socket) return;\n\n    // File message\n    if (newFile) {\n      const formData = new FormData();\n      formData.append(\"file\", newFile);\n      let uploadUrl = \"\";\n      if (selectedTab === \"general\") {\n        uploadUrl = \"http://localhost:8000/general-chat-messages/upload-chat\";\n      } else if (selectedTab === \"session\") {\n        formData.append(\"session2Id\", session2Id);\n        uploadUrl = \"http://localhost:8000/session2-chat-messages/upload-chat\";\n      } else if (selectedTab === \"seance\") {\n        formData.append(\"seanceId\", seanceId);\n        uploadUrl = \"http://localhost:8000/chat-messages/upload-chat\";\n      }\n      try {\n        const res = await fetch(uploadUrl, {\n          method: \"POST\",\n          body: formData\n        });\n        const fileData = await res.json();\n        if (selectedTab === \"general\") {\n          socket.emit(\"sendGeneralMessage\", {\n            content: fileData.fileUrl,\n            type: fileData.fileType || \"file\",\n            senderId: user.id\n          });\n        } else if (selectedTab === \"session\") {\n          socket.emit(\"sendSession2Message\", {\n            content: fileData.fileUrl,\n            type: fileData.fileType || \"file\",\n            session2Id: Number(session2Id),\n            senderId: user.id\n          });\n        } else {\n          socket.emit(\"sendSeanceMessage\", {\n            content: fileData.fileUrl,\n            type: fileData.fileType || \"file\",\n            seanceId: Number(seanceId),\n            senderId: user.id\n          });\n        }\n      } catch {\n        alert(\"Erreur upload fichier\");\n      }\n      setNewFile(null);\n      if (fileInputRef.current) fileInputRef.current.value = \"\";\n      return;\n    }\n    // Text message\n    if (newMsg.trim()) {\n      if (selectedTab === \"general\") {\n        socket.emit(\"sendGeneralMessage\", {\n          content: newMsg,\n          type: \"text\",\n          senderId: user.id\n        });\n      } else if (selectedTab === \"session\") {\n        socket.emit(\"sendSession2Message\", {\n          content: newMsg,\n          type: \"text\",\n          session2Id: Number(session2Id),\n          senderId: user.id\n        });\n      } else {\n        socket.emit(\"sendSeanceMessage\", {\n          content: newMsg,\n          type: \"text\",\n          seanceId: Number(seanceId),\n          senderId: user.id\n        });\n      }\n      setNewMsg(\"\");\n    }\n  };\n\n  // Delete message\n  const handleDeleteMsg = async msgId => {\n    if (selectedTab === \"session\") {\n      await fetch(`http://localhost:8000/session2-chat-messages/${msgId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      setSessionChatMessages(prev => prev.filter(m => m.id !== msgId));\n    } else if (selectedTab === \"seance\") {\n      await fetch(`http://localhost:8000/chat-messages/${msgId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      setSeanceChatMessages(prev => prev.filter(m => m.id !== msgId));\n    } else if (selectedTab === \"general\") {\n      await fetch(`http://localhost:8000/general-chat-messages/${msgId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      setGeneralChatMessages(prev => prev.filter(m => m.id !== msgId));\n    }\n  };\n\n  // --- UI ---\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setOpen(!open),\n      style: {\n        position: \"fixed\",\n        bottom: \"104px\",\n        right: \"24px\",\n        width: \"64px\",\n        height: \"64px\",\n        borderRadius: \"50%\",\n        background: open ? \"#fff\" : \"#d32f2f\",\n        color: open ? \"#d32f2f\" : \"#fff\",\n        fontSize: \"28px\",\n        boxShadow: \"0 4px 24px rgba(0, 0, 0, 0.22)\",\n        border: open ? \"3px solid #d32f2f\" : \"none\",\n        zIndex: 2000,\n        cursor: \"pointer\",\n        transition: \"all 0.18s ease-in-out\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        outline: \"none\"\n      },\n      \"aria-label\": open ? \"Close chat\" : \"Open chat\",\n      onMouseOver: e => {\n        e.currentTarget.style.background = open ? \"#fbeaec\" : \"#e53935\";\n      },\n      onMouseOut: e => {\n        e.currentTarget.style.background = open ? \"#fff\" : \"#d32f2f\";\n      },\n      onFocus: e => {\n        e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(211, 47, 47, 0.4)\";\n      },\n      onBlur: e => {\n        e.currentTarget.style.boxShadow = \"0 4px 24px rgba(0, 0, 0, 0.22)\";\n      },\n      children: open ? \"✕\" : \"💬\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        bottom: 166,\n        right: 24,\n        fontSize: \"12px\",\n        color: \"white\",\n        background: \"rgba(0,0,0,0.8)\",\n        padding: \"6px 12px\",\n        borderRadius: \"6px\",\n        whiteSpace: \"nowrap\",\n        zIndex: 2001\n      },\n      children: \"Chat de session\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this), open && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        position: \"fixed\",\n        bottom: 180,\n        right: 32,\n        width: 410,\n        maxHeight: \"74vh\",\n        borderRadius: 4,\n        boxShadow: 10,\n        zIndex: 2100,\n        display: \"flex\",\n        flexDirection: \"column\",\n        p: 0,\n        overflow: \"hidden\",\n        background: \"#f9f9fa\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        bgcolor: \"#fff\",\n        px: 2,\n        pt: 1.5,\n        pb: 0.5,\n        borderBottom: \"1px solid #e8e8e8\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: selectedTab === \"session\" ? \"contained\" : \"text\",\n          onClick: () => setSelectedTab(\"session\"),\n          color: \"error\",\n          size: \"small\",\n          sx: {\n            borderRadius: 3,\n            fontWeight: 600,\n            boxShadow: selectedTab === \"session\" ? 3 : 0,\n            mx: 1,\n            px: 2\n          },\n          children: \"Session Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: selectedTab === \"seance\" ? \"contained\" : \"text\",\n          onClick: () => setSelectedTab(\"seance\"),\n          color: \"primary\",\n          size: \"small\",\n          disabled: !seanceId,\n          sx: {\n            borderRadius: 3,\n            fontWeight: 600,\n            boxShadow: selectedTab === \"seance\" ? 3 : 0,\n            mx: 1,\n            px: 2\n          },\n          children: \"S\\xE9ance Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: selectedTab === \"general\" ? \"contained\" : \"text\",\n          onClick: () => setSelectedTab(\"general\"),\n          color: \"secondary\",\n          size: \"small\",\n          sx: {\n            borderRadius: 3,\n            fontWeight: 600,\n            boxShadow: selectedTab === \"general\" ? 3 : 0,\n            mx: 1,\n            px: 2\n          },\n          children: \"Chat G\\xE9n\\xE9ral\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 11\n      }, this), session2s.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        bgcolor: \"#f6f6fc\",\n        px: 2,\n        py: 1.5,\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: session2Id,\n          onChange: e => setSession2Id(Number(e.target.value)),\n          style: {\n            width: \"100%\",\n            padding: \"6px 10px\",\n            borderRadius: 8,\n            border: \"1px solid #ccc\",\n            fontWeight: 500\n          },\n          children: session2s.map(s => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: s.id,\n            children: s.name\n          }, s.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 13\n      }, this), selectedTab === \"seance\" && seances.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        bgcolor: \"#f6f6fc\",\n        px: 2,\n        py: 1.5,\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: seanceId,\n          onChange: e => setSeanceId(Number(e.target.value)),\n          style: {\n            width: \"100%\",\n            padding: \"6px 10px\",\n            borderRadius: 8,\n            border: \"1px solid #ccc\",\n            fontWeight: 500\n          },\n          children: seances.map(s => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: s.id,\n            children: s.title || `Séance ${s.id}`\n          }, s.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          pt: 1.5,\n          flex: 1,\n          overflowY: \"auto\",\n          background: \"#f7f7fa\",\n          borderBottom: \"1px solid #eee\",\n          minHeight: 200,\n          scrollbarWidth: \"thin\",\n          \"&::-webkit-scrollbar\": {\n            width: \"7px\",\n            background: \"#eaeaea\",\n            borderRadius: 5\n          },\n          \"&::-webkit-scrollbar-thumb\": {\n            background: \"#e1e1e1\",\n            borderRadius: 5\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 1,\n          children: [(selectedTab === \"session\" ? sessionChatMessages : selectedTab === \"seance\" ? seanceChatMessages : generalChatMessages).map((msg, i) => {\n            var _msg$sender, _msg$sender2, _msg$sender3, _msg$sender4, _msg$sender5, _msg$sender6, _msg$sender6$profileP, _msg$sender7, _msg$sender8, _msg$sender9, _msg$sender9$name, _msg$sender9$name$, _msg$sender10, _msg$sender11, _msg$sender12;\n            return /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                alignItems: \"flex-start\",\n                gap: 1.5,\n                bgcolor: ((_msg$sender = msg.sender) === null || _msg$sender === void 0 ? void 0 : _msg$sender.id) === user.id ? \"#fff8f8\" : \"#fff\",\n                borderRadius: 3,\n                boxShadow: ((_msg$sender2 = msg.sender) === null || _msg$sender2 === void 0 ? void 0 : _msg$sender2.id) === user.id ? \"0 1px 8px #ffe0e0\" : \"0 1px 8px #e2e2ef0c\",\n                border: \"1px solid #f2f2f3\",\n                px: 1.5,\n                py: 1,\n                mr: ((_msg$sender3 = msg.sender) === null || _msg$sender3 === void 0 ? void 0 : _msg$sender3.id) === user.id ? 0 : \"auto\",\n                ml: ((_msg$sender4 = msg.sender) === null || _msg$sender4 === void 0 ? void 0 : _msg$sender4.id) === user.id ? \"auto\" : 0,\n                maxWidth: \"85%\"\n              },\n              children: [(_msg$sender5 = msg.sender) !== null && _msg$sender5 !== void 0 && _msg$sender5.profilePic ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: (_msg$sender6 = msg.sender) !== null && _msg$sender6 !== void 0 && (_msg$sender6$profileP = _msg$sender6.profilePic) !== null && _msg$sender6$profileP !== void 0 && _msg$sender6$profileP.startsWith('http') ? msg.sender.profilePic : `http://localhost:8000${((_msg$sender7 = msg.sender) === null || _msg$sender7 === void 0 ? void 0 : _msg$sender7.profilePic) || '/profile-pics/default.png'}`,\n                alt: (_msg$sender8 = msg.sender) === null || _msg$sender8 === void 0 ? void 0 : _msg$sender8.name,\n                style: {\n                  width: 32,\n                  height: 32,\n                  borderRadius: \"50%\",\n                  marginTop: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 32,\n                  height: 32,\n                  bgcolor: \"#d5dde9\",\n                  mt: 0.25\n                },\n                children: ((_msg$sender9 = msg.sender) === null || _msg$sender9 === void 0 ? void 0 : (_msg$sender9$name = _msg$sender9.name) === null || _msg$sender9$name === void 0 ? void 0 : (_msg$sender9$name$ = _msg$sender9$name[0]) === null || _msg$sender9$name$ === void 0 ? void 0 : _msg$sender9$name$.toUpperCase()) || \"?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: \"bold\",\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 15\n                    },\n                    children: ((_msg$sender10 = msg.sender) === null || _msg$sender10 === void 0 ? void 0 : _msg$sender10.name) || \"Anonyme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      color: \"#aaa\",\n                      fontSize: 11.5,\n                      fontWeight: 500,\n                      ml: 1,\n                      mt: 0.3\n                    },\n                    children: [(_msg$sender11 = msg.sender) !== null && _msg$sender11 !== void 0 && _msg$sender11.role ? \"· \" + msg.sender.role : \"\", msg.createdAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        marginLeft: 8\n                      },\n                      children: new Date(msg.createdAt).toLocaleTimeString([], {\n                        hour: \"2-digit\",\n                        minute: \"2-digit\"\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 23\n                  }, this), ((_msg$sender12 = msg.sender) === null || _msg$sender12 === void 0 ? void 0 : _msg$sender12.id) === user.id && /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDeleteMsg(msg.id),\n                    color: \"error\",\n                    sx: {\n                      ml: \"auto\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 0.5,\n                  children: [msg.type === \"text\" && /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      fontSize: 15,\n                      color: \"#222\"\n                    },\n                    children: msg.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 566,\n                    columnNumber: 25\n                  }, this), msg.type === \"image\" && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: msg.content,\n                    alt: \"img\",\n                    style: {\n                      maxWidth: 170,\n                      borderRadius: 7,\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 25\n                  }, this), msg.type === \"audio\" && /*#__PURE__*/_jsxDEV(\"audio\", {\n                    controls: true,\n                    src: msg.content,\n                    style: {\n                      maxWidth: 150,\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 574,\n                    columnNumber: 25\n                  }, this), msg.type === \"video\" && /*#__PURE__*/_jsxDEV(\"video\", {\n                    controls: true,\n                    src: msg.content,\n                    style: {\n                      maxWidth: 160,\n                      borderRadius: 7,\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 577,\n                    columnNumber: 25\n                  }, this), msg.type === \"file\" && /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: msg.content,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    style: {\n                      display: \"block\",\n                      marginTop: 4,\n                      color: \"#0072ff\"\n                    },\n                    children: [\"\\uD83D\\uDCCE \", msg.content.split(\"/\").pop()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this)]\n            }, msg.id || i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 17\n            }, this);\n          }), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: chatBottomRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          background: \"#fff\",\n          borderTop: \"1px solid #e8e8e8\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          value: newMsg,\n          size: \"small\",\n          placeholder: \"\\xC9cris un message\\u2026\",\n          onChange: e => setNewMsg(e.target.value),\n          onKeyDown: e => e.key === \"Enter\" && handleChatSend(),\n          sx: {\n            background: \"#f8f8f8\",\n            borderRadius: 2\n          },\n          inputProps: {\n            style: {\n              fontSize: 15\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setShowEmoji(v => !v),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            role: \"img\",\n            \"aria-label\": \"emoji\",\n            children: \"\\uD83D\\uDE00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          component: \"label\",\n          color: newFile ? \"success\" : \"primary\",\n          children: [/*#__PURE__*/_jsxDEV(AddPhotoAlternateIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            hidden: true,\n            ref: fileInputRef,\n            type: \"file\",\n            accept: \"image/*,video/*,audio/*,application/pdf\",\n            onChange: e => setNewFile(e.target.files[0])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleChatSend,\n          variant: \"contained\",\n          color: selectedTab === \"session\" ? \"error\" : \"primary\",\n          disabled: !newMsg.trim() && !newFile,\n          sx: {\n            px: 2,\n            fontWeight: 600\n          },\n          children: \"Envoyer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 624,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 11\n      }, this), showEmoji && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"absolute\",\n          bottom: 90,\n          right: 30,\n          zIndex: 11\n        },\n        children: /*#__PURE__*/_jsxDEV(EmojiPicker, {\n          onEmojiClick: handleEmoji,\n          autoFocusSearch: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 13\n      }, this), newFile && /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"primary\",\n        fontSize: 13,\n        ml: 2,\n        mt: 0.5,\n        children: [\"Fichier pr\\xEAt \\xE0 envoyer : \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: newFile.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 40\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(UnifiedSessionChatPopup, \"Ill+ZC80K9uNedVwZtlNlCKbk6U=\");\n_c = UnifiedSessionChatPopup;\nvar _c;\n$RefreshReg$(_c, \"UnifiedSessionChatPopup\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Paper", "Typography", "<PERSON><PERSON>", "TextField", "IconButton", "<PERSON><PERSON>", "Avatar", "AddPhotoAlternateIcon", "DeleteIcon", "EmojiPicker", "io", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SOCKET_URL", "UnifiedSessionChatPopup", "user", "_s", "generalChatMessages", "setGeneralChatMessages", "generalSocketRef", "selectedTab", "setSelectedTab", "session2s", "setSession2s", "session2Id", "setSession2Id", "seances", "setSeances", "seanceId", "setSeanceId", "sessionChatMessages", "setSessionChatMessages", "seanceChatMessages", "setSeanceChatMessages", "newMsg", "setNewMsg", "newFile", "setNewFile", "showE<PERSON>ji", "setShowEmoji", "chatBottomRef", "fileInputRef", "sessionSocketRef", "seanceSocketRef", "open", "<PERSON><PERSON><PERSON>", "current", "scrollIntoView", "behavior", "disconnect", "s", "transports", "emit", "on", "msgs", "msg", "prev", "id", "filter", "m", "fetch", "then", "res", "json", "data", "_sessions$0$id", "_sessions$", "sessions", "map", "_s$session", "session2", "name", "_data$0$id", "_data$", "Number", "payload", "handleEmoji", "e", "emoji", "handleChatSend", "socket", "formData", "FormData", "append", "uploadUrl", "method", "body", "fileData", "content", "fileUrl", "type", "fileType", "senderId", "alert", "value", "trim", "handleDeleteMsg", "msgId", "headers", "JSON", "stringify", "userId", "children", "onClick", "style", "position", "bottom", "right", "width", "height", "borderRadius", "background", "color", "fontSize", "boxShadow", "border", "zIndex", "cursor", "transition", "display", "alignItems", "justifyContent", "outline", "onMouseOver", "currentTarget", "onMouseOut", "onFocus", "onBlur", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showTooltip", "padding", "whiteSpace", "sx", "maxHeight", "flexDirection", "p", "overflow", "bgcolor", "px", "pt", "pb", "borderBottom", "variant", "size", "fontWeight", "mx", "disabled", "length", "py", "onChange", "target", "title", "flex", "overflowY", "minHeight", "scrollbarWidth", "spacing", "i", "_msg$sender", "_msg$sender2", "_msg$sender3", "_msg$sender4", "_msg$sender5", "_msg$sender6", "_msg$sender6$profileP", "_msg$sender7", "_msg$sender8", "_msg$sender9", "_msg$sender9$name", "_msg$sender9$name$", "_msg$sender10", "_msg$sender11", "_msg$sender12", "gap", "sender", "mr", "ml", "max<PERSON><PERSON><PERSON>", "profilePic", "src", "startsWith", "alt", "marginTop", "mt", "toUpperCase", "role", "createdAt", "marginLeft", "Date", "toLocaleTimeString", "hour", "minute", "controls", "href", "rel", "split", "pop", "ref", "borderTop", "fullWidth", "placeholder", "onKeyDown", "key", "inputProps", "v", "component", "hidden", "accept", "files", "onEmojiClick", "autoFocusSearch", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/components/Session2ChatPopup.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n  Box, Paper, Typography, Stack, TextField, IconButton, Button, Avatar,\r\n} from \"@mui/material\";\r\nimport AddPhotoAlternateIcon from \"@mui/icons-material/AddPhotoAlternate\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport EmojiPicker from \"emoji-picker-react\";\r\nimport io from \"socket.io-client\";\r\n\r\n\r\nconst SOCKET_URL = \"http://localhost:8000\";\r\n\r\nexport default function UnifiedSessionChatPopup({ user }) {\r\n\r\n  // const globalSocketRef = useRef(null);\r\n\r\n\r\n  const [generalChatMessages, setGeneralChatMessages] = useState([]);\r\n  const generalSocketRef = useRef(null);\r\n\r\n  // Which chat is active: \"session\" or \"seance\"\r\n  const [selectedTab, setSelectedTab] = useState(\"session\");\r\n\r\n  // Session2 data\r\n  const [session2s, setSession2s] = useState([]); // All sessions user is in\r\n  const [session2Id, setSession2Id] = useState(null);\r\n\r\n  // Seance data\r\n  const [seances, setSeances] = useState([]); // All seances for current session\r\n  const [seanceId, setSeanceId] = useState(null);\r\n\r\n  // Chat states (messages, input, etc)\r\n  const [sessionChatMessages, setSessionChatMessages] = useState([]);\r\n  const [seanceChatMessages, setSeanceChatMessages] = useState([]);\r\n\r\n  const [newMsg, setNewMsg] = useState(\"\");\r\n  const [newFile, setNewFile] = useState(null);\r\n  const [showEmoji, setShowEmoji] = useState(false);\r\n\r\n  const chatBottomRef = useRef();\r\n  const fileInputRef = useRef();\r\n  const sessionSocketRef = useRef(null);\r\n  const seanceSocketRef = useRef(null);\r\n\r\n  // UI popup\r\n  const [open, setOpen] = useState(false);\r\n  useEffect(() => {\r\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [sessionChatMessages, seanceChatMessages, generalChatMessages, selectedTab, open]);\r\n\r\n  //genralechat \r\n  useEffect(() => {\r\n    // Only connect once\r\n    if (generalSocketRef.current) {\r\n      generalSocketRef.current.disconnect();\r\n    }\r\n    const s = io(`${SOCKET_URL}/general-chat`, { transports: [\"websocket\"] });\r\n    generalSocketRef.current = s;\r\n\r\n    // Fetch messages\r\n    s.emit(\"fetchGeneralMessages\");\r\n    s.on(\"generalMessages\", msgs => setGeneralChatMessages(msgs));\r\n    s.on(\"newGeneralMessage\", msg =>\r\n      setGeneralChatMessages(prev => [...prev, msg])\r\n    );\r\n    s.on(\"deleteGeneralMessage\", ({ id }) =>\r\n      setGeneralChatMessages(prev => prev.filter(m => m.id !== id))\r\n    );\r\n    return () => s.disconnect();\r\n  }, []); // []: only once\r\n\r\n  // Fetch session2s for the user\r\n  useEffect(() => {\r\n    if (!user?.id) return;\r\n    fetch(`http://localhost:8000/users/${user.id}/sessions2`)\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        // Normalize (handles both array of userSession2 or array of session2)\r\n        const sessions = data.map(s =>\r\n          s.session2\r\n            ? { id: s.session2.id, name: s.session2.name }\r\n            : s.session2Id\r\n              ? { id: s.session2Id, name: s.session2?.name || \"\" }\r\n              : s\r\n        );\r\n        setSession2s(sessions);\r\n        setSession2Id(sessions[0]?.id ?? null);\r\n      });\r\n  }, [user?.id]);\r\n\r\n  // Fetch seances for current session2\r\n  useEffect(() => {\r\n    if (!session2Id) {\r\n      setSeances([]);\r\n      setSeanceId(null);\r\n      return;\r\n    }\r\n    fetch(`http://localhost:8000/seance-formateur/session/${session2Id}`)\r\n      .then(res => res.json())\r\n      .then(data => {\r\n        setSeances(data);\r\n        setSeanceId(data[0]?.id ?? null); // pick first as default, or let user choose if multiple\r\n      });\r\n  }, [session2Id]);\r\n\r\n  // Fetch messages for both chats\r\n  useEffect(() => {\r\n    if (session2Id) {\r\n      fetch(`http://localhost:8000/session2-chat-messages/${session2Id}`)\r\n        .then(res => res.json())\r\n        .then(msgs => setSessionChatMessages(msgs));\r\n    }\r\n    if (seanceId) {\r\n      fetch(`http://localhost:8000/chat-messages/${seanceId}`)\r\n        .then(res => res.json())\r\n        .then(msgs => setSeanceChatMessages(msgs));\r\n    }\r\n  }, [session2Id, seanceId]);\r\n\r\n  // Session chat socket\r\n  useEffect(() => {\r\n    if (!session2Id) return;\r\n    if (sessionSocketRef.current) {\r\n      sessionSocketRef.current.emit(\"leaveSession2\", { session2Id: Number(session2Id) });\r\n      sessionSocketRef.current.disconnect();\r\n    }\r\n    const s = io(SOCKET_URL, { transports: [\"websocket\"] });\r\n    sessionSocketRef.current = s;\r\n    s.emit(\"joinSession2\", { session2Id: Number(session2Id) });\r\n    s.on(\"newSession2Message\", msg => setSessionChatMessages(prev => [...prev, msg]));\r\n    s.on(\"deleteSession2Message\", payload => setSessionChatMessages(prev => prev.filter(m => m.id !== payload.id)));\r\n    s.on(\"clearSession2Messages\", () => setSessionChatMessages([]));\r\n    return () => {\r\n      s.emit(\"leaveSession2\", { session2Id: Number(session2Id) });\r\n      s.disconnect();\r\n    };\r\n  }, [session2Id]);\r\n\r\n  // Seance chat socket\r\n  useEffect(() => {\r\n    if (!seanceId) return;\r\n    if (seanceSocketRef.current) {\r\n      seanceSocketRef.current.emit(\"leaveSeance\", { seanceId: Number(seanceId) });\r\n      seanceSocketRef.current.disconnect();\r\n    }\r\n    const s = io(SOCKET_URL, { transports: [\"websocket\"] });\r\n    seanceSocketRef.current = s;\r\n    s.emit(\"joinSeance\", { seanceId: Number(seanceId) });\r\n    s.on(\"newSeanceMessage\", msg => setSeanceChatMessages(prev => [...prev, msg]));\r\n    s.on(\"deleteSeanceMessage\", payload => setSeanceChatMessages(prev => prev.filter(m => m.id !== payload.id)));\r\n    s.on(\"clearSeanceMessages\", () => setSeanceChatMessages([]));\r\n    return () => {\r\n      s.emit(\"leaveSeance\", { seanceId: Number(seanceId) });\r\n      s.disconnect();\r\n    };\r\n  }, [seanceId]);\r\n\r\n  // Scroll to bottom\r\n  useEffect(() => {\r\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [sessionChatMessages, seanceChatMessages, selectedTab, open]);\r\n\r\n  // Tab switch resets input\r\n  useEffect(() => {\r\n    setNewMsg(\"\");\r\n    setNewFile(null);\r\n    setShowEmoji(false);\r\n  }, [selectedTab, session2Id, seanceId]);\r\n\r\n  // Emoji\r\n  const handleEmoji = (e) => {\r\n    setNewMsg((prev) => prev + e.emoji);\r\n    setShowEmoji(false);\r\n  };\r\n\r\n  // Send message (text/file) for current tab\r\n  const handleChatSend = async () => {\r\n    let socket;\r\n    if (selectedTab === \"session\") socket = sessionSocketRef.current;\r\n    else if (selectedTab === \"seance\") socket = seanceSocketRef.current;\r\n    else if (selectedTab === \"general\") socket = generalSocketRef.current;\r\n    if (!socket) return;\r\n\r\n    // File message\r\n    if (newFile) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", newFile);\r\n\r\n      let uploadUrl = \"\";\r\n      if (selectedTab === \"general\") {\r\n        uploadUrl = \"http://localhost:8000/general-chat-messages/upload-chat\";\r\n      } else if (selectedTab === \"session\") {\r\n        formData.append(\"session2Id\", session2Id);\r\n        uploadUrl = \"http://localhost:8000/session2-chat-messages/upload-chat\";\r\n      } else if (selectedTab === \"seance\") {\r\n        formData.append(\"seanceId\", seanceId);\r\n        uploadUrl = \"http://localhost:8000/chat-messages/upload-chat\";\r\n      }\r\n      try {\r\n        const res = await fetch(uploadUrl, {\r\n          method: \"POST\",\r\n          body: formData,\r\n        });\r\n        const fileData = await res.json();\r\n        if (selectedTab === \"general\") {\r\n          socket.emit(\"sendGeneralMessage\", {\r\n            content: fileData.fileUrl,\r\n            type: fileData.fileType || \"file\",\r\n            senderId: user.id,\r\n          });\r\n        } else if (selectedTab === \"session\") {\r\n          socket.emit(\"sendSession2Message\", {\r\n            content: fileData.fileUrl,\r\n            type: fileData.fileType || \"file\",\r\n            session2Id: Number(session2Id),\r\n            senderId: user.id,\r\n          });\r\n        } else {\r\n          socket.emit(\"sendSeanceMessage\", {\r\n            content: fileData.fileUrl,\r\n            type: fileData.fileType || \"file\",\r\n            seanceId: Number(seanceId),\r\n            senderId: user.id,\r\n          });\r\n        }\r\n      } catch {\r\n        alert(\"Erreur upload fichier\");\r\n      }\r\n      setNewFile(null);\r\n      if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n      return;\r\n    }\r\n    // Text message\r\n    if (newMsg.trim()) {\r\n      if (selectedTab === \"general\") {\r\n        socket.emit(\"sendGeneralMessage\", {\r\n          content: newMsg,\r\n          type: \"text\",\r\n          senderId: user.id,\r\n        });\r\n      } else if (selectedTab === \"session\") {\r\n        socket.emit(\"sendSession2Message\", {\r\n          content: newMsg,\r\n          type: \"text\",\r\n          session2Id: Number(session2Id),\r\n          senderId: user.id,\r\n        });\r\n      } else {\r\n        socket.emit(\"sendSeanceMessage\", {\r\n          content: newMsg,\r\n          type: \"text\",\r\n          seanceId: Number(seanceId),\r\n          senderId: user.id,\r\n        });\r\n      }\r\n      setNewMsg(\"\");\r\n    }\r\n  };\r\n\r\n\r\n  // Delete message\r\n  const handleDeleteMsg = async (msgId) => {\r\n    if (selectedTab === \"session\") {\r\n      await fetch(`http://localhost:8000/session2-chat-messages/${msgId}`, {\r\n        method: \"DELETE\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ userId: user.id }),\r\n      });\r\n      setSessionChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    } else if (selectedTab === \"seance\") {\r\n      await fetch(`http://localhost:8000/chat-messages/${msgId}`, {\r\n        method: \"DELETE\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ userId: user.id }),\r\n      });\r\n      setSeanceChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    } else if (selectedTab === \"general\") {\r\n      await fetch(`http://localhost:8000/general-chat-messages/${msgId}`, {\r\n        method: \"DELETE\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ userId: user.id }),\r\n      });\r\n      setGeneralChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    }\r\n  };\r\n\r\n\r\n  // --- UI ---\r\n  return (\r\n    <>\r\n      {/* Floating Button */}\r\n      <button\r\n        onClick={() => setOpen(!open)}\r\n        style={{\r\n          position: \"fixed\",\r\n          bottom: \"104px\",\r\n          right: \"24px\",\r\n          width: \"64px\",\r\n          height: \"64px\",\r\n          borderRadius: \"50%\",\r\n          background: open ? \"#fff\" : \"#d32f2f\",\r\n          color: open ? \"#d32f2f\" : \"#fff\",\r\n          fontSize: \"28px\",\r\n          boxShadow: \"0 4px 24px rgba(0, 0, 0, 0.22)\",\r\n          border: open ? \"3px solid #d32f2f\" : \"none\",\r\n          zIndex: 2000,\r\n          cursor: \"pointer\",\r\n          transition: \"all 0.18s ease-in-out\",\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          justifyContent: \"center\",\r\n          outline: \"none\"\r\n        }}\r\n        aria-label={open ? \"Close chat\" : \"Open chat\"}\r\n        onMouseOver={(e) => {\r\n          e.currentTarget.style.background = open ? \"#fbeaec\" : \"#e53935\";\r\n        }}\r\n        onMouseOut={(e) => {\r\n          e.currentTarget.style.background = open ? \"#fff\" : \"#d32f2f\";\r\n        }}\r\n        onFocus={(e) => {\r\n          e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(211, 47, 47, 0.4)\";\r\n        }}\r\n        onBlur={(e) => {\r\n          e.currentTarget.style.boxShadow = \"0 4px 24px rgba(0, 0, 0, 0.22)\";\r\n        }}\r\n      >\r\n        {open ? \"✕\" : \"💬\"}\r\n      </button>\r\n      {/* Tooltip au survol */}\r\n      {showTooltip && (\r\n        <div style={{\r\n          position: \"fixed\",\r\n          bottom: 166,\r\n          right: 24,\r\n          fontSize: \"12px\",\r\n          color: \"white\",\r\n          background: \"rgba(0,0,0,0.8)\",\r\n          padding: \"6px 12px\",\r\n          borderRadius: \"6px\",\r\n          whiteSpace: \"nowrap\",\r\n          zIndex: 2001\r\n        }}>\r\n          Chat de session\r\n        </div>\r\n      )}\r\n\r\n      {/* Popup */}\r\n      {open && (\r\n        <Paper\r\n          sx={{\r\n            position: \"fixed\",\r\n            bottom: 180,\r\n            right: 32,\r\n            width: 410,\r\n            maxHeight: \"74vh\",\r\n            borderRadius: 4,\r\n            boxShadow: 10,\r\n            zIndex: 2100,\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            p: 0,\r\n            overflow: \"hidden\",\r\n            background: \"#f9f9fa\",\r\n          }}\r\n        >\r\n          {/* Tabs */}\r\n          <Box\r\n            display=\"flex\"\r\n            alignItems=\"center\"\r\n            bgcolor=\"#fff\"\r\n            px={2}\r\n            pt={1.5}\r\n            pb={0.5}\r\n            borderBottom=\"1px solid #e8e8e8\"\r\n          >\r\n            <Button\r\n              variant={selectedTab === \"session\" ? \"contained\" : \"text\"}\r\n              onClick={() => setSelectedTab(\"session\")}\r\n              color=\"error\"\r\n              size=\"small\"\r\n              sx={{\r\n                borderRadius: 3,\r\n                fontWeight: 600,\r\n                boxShadow: selectedTab === \"session\" ? 3 : 0,\r\n                mx: 1,\r\n                px: 2\r\n              }}\r\n            >\r\n              Session Chat\r\n            </Button>\r\n            <Button\r\n              variant={selectedTab === \"seance\" ? \"contained\" : \"text\"}\r\n              onClick={() => setSelectedTab(\"seance\")}\r\n              color=\"primary\"\r\n              size=\"small\"\r\n              disabled={!seanceId}\r\n              sx={{\r\n                borderRadius: 3,\r\n                fontWeight: 600,\r\n                boxShadow: selectedTab === \"seance\" ? 3 : 0,\r\n                mx: 1,\r\n                px: 2\r\n              }}\r\n            >\r\n              Séance Chat\r\n            </Button>\r\n            <Button\r\n              variant={selectedTab === \"general\" ? \"contained\" : \"text\"}\r\n              onClick={() => setSelectedTab(\"general\")}\r\n              color=\"secondary\"\r\n              size=\"small\"\r\n              sx={{\r\n                borderRadius: 3,\r\n                fontWeight: 600,\r\n                boxShadow: selectedTab === \"general\" ? 3 : 0,\r\n                mx: 1,\r\n                px: 2\r\n              }}\r\n            >\r\n              Chat Général\r\n            </Button>\r\n\r\n          </Box>\r\n          {/* Session selector */}\r\n          {session2s.length > 1 && (\r\n            <Box bgcolor=\"#f6f6fc\" px={2} py={1.5}>\r\n              <select\r\n                value={session2Id}\r\n                onChange={e => setSession2Id(Number(e.target.value))}\r\n                style={{\r\n                  width: \"100%\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: 8,\r\n                  border: \"1px solid #ccc\",\r\n                  fontWeight: 500\r\n                }}\r\n              >\r\n                {session2s.map(s => (\r\n                  <option value={s.id} key={s.id}>{s.name}</option>\r\n                ))}\r\n              </select>\r\n            </Box>\r\n          )}\r\n          {/* Seance selector */}\r\n          {selectedTab === \"seance\" && seances.length > 1 && (\r\n            <Box bgcolor=\"#f6f6fc\" px={2} py={1.5}>\r\n              <select\r\n                value={seanceId}\r\n                onChange={e => setSeanceId(Number(e.target.value))}\r\n                style={{\r\n                  width: \"100%\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: 8,\r\n                  border: \"1px solid #ccc\",\r\n                  fontWeight: 500\r\n                }}\r\n              >\r\n                {seances.map(s => (\r\n                  <option value={s.id} key={s.id}>{s.title || `Séance ${s.id}`}</option>\r\n                ))}\r\n              </select>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Chat Messages */}\r\n          <Box\r\n            sx={{\r\n              p: 2,\r\n              pt: 1.5,\r\n              flex: 1,\r\n              overflowY: \"auto\",\r\n              background: \"#f7f7fa\",\r\n              borderBottom: \"1px solid #eee\",\r\n              minHeight: 200,\r\n              scrollbarWidth: \"thin\",\r\n              \"&::-webkit-scrollbar\": {\r\n                width: \"7px\",\r\n                background: \"#eaeaea\",\r\n                borderRadius: 5\r\n              },\r\n              \"&::-webkit-scrollbar-thumb\": {\r\n                background: \"#e1e1e1\",\r\n                borderRadius: 5\r\n              }\r\n            }}\r\n          >\r\n            <Stack spacing={1}>\r\n              {(\r\n                selectedTab === \"session\"\r\n                  ? sessionChatMessages\r\n                  : selectedTab === \"seance\"\r\n                    ? seanceChatMessages\r\n                    : generalChatMessages\r\n              ).map((msg, i) => (\r\n                <Box\r\n                  key={msg.id || i}\r\n                  sx={{\r\n                    display: \"flex\",\r\n                    alignItems: \"flex-start\",\r\n                    gap: 1.5,\r\n                    bgcolor: msg.sender?.id === user.id ? \"#fff8f8\" : \"#fff\",\r\n                    borderRadius: 3,\r\n                    boxShadow: msg.sender?.id === user.id ? \"0 1px 8px #ffe0e0\" : \"0 1px 8px #e2e2ef0c\",\r\n                    border: \"1px solid #f2f2f3\",\r\n                    px: 1.5,\r\n                    py: 1,\r\n                    mr: msg.sender?.id === user.id ? 0 : \"auto\",\r\n                    ml: msg.sender?.id === user.id ? \"auto\" : 0,\r\n                    maxWidth: \"85%\",\r\n                  }}\r\n                >\r\n                  {/* Avatar */}\r\n                  {msg.sender?.profilePic\r\n                    ? (\r\n                      <img\r\n                        src={\r\n                          msg.sender?.profilePic?.startsWith('http')\r\n                            ? msg.sender.profilePic\r\n                            : `http://localhost:8000${msg.sender?.profilePic || '/profile-pics/default.png'}`\r\n                        }\r\n                        alt={msg.sender?.name}\r\n                        style={{ width: 32, height: 32, borderRadius: \"50%\", marginTop: 2 }}\r\n                      />\r\n                    ) : (\r\n                      <Avatar sx={{ width: 32, height: 32, bgcolor: \"#d5dde9\", mt: 0.25 }}>\r\n                        {msg.sender?.name?.[0]?.toUpperCase() || \"?\"}\r\n                      </Avatar>\r\n                    )\r\n                  }\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box display=\"flex\" alignItems=\"center\">\r\n                      <Typography variant=\"subtitle2\" fontWeight=\"bold\" color=\"primary\" sx={{ fontSize: 15 }}>\r\n                        {msg.sender?.name || \"Anonyme\"}\r\n                      </Typography>\r\n                      <Typography\r\n                        sx={{\r\n                          color: \"#aaa\",\r\n                          fontSize: 11.5,\r\n                          fontWeight: 500,\r\n                          ml: 1,\r\n                          mt: 0.3\r\n                        }}\r\n                      >\r\n                        {msg.sender?.role ? \"· \" + msg.sender.role : \"\"}\r\n                        {msg.createdAt && (\r\n                          <span style={{ marginLeft: 8 }}>\r\n                            {new Date(msg.createdAt).toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" })}\r\n                          </span>\r\n                        )}\r\n                      </Typography>\r\n                      {msg.sender?.id === user.id && (\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={() => handleDeleteMsg(msg.id)}\r\n                          color=\"error\"\r\n                          sx={{ ml: \"auto\" }}\r\n                        >\r\n                          <DeleteIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      )}\r\n                    </Box>\r\n                    {/* Message Content */}\r\n                    <Box mt={0.5}>\r\n                      {msg.type === \"text\" && (\r\n                        <Typography sx={{ fontSize: 15, color: \"#222\" }}>\r\n                          {msg.content}\r\n                        </Typography>\r\n                      )}\r\n                      {msg.type === \"image\" && (\r\n                        <img src={msg.content} alt=\"img\" style={{ maxWidth: 170, borderRadius: 7, marginTop: 4 }} />\r\n                      )}\r\n                      {msg.type === \"audio\" && (\r\n                        <audio controls src={msg.content} style={{ maxWidth: 150, marginTop: 4 }} />\r\n                      )}\r\n                      {msg.type === \"video\" && (\r\n                        <video controls src={msg.content} style={{ maxWidth: 160, borderRadius: 7, marginTop: 4 }} />\r\n                      )}\r\n                      {msg.type === \"file\" && (\r\n                        <a href={msg.content} target=\"_blank\" rel=\"noopener noreferrer\" style={{ display: \"block\", marginTop: 4, color: \"#0072ff\" }}>\r\n                          📎 {msg.content.split(\"/\").pop()}\r\n                        </a>\r\n                      )}\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              ))}\r\n              <div ref={chatBottomRef} />\r\n            </Stack>\r\n          </Box>\r\n\r\n          {/* Input */}\r\n          <Box sx={{\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 1,\r\n            background: \"#fff\",\r\n            borderTop: \"1px solid #e8e8e8\"\r\n          }}>\r\n            <TextField\r\n              fullWidth\r\n              value={newMsg}\r\n              size=\"small\"\r\n              placeholder=\"Écris un message…\"\r\n              onChange={e => setNewMsg(e.target.value)}\r\n              onKeyDown={e => e.key === \"Enter\" && handleChatSend()}\r\n              sx={{ background: \"#f8f8f8\", borderRadius: 2 }}\r\n              inputProps={{ style: { fontSize: 15 } }}\r\n            />\r\n            <IconButton onClick={() => setShowEmoji(v => !v)}>\r\n              <span role=\"img\" aria-label=\"emoji\">😀</span>\r\n            </IconButton>\r\n            <IconButton component=\"label\" color={newFile ? \"success\" : \"primary\"}>\r\n              <AddPhotoAlternateIcon />\r\n              <input\r\n                hidden\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\"image/*,video/*,audio/*,application/pdf\"\r\n                onChange={e => setNewFile(e.target.files[0])}\r\n              />\r\n            </IconButton>\r\n            <Button onClick={handleChatSend} variant=\"contained\" color={selectedTab === \"session\" ? \"error\" : \"primary\"} disabled={!newMsg.trim() && !newFile} sx={{ px: 2, fontWeight: 600 }}>\r\n              Envoyer\r\n            </Button>\r\n          </Box>\r\n          {showEmoji && (\r\n            <Box sx={{ position: \"absolute\", bottom: 90, right: 30, zIndex: 11 }}>\r\n              <EmojiPicker onEmojiClick={handleEmoji} autoFocusSearch={false} />\r\n            </Box>\r\n          )}\r\n          {newFile && (\r\n            <Typography color=\"primary\" fontSize={13} ml={2} mt={0.5}>\r\n              Fichier prêt à envoyer : <strong>{newFile.name}</strong>\r\n            </Typography>\r\n          )}\r\n        </Paper>\r\n      )}\r\n    </>\r\n  );\r\n\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,QAC/D,eAAe;AACtB,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,EAAE,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGlC,MAAMC,UAAU,GAAG,uBAAuB;AAE1C,eAAe,SAASC,uBAAuBA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAExD;;EAGA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAMwB,gBAAgB,GAAGvB,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,SAAS,CAAC;;EAEzD;EACA,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM6C,aAAa,GAAG5C,MAAM,CAAC,CAAC;EAC9B,MAAM6C,YAAY,GAAG7C,MAAM,CAAC,CAAC;EAC7B,MAAM8C,gBAAgB,GAAG9C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM+C,eAAe,GAAG/C,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM,CAACgD,IAAI,EAAEC,OAAO,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvCD,SAAS,CAAC,MAAM;IACd,IAAI8C,aAAa,CAACM,OAAO,EAAEN,aAAa,CAACM,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACzF,CAAC,EAAE,CAAClB,mBAAmB,EAAEE,kBAAkB,EAAEf,mBAAmB,EAAEG,WAAW,EAAEwB,IAAI,CAAC,CAAC;;EAErF;EACAlD,SAAS,CAAC,MAAM;IACd;IACA,IAAIyB,gBAAgB,CAAC2B,OAAO,EAAE;MAC5B3B,gBAAgB,CAAC2B,OAAO,CAACG,UAAU,CAAC,CAAC;IACvC;IACA,MAAMC,CAAC,GAAG1C,EAAE,CAAC,GAAGK,UAAU,eAAe,EAAE;MAAEsC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACzEhC,gBAAgB,CAAC2B,OAAO,GAAGI,CAAC;;IAE5B;IACAA,CAAC,CAACE,IAAI,CAAC,sBAAsB,CAAC;IAC9BF,CAAC,CAACG,EAAE,CAAC,iBAAiB,EAAEC,IAAI,IAAIpC,sBAAsB,CAACoC,IAAI,CAAC,CAAC;IAC7DJ,CAAC,CAACG,EAAE,CAAC,mBAAmB,EAAEE,GAAG,IAC3BrC,sBAAsB,CAACsC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAC/C,CAAC;IACDL,CAAC,CAACG,EAAE,CAAC,sBAAsB,EAAE,CAAC;MAAEI;IAAG,CAAC,KAClCvC,sBAAsB,CAACsC,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKA,EAAE,CAAC,CAC9D,CAAC;IACD,OAAO,MAAMP,CAAC,CAACD,UAAU,CAAC,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAvD,SAAS,CAAC,MAAM;IACd,IAAI,EAACqB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0C,EAAE,GAAE;IACfG,KAAK,CAAC,+BAA+B7C,IAAI,CAAC0C,EAAE,YAAY,CAAC,CACtDI,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MAAA,IAAAC,cAAA,EAAAC,UAAA;MACd;MACA,MAAMC,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAAClB,CAAC;QAAA,IAAAmB,UAAA;QAAA,OACzBnB,CAAC,CAACoB,QAAQ,GACN;UAAEb,EAAE,EAAEP,CAAC,CAACoB,QAAQ,CAACb,EAAE;UAAEc,IAAI,EAAErB,CAAC,CAACoB,QAAQ,CAACC;QAAK,CAAC,GAC5CrB,CAAC,CAAC1B,UAAU,GACV;UAAEiC,EAAE,EAAEP,CAAC,CAAC1B,UAAU;UAAE+C,IAAI,EAAE,EAAAF,UAAA,GAAAnB,CAAC,CAACoB,QAAQ,cAAAD,UAAA,uBAAVA,UAAA,CAAYE,IAAI,KAAI;QAAG,CAAC,GAClDrB,CAAC;MAAA,CACT,CAAC;MACD3B,YAAY,CAAC4C,QAAQ,CAAC;MACtB1C,aAAa,EAAAwC,cAAA,IAAAC,UAAA,GAACC,QAAQ,CAAC,CAAC,CAAC,cAAAD,UAAA,uBAAXA,UAAA,CAAaT,EAAE,cAAAQ,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;IACxC,CAAC,CAAC;EACN,CAAC,EAAE,CAAClD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,EAAE,CAAC,CAAC;;EAEd;EACA/D,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,UAAU,EAAE;MACfG,UAAU,CAAC,EAAE,CAAC;MACdE,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IACA+B,KAAK,CAAC,kDAAkDpC,UAAU,EAAE,CAAC,CAClEqC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MAAA,IAAAQ,UAAA,EAAAC,MAAA;MACZ9C,UAAU,CAACqC,IAAI,CAAC;MAChBnC,WAAW,EAAA2C,UAAA,IAAAC,MAAA,GAACT,IAAI,CAAC,CAAC,CAAC,cAAAS,MAAA,uBAAPA,MAAA,CAAShB,EAAE,cAAAe,UAAA,cAAAA,UAAA,GAAI,IAAI,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;EACN,CAAC,EAAE,CAAChD,UAAU,CAAC,CAAC;;EAEhB;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI8B,UAAU,EAAE;MACdoC,KAAK,CAAC,gDAAgDpC,UAAU,EAAE,CAAC,CAChEqC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACP,IAAI,IAAIvB,sBAAsB,CAACuB,IAAI,CAAC,CAAC;IAC/C;IACA,IAAI1B,QAAQ,EAAE;MACZgC,KAAK,CAAC,uCAAuChC,QAAQ,EAAE,CAAC,CACrDiC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACP,IAAI,IAAIrB,qBAAqB,CAACqB,IAAI,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE,CAAC9B,UAAU,EAAEI,QAAQ,CAAC,CAAC;;EAE1B;EACAlC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8B,UAAU,EAAE;IACjB,IAAIkB,gBAAgB,CAACI,OAAO,EAAE;MAC5BJ,gBAAgB,CAACI,OAAO,CAACM,IAAI,CAAC,eAAe,EAAE;QAAE5B,UAAU,EAAEkD,MAAM,CAAClD,UAAU;MAAE,CAAC,CAAC;MAClFkB,gBAAgB,CAACI,OAAO,CAACG,UAAU,CAAC,CAAC;IACvC;IACA,MAAMC,CAAC,GAAG1C,EAAE,CAACK,UAAU,EAAE;MAAEsC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACvDT,gBAAgB,CAACI,OAAO,GAAGI,CAAC;IAC5BA,CAAC,CAACE,IAAI,CAAC,cAAc,EAAE;MAAE5B,UAAU,EAAEkD,MAAM,CAAClD,UAAU;IAAE,CAAC,CAAC;IAC1D0B,CAAC,CAACG,EAAE,CAAC,oBAAoB,EAAEE,GAAG,IAAIxB,sBAAsB,CAACyB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC,CAAC;IACjFL,CAAC,CAACG,EAAE,CAAC,uBAAuB,EAAEsB,OAAO,IAAI5C,sBAAsB,CAACyB,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKkB,OAAO,CAAClB,EAAE,CAAC,CAAC,CAAC;IAC/GP,CAAC,CAACG,EAAE,CAAC,uBAAuB,EAAE,MAAMtB,sBAAsB,CAAC,EAAE,CAAC,CAAC;IAC/D,OAAO,MAAM;MACXmB,CAAC,CAACE,IAAI,CAAC,eAAe,EAAE;QAAE5B,UAAU,EAAEkD,MAAM,CAAClD,UAAU;MAAE,CAAC,CAAC;MAC3D0B,CAAC,CAACD,UAAU,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACzB,UAAU,CAAC,CAAC;;EAEhB;EACA9B,SAAS,CAAC,MAAM;IACd,IAAI,CAACkC,QAAQ,EAAE;IACf,IAAIe,eAAe,CAACG,OAAO,EAAE;MAC3BH,eAAe,CAACG,OAAO,CAACM,IAAI,CAAC,aAAa,EAAE;QAAExB,QAAQ,EAAE8C,MAAM,CAAC9C,QAAQ;MAAE,CAAC,CAAC;MAC3Ee,eAAe,CAACG,OAAO,CAACG,UAAU,CAAC,CAAC;IACtC;IACA,MAAMC,CAAC,GAAG1C,EAAE,CAACK,UAAU,EAAE;MAAEsC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACvDR,eAAe,CAACG,OAAO,GAAGI,CAAC;IAC3BA,CAAC,CAACE,IAAI,CAAC,YAAY,EAAE;MAAExB,QAAQ,EAAE8C,MAAM,CAAC9C,QAAQ;IAAE,CAAC,CAAC;IACpDsB,CAAC,CAACG,EAAE,CAAC,kBAAkB,EAAEE,GAAG,IAAItB,qBAAqB,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC,CAAC;IAC9EL,CAAC,CAACG,EAAE,CAAC,qBAAqB,EAAEsB,OAAO,IAAI1C,qBAAqB,CAACuB,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKkB,OAAO,CAAClB,EAAE,CAAC,CAAC,CAAC;IAC5GP,CAAC,CAACG,EAAE,CAAC,qBAAqB,EAAE,MAAMpB,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC5D,OAAO,MAAM;MACXiB,CAAC,CAACE,IAAI,CAAC,aAAa,EAAE;QAAExB,QAAQ,EAAE8C,MAAM,CAAC9C,QAAQ;MAAE,CAAC,CAAC;MACrDsB,CAAC,CAACD,UAAU,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACrB,QAAQ,CAAC,CAAC;;EAEd;EACAlC,SAAS,CAAC,MAAM;IACd,IAAI8C,aAAa,CAACM,OAAO,EAAEN,aAAa,CAACM,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACzF,CAAC,EAAE,CAAClB,mBAAmB,EAAEE,kBAAkB,EAAEZ,WAAW,EAAEwB,IAAI,CAAC,CAAC;;EAEhE;EACAlD,SAAS,CAAC,MAAM;IACdyC,SAAS,CAAC,EAAE,CAAC;IACbE,UAAU,CAAC,IAAI,CAAC;IAChBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,CAACnB,WAAW,EAAEI,UAAU,EAAEI,QAAQ,CAAC,CAAC;;EAEvC;EACA,MAAMgD,WAAW,GAAIC,CAAC,IAAK;IACzB1C,SAAS,CAAEqB,IAAI,IAAKA,IAAI,GAAGqB,CAAC,CAACC,KAAK,CAAC;IACnCvC,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMwC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAIC,MAAM;IACV,IAAI5D,WAAW,KAAK,SAAS,EAAE4D,MAAM,GAAGtC,gBAAgB,CAACI,OAAO,CAAC,KAC5D,IAAI1B,WAAW,KAAK,QAAQ,EAAE4D,MAAM,GAAGrC,eAAe,CAACG,OAAO,CAAC,KAC/D,IAAI1B,WAAW,KAAK,SAAS,EAAE4D,MAAM,GAAG7D,gBAAgB,CAAC2B,OAAO;IACrE,IAAI,CAACkC,MAAM,EAAE;;IAEb;IACA,IAAI5C,OAAO,EAAE;MACX,MAAM6C,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE/C,OAAO,CAAC;MAEhC,IAAIgD,SAAS,GAAG,EAAE;MAClB,IAAIhE,WAAW,KAAK,SAAS,EAAE;QAC7BgE,SAAS,GAAG,yDAAyD;MACvE,CAAC,MAAM,IAAIhE,WAAW,KAAK,SAAS,EAAE;QACpC6D,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE3D,UAAU,CAAC;QACzC4D,SAAS,GAAG,0DAA0D;MACxE,CAAC,MAAM,IAAIhE,WAAW,KAAK,QAAQ,EAAE;QACnC6D,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEvD,QAAQ,CAAC;QACrCwD,SAAS,GAAG,iDAAiD;MAC/D;MACA,IAAI;QACF,MAAMtB,GAAG,GAAG,MAAMF,KAAK,CAACwB,SAAS,EAAE;UACjCC,MAAM,EAAE,MAAM;UACdC,IAAI,EAAEL;QACR,CAAC,CAAC;QACF,MAAMM,QAAQ,GAAG,MAAMzB,GAAG,CAACC,IAAI,CAAC,CAAC;QACjC,IAAI3C,WAAW,KAAK,SAAS,EAAE;UAC7B4D,MAAM,CAAC5B,IAAI,CAAC,oBAAoB,EAAE;YAChCoC,OAAO,EAAED,QAAQ,CAACE,OAAO;YACzBC,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAI,MAAM;YACjCC,QAAQ,EAAE7E,IAAI,CAAC0C;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIrC,WAAW,KAAK,SAAS,EAAE;UACpC4D,MAAM,CAAC5B,IAAI,CAAC,qBAAqB,EAAE;YACjCoC,OAAO,EAAED,QAAQ,CAACE,OAAO;YACzBC,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAI,MAAM;YACjCnE,UAAU,EAAEkD,MAAM,CAAClD,UAAU,CAAC;YAC9BoE,QAAQ,EAAE7E,IAAI,CAAC0C;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLuB,MAAM,CAAC5B,IAAI,CAAC,mBAAmB,EAAE;YAC/BoC,OAAO,EAAED,QAAQ,CAACE,OAAO;YACzBC,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAI,MAAM;YACjC/D,QAAQ,EAAE8C,MAAM,CAAC9C,QAAQ,CAAC;YAC1BgE,QAAQ,EAAE7E,IAAI,CAAC0C;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,MAAM;QACNoC,KAAK,CAAC,uBAAuB,CAAC;MAChC;MACAxD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAII,YAAY,CAACK,OAAO,EAAEL,YAAY,CAACK,OAAO,CAACgD,KAAK,GAAG,EAAE;MACzD;IACF;IACA;IACA,IAAI5D,MAAM,CAAC6D,IAAI,CAAC,CAAC,EAAE;MACjB,IAAI3E,WAAW,KAAK,SAAS,EAAE;QAC7B4D,MAAM,CAAC5B,IAAI,CAAC,oBAAoB,EAAE;UAChCoC,OAAO,EAAEtD,MAAM;UACfwD,IAAI,EAAE,MAAM;UACZE,QAAQ,EAAE7E,IAAI,CAAC0C;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIrC,WAAW,KAAK,SAAS,EAAE;QACpC4D,MAAM,CAAC5B,IAAI,CAAC,qBAAqB,EAAE;UACjCoC,OAAO,EAAEtD,MAAM;UACfwD,IAAI,EAAE,MAAM;UACZlE,UAAU,EAAEkD,MAAM,CAAClD,UAAU,CAAC;UAC9BoE,QAAQ,EAAE7E,IAAI,CAAC0C;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLuB,MAAM,CAAC5B,IAAI,CAAC,mBAAmB,EAAE;UAC/BoC,OAAO,EAAEtD,MAAM;UACfwD,IAAI,EAAE,MAAM;UACZ9D,QAAQ,EAAE8C,MAAM,CAAC9C,QAAQ,CAAC;UAC1BgE,QAAQ,EAAE7E,IAAI,CAAC0C;QACjB,CAAC,CAAC;MACJ;MACAtB,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;;EAGD;EACA,MAAM6D,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAI7E,WAAW,KAAK,SAAS,EAAE;MAC7B,MAAMwC,KAAK,CAAC,gDAAgDqC,KAAK,EAAE,EAAE;QACnEZ,MAAM,EAAE,QAAQ;QAChBa,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CZ,IAAI,EAAEa,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEtF,IAAI,CAAC0C;QAAG,CAAC;MAC1C,CAAC,CAAC;MACF1B,sBAAsB,CAAEyB,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKwC,KAAK,CAAC,CAAC;IACtE,CAAC,MAAM,IAAI7E,WAAW,KAAK,QAAQ,EAAE;MACnC,MAAMwC,KAAK,CAAC,uCAAuCqC,KAAK,EAAE,EAAE;QAC1DZ,MAAM,EAAE,QAAQ;QAChBa,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CZ,IAAI,EAAEa,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEtF,IAAI,CAAC0C;QAAG,CAAC;MAC1C,CAAC,CAAC;MACFxB,qBAAqB,CAAEuB,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKwC,KAAK,CAAC,CAAC;IACrE,CAAC,MAAM,IAAI7E,WAAW,KAAK,SAAS,EAAE;MACpC,MAAMwC,KAAK,CAAC,+CAA+CqC,KAAK,EAAE,EAAE;QAClEZ,MAAM,EAAE,QAAQ;QAChBa,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CZ,IAAI,EAAEa,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEtF,IAAI,CAAC0C;QAAG,CAAC;MAC1C,CAAC,CAAC;MACFvC,sBAAsB,CAAEsC,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKwC,KAAK,CAAC,CAAC;IACtE;EACF,CAAC;;EAGD;EACA,oBACEvF,OAAA,CAAAE,SAAA;IAAA0F,QAAA,gBAEE5F,OAAA;MACE6F,OAAO,EAAEA,CAAA,KAAM1D,OAAO,CAAC,CAACD,IAAI,CAAE;MAC9B4D,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAEnE,IAAI,GAAG,MAAM,GAAG,SAAS;QACrCoE,KAAK,EAAEpE,IAAI,GAAG,SAAS,GAAG,MAAM;QAChCqE,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAEvE,IAAI,GAAG,mBAAmB,GAAG,MAAM;QAC3CwE,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,uBAAuB;QACnCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,OAAO,EAAE;MACX,CAAE;MACF,cAAY9E,IAAI,GAAG,YAAY,GAAG,WAAY;MAC9C+E,WAAW,EAAG9C,CAAC,IAAK;QAClBA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACO,UAAU,GAAGnE,IAAI,GAAG,SAAS,GAAG,SAAS;MACjE,CAAE;MACFiF,UAAU,EAAGhD,CAAC,IAAK;QACjBA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACO,UAAU,GAAGnE,IAAI,GAAG,MAAM,GAAG,SAAS;MAC9D,CAAE;MACFkF,OAAO,EAAGjD,CAAC,IAAK;QACdA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACU,SAAS,GAAG,kCAAkC;MACtE,CAAE;MACFa,MAAM,EAAGlD,CAAC,IAAK;QACbA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACU,SAAS,GAAG,gCAAgC;MACpE,CAAE;MAAAZ,QAAA,EAED1D,IAAI,GAAG,GAAG,GAAG;IAAI;MAAAoF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,EAERC,WAAW,iBACV1H,OAAA;MAAK8F,KAAK,EAAE;QACVC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,GAAG;QACXC,KAAK,EAAE,EAAE;QACTM,QAAQ,EAAE,MAAM;QAChBD,KAAK,EAAE,OAAO;QACdD,UAAU,EAAE,iBAAiB;QAC7BsB,OAAO,EAAE,UAAU;QACnBvB,YAAY,EAAE,KAAK;QACnBwB,UAAU,EAAE,QAAQ;QACpBlB,MAAM,EAAE;MACV,CAAE;MAAAd,QAAA,EAAC;IAEH;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,EAGAvF,IAAI,iBACHlC,OAAA,CAACZ,KAAK;MACJyI,EAAE,EAAE;QACF9B,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,GAAG;QACXC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,GAAG;QACV4B,SAAS,EAAE,MAAM;QACjB1B,YAAY,EAAE,CAAC;QACfI,SAAS,EAAE,EAAE;QACbE,MAAM,EAAE,IAAI;QACZG,OAAO,EAAE,MAAM;QACfkB,aAAa,EAAE,QAAQ;QACvBC,CAAC,EAAE,CAAC;QACJC,QAAQ,EAAE,QAAQ;QAClB5B,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,gBAGF5F,OAAA,CAACb,GAAG;QACF0H,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBoB,OAAO,EAAC,MAAM;QACdC,EAAE,EAAE,CAAE;QACNC,EAAE,EAAE,GAAI;QACRC,EAAE,EAAE,GAAI;QACRC,YAAY,EAAC,mBAAmB;QAAA1C,QAAA,gBAEhC5F,OAAA,CAACP,MAAM;UACL8I,OAAO,EAAE7H,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG,MAAO;UAC1DmF,OAAO,EAAEA,CAAA,KAAMlF,cAAc,CAAC,SAAS,CAAE;UACzC2F,KAAK,EAAC,OAAO;UACbkC,IAAI,EAAC,OAAO;UACZX,EAAE,EAAE;YACFzB,YAAY,EAAE,CAAC;YACfqC,UAAU,EAAE,GAAG;YACfjC,SAAS,EAAE9F,WAAW,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;YAC5CgI,EAAE,EAAE,CAAC;YACLP,EAAE,EAAE;UACN,CAAE;UAAAvC,QAAA,EACH;QAED;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA,CAACP,MAAM;UACL8I,OAAO,EAAE7H,WAAW,KAAK,QAAQ,GAAG,WAAW,GAAG,MAAO;UACzDmF,OAAO,EAAEA,CAAA,KAAMlF,cAAc,CAAC,QAAQ,CAAE;UACxC2F,KAAK,EAAC,SAAS;UACfkC,IAAI,EAAC,OAAO;UACZG,QAAQ,EAAE,CAACzH,QAAS;UACpB2G,EAAE,EAAE;YACFzB,YAAY,EAAE,CAAC;YACfqC,UAAU,EAAE,GAAG;YACfjC,SAAS,EAAE9F,WAAW,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;YAC3CgI,EAAE,EAAE,CAAC;YACLP,EAAE,EAAE;UACN,CAAE;UAAAvC,QAAA,EACH;QAED;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzH,OAAA,CAACP,MAAM;UACL8I,OAAO,EAAE7H,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG,MAAO;UAC1DmF,OAAO,EAAEA,CAAA,KAAMlF,cAAc,CAAC,SAAS,CAAE;UACzC2F,KAAK,EAAC,WAAW;UACjBkC,IAAI,EAAC,OAAO;UACZX,EAAE,EAAE;YACFzB,YAAY,EAAE,CAAC;YACfqC,UAAU,EAAE,GAAG;YACfjC,SAAS,EAAE9F,WAAW,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;YAC5CgI,EAAE,EAAE,CAAC;YACLP,EAAE,EAAE;UACN,CAAE;UAAAvC,QAAA,EACH;QAED;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CAAC,EAEL7G,SAAS,CAACgI,MAAM,GAAG,CAAC,iBACnB5I,OAAA,CAACb,GAAG;QAAC+I,OAAO,EAAC,SAAS;QAACC,EAAE,EAAE,CAAE;QAACU,EAAE,EAAE,GAAI;QAAAjD,QAAA,eACpC5F,OAAA;UACEoF,KAAK,EAAEtE,UAAW;UAClBgI,QAAQ,EAAE3E,CAAC,IAAIpD,aAAa,CAACiD,MAAM,CAACG,CAAC,CAAC4E,MAAM,CAAC3D,KAAK,CAAC,CAAE;UACrDU,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbyB,OAAO,EAAE,UAAU;YACnBvB,YAAY,EAAE,CAAC;YACfK,MAAM,EAAE,gBAAgB;YACxBgC,UAAU,EAAE;UACd,CAAE;UAAA7C,QAAA,EAEDhF,SAAS,CAAC8C,GAAG,CAAClB,CAAC,iBACdxC,OAAA;YAAQoF,KAAK,EAAE5C,CAAC,CAACO,EAAG;YAAA6C,QAAA,EAAapD,CAAC,CAACqB;UAAI,GAAbrB,CAAC,CAACO,EAAE;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEA/G,WAAW,KAAK,QAAQ,IAAIM,OAAO,CAAC4H,MAAM,GAAG,CAAC,iBAC7C5I,OAAA,CAACb,GAAG;QAAC+I,OAAO,EAAC,SAAS;QAACC,EAAE,EAAE,CAAE;QAACU,EAAE,EAAE,GAAI;QAAAjD,QAAA,eACpC5F,OAAA;UACEoF,KAAK,EAAElE,QAAS;UAChB4H,QAAQ,EAAE3E,CAAC,IAAIhD,WAAW,CAAC6C,MAAM,CAACG,CAAC,CAAC4E,MAAM,CAAC3D,KAAK,CAAC,CAAE;UACnDU,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbyB,OAAO,EAAE,UAAU;YACnBvB,YAAY,EAAE,CAAC;YACfK,MAAM,EAAE,gBAAgB;YACxBgC,UAAU,EAAE;UACd,CAAE;UAAA7C,QAAA,EAED5E,OAAO,CAAC0C,GAAG,CAAClB,CAAC,iBACZxC,OAAA;YAAQoF,KAAK,EAAE5C,CAAC,CAACO,EAAG;YAAA6C,QAAA,EAAapD,CAAC,CAACwG,KAAK,IAAI,UAAUxG,CAAC,CAACO,EAAE;UAAE,GAAlCP,CAAC,CAACO,EAAE;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAuC,CACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAGDzH,OAAA,CAACb,GAAG;QACF0I,EAAE,EAAE;UACFG,CAAC,EAAE,CAAC;UACJI,EAAE,EAAE,GAAG;UACPa,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,MAAM;UACjB7C,UAAU,EAAE,SAAS;UACrBiC,YAAY,EAAE,gBAAgB;UAC9Ba,SAAS,EAAE,GAAG;UACdC,cAAc,EAAE,MAAM;UACtB,sBAAsB,EAAE;YACtBlD,KAAK,EAAE,KAAK;YACZG,UAAU,EAAE,SAAS;YACrBD,YAAY,EAAE;UAChB,CAAC;UACD,4BAA4B,EAAE;YAC5BC,UAAU,EAAE,SAAS;YACrBD,YAAY,EAAE;UAChB;QACF,CAAE;QAAAR,QAAA,eAEF5F,OAAA,CAACV,KAAK;UAAC+J,OAAO,EAAE,CAAE;UAAAzD,QAAA,GACf,CACClF,WAAW,KAAK,SAAS,GACrBU,mBAAmB,GACnBV,WAAW,KAAK,QAAQ,GACtBY,kBAAkB,GAClBf,mBAAmB,EACzBmD,GAAG,CAAC,CAACb,GAAG,EAAEyG,CAAC;YAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;YAAA,oBACXrK,OAAA,CAACb,GAAG;cAEF0I,EAAE,EAAE;gBACFhB,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,YAAY;gBACxBwD,GAAG,EAAE,GAAG;gBACRpC,OAAO,EAAE,EAAAqB,WAAA,GAAA1G,GAAG,CAAC0H,MAAM,cAAAhB,WAAA,uBAAVA,WAAA,CAAYxG,EAAE,MAAK1C,IAAI,CAAC0C,EAAE,GAAG,SAAS,GAAG,MAAM;gBACxDqD,YAAY,EAAE,CAAC;gBACfI,SAAS,EAAE,EAAAgD,YAAA,GAAA3G,GAAG,CAAC0H,MAAM,cAAAf,YAAA,uBAAVA,YAAA,CAAYzG,EAAE,MAAK1C,IAAI,CAAC0C,EAAE,GAAG,mBAAmB,GAAG,qBAAqB;gBACnF0D,MAAM,EAAE,mBAAmB;gBAC3B0B,EAAE,EAAE,GAAG;gBACPU,EAAE,EAAE,CAAC;gBACL2B,EAAE,EAAE,EAAAf,YAAA,GAAA5G,GAAG,CAAC0H,MAAM,cAAAd,YAAA,uBAAVA,YAAA,CAAY1G,EAAE,MAAK1C,IAAI,CAAC0C,EAAE,GAAG,CAAC,GAAG,MAAM;gBAC3C0H,EAAE,EAAE,EAAAf,YAAA,GAAA7G,GAAG,CAAC0H,MAAM,cAAAb,YAAA,uBAAVA,YAAA,CAAY3G,EAAE,MAAK1C,IAAI,CAAC0C,EAAE,GAAG,MAAM,GAAG,CAAC;gBAC3C2H,QAAQ,EAAE;cACZ,CAAE;cAAA9E,QAAA,GAGD,CAAA+D,YAAA,GAAA9G,GAAG,CAAC0H,MAAM,cAAAZ,YAAA,eAAVA,YAAA,CAAYgB,UAAU,gBAEnB3K,OAAA;gBACE4K,GAAG,EACD,CAAAhB,YAAA,GAAA/G,GAAG,CAAC0H,MAAM,cAAAX,YAAA,gBAAAC,qBAAA,GAAVD,YAAA,CAAYe,UAAU,cAAAd,qBAAA,eAAtBA,qBAAA,CAAwBgB,UAAU,CAAC,MAAM,CAAC,GACtChI,GAAG,CAAC0H,MAAM,CAACI,UAAU,GACrB,wBAAwB,EAAAb,YAAA,GAAAjH,GAAG,CAAC0H,MAAM,cAAAT,YAAA,uBAAVA,YAAA,CAAYa,UAAU,KAAI,2BAA2B,EAClF;gBACDG,GAAG,GAAAf,YAAA,GAAElH,GAAG,CAAC0H,MAAM,cAAAR,YAAA,uBAAVA,YAAA,CAAYlG,IAAK;gBACtBiC,KAAK,EAAE;kBAAEI,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE,KAAK;kBAAE2E,SAAS,EAAE;gBAAE;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,gBAEFzH,OAAA,CAACN,MAAM;gBAACmI,EAAE,EAAE;kBAAE3B,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAE+B,OAAO,EAAE,SAAS;kBAAE8C,EAAE,EAAE;gBAAK,CAAE;gBAAApF,QAAA,EACjE,EAAAoE,YAAA,GAAAnH,GAAG,CAAC0H,MAAM,cAAAP,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYnG,IAAI,cAAAoG,iBAAA,wBAAAC,kBAAA,GAAhBD,iBAAA,CAAmB,CAAC,CAAC,cAAAC,kBAAA,uBAArBA,kBAAA,CAAuBe,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CACT,eAEHzH,OAAA,CAACb,GAAG;gBAAC0I,EAAE,EAAE;kBAAEoB,IAAI,EAAE;gBAAE,CAAE;gBAAArD,QAAA,gBACnB5F,OAAA,CAACb,GAAG;kBAAC0H,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAAAlB,QAAA,gBACrC5F,OAAA,CAACX,UAAU;oBAACkJ,OAAO,EAAC,WAAW;oBAACE,UAAU,EAAC,MAAM;oBAACnC,KAAK,EAAC,SAAS;oBAACuB,EAAE,EAAE;sBAAEtB,QAAQ,EAAE;oBAAG,CAAE;oBAAAX,QAAA,EACpF,EAAAuE,aAAA,GAAAtH,GAAG,CAAC0H,MAAM,cAAAJ,aAAA,uBAAVA,aAAA,CAAYtG,IAAI,KAAI;kBAAS;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACbzH,OAAA,CAACX,UAAU;oBACTwI,EAAE,EAAE;sBACFvB,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,IAAI;sBACdkC,UAAU,EAAE,GAAG;sBACfgC,EAAE,EAAE,CAAC;sBACLO,EAAE,EAAE;oBACN,CAAE;oBAAApF,QAAA,GAED,CAAAwE,aAAA,GAAAvH,GAAG,CAAC0H,MAAM,cAAAH,aAAA,eAAVA,aAAA,CAAYc,IAAI,GAAG,IAAI,GAAGrI,GAAG,CAAC0H,MAAM,CAACW,IAAI,GAAG,EAAE,EAC9CrI,GAAG,CAACsI,SAAS,iBACZnL,OAAA;sBAAM8F,KAAK,EAAE;wBAAEsF,UAAU,EAAE;sBAAE,CAAE;sBAAAxF,QAAA,EAC5B,IAAIyF,IAAI,CAACxI,GAAG,CAACsI,SAAS,CAAC,CAACG,kBAAkB,CAAC,EAAE,EAAE;wBAAEC,IAAI,EAAE,SAAS;wBAAEC,MAAM,EAAE;sBAAU,CAAC;oBAAC;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,EACZ,EAAA4C,aAAA,GAAAxH,GAAG,CAAC0H,MAAM,cAAAF,aAAA,uBAAVA,aAAA,CAAYtH,EAAE,MAAK1C,IAAI,CAAC0C,EAAE,iBACzB/C,OAAA,CAACR,UAAU;oBACTgJ,IAAI,EAAC,OAAO;oBACZ3C,OAAO,EAAEA,CAAA,KAAMP,eAAe,CAACzC,GAAG,CAACE,EAAE,CAAE;oBACvCuD,KAAK,EAAC,OAAO;oBACbuB,EAAE,EAAE;sBAAE4C,EAAE,EAAE;oBAAO,CAAE;oBAAA7E,QAAA,eAEnB5F,OAAA,CAACJ,UAAU;sBAAC2G,QAAQ,EAAC;oBAAO;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENzH,OAAA,CAACb,GAAG;kBAAC6L,EAAE,EAAE,GAAI;kBAAApF,QAAA,GACV/C,GAAG,CAACmC,IAAI,KAAK,MAAM,iBAClBhF,OAAA,CAACX,UAAU;oBAACwI,EAAE,EAAE;sBAAEtB,QAAQ,EAAE,EAAE;sBAAED,KAAK,EAAE;oBAAO,CAAE;oBAAAV,QAAA,EAC7C/C,GAAG,CAACiC;kBAAO;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACb,EACA5E,GAAG,CAACmC,IAAI,KAAK,OAAO,iBACnBhF,OAAA;oBAAK4K,GAAG,EAAE/H,GAAG,CAACiC,OAAQ;oBAACgG,GAAG,EAAC,KAAK;oBAAChF,KAAK,EAAE;sBAAE4E,QAAQ,EAAE,GAAG;sBAAEtE,YAAY,EAAE,CAAC;sBAAE2E,SAAS,EAAE;oBAAE;kBAAE;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC5F,EACA5E,GAAG,CAACmC,IAAI,KAAK,OAAO,iBACnBhF,OAAA;oBAAOyL,QAAQ;oBAACb,GAAG,EAAE/H,GAAG,CAACiC,OAAQ;oBAACgB,KAAK,EAAE;sBAAE4E,QAAQ,EAAE,GAAG;sBAAEK,SAAS,EAAE;oBAAE;kBAAE;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC5E,EACA5E,GAAG,CAACmC,IAAI,KAAK,OAAO,iBACnBhF,OAAA;oBAAOyL,QAAQ;oBAACb,GAAG,EAAE/H,GAAG,CAACiC,OAAQ;oBAACgB,KAAK,EAAE;sBAAE4E,QAAQ,EAAE,GAAG;sBAAEtE,YAAY,EAAE,CAAC;sBAAE2E,SAAS,EAAE;oBAAE;kBAAE;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC7F,EACA5E,GAAG,CAACmC,IAAI,KAAK,MAAM,iBAClBhF,OAAA;oBAAG0L,IAAI,EAAE7I,GAAG,CAACiC,OAAQ;oBAACiE,MAAM,EAAC,QAAQ;oBAAC4C,GAAG,EAAC,qBAAqB;oBAAC7F,KAAK,EAAE;sBAAEe,OAAO,EAAE,OAAO;sBAAEkE,SAAS,EAAE,CAAC;sBAAEzE,KAAK,EAAE;oBAAU,CAAE;oBAAAV,QAAA,GAAC,eACxH,EAAC/C,GAAG,CAACiC,OAAO,CAAC8G,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAxFD5E,GAAG,CAACE,EAAE,IAAIuG,CAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyFb,CAAC;UAAA,CACP,CAAC,eACFzH,OAAA;YAAK8L,GAAG,EAAEhK;UAAc;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNzH,OAAA,CAACb,GAAG;QAAC0I,EAAE,EAAE;UACPG,CAAC,EAAE,CAAC;UACJnB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBwD,GAAG,EAAE,CAAC;UACNjE,UAAU,EAAE,MAAM;UAClB0F,SAAS,EAAE;QACb,CAAE;QAAAnG,QAAA,gBACA5F,OAAA,CAACT,SAAS;UACRyM,SAAS;UACT5G,KAAK,EAAE5D,MAAO;UACdgH,IAAI,EAAC,OAAO;UACZyD,WAAW,EAAC,2BAAmB;UAC/BnD,QAAQ,EAAE3E,CAAC,IAAI1C,SAAS,CAAC0C,CAAC,CAAC4E,MAAM,CAAC3D,KAAK,CAAE;UACzC8G,SAAS,EAAE/H,CAAC,IAAIA,CAAC,CAACgI,GAAG,KAAK,OAAO,IAAI9H,cAAc,CAAC,CAAE;UACtDwD,EAAE,EAAE;YAAExB,UAAU,EAAE,SAAS;YAAED,YAAY,EAAE;UAAE,CAAE;UAC/CgG,UAAU,EAAE;YAAEtG,KAAK,EAAE;cAAES,QAAQ,EAAE;YAAG;UAAE;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACFzH,OAAA,CAACR,UAAU;UAACqG,OAAO,EAAEA,CAAA,KAAMhE,YAAY,CAACwK,CAAC,IAAI,CAACA,CAAC,CAAE;UAAAzG,QAAA,eAC/C5F,OAAA;YAAMkL,IAAI,EAAC,KAAK;YAAC,cAAW,OAAO;YAAAtF,QAAA,EAAC;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACbzH,OAAA,CAACR,UAAU;UAAC8M,SAAS,EAAC,OAAO;UAAChG,KAAK,EAAE5E,OAAO,GAAG,SAAS,GAAG,SAAU;UAAAkE,QAAA,gBACnE5F,OAAA,CAACL,qBAAqB;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzBzH,OAAA;YACEuM,MAAM;YACNT,GAAG,EAAE/J,YAAa;YAClBiD,IAAI,EAAC,MAAM;YACXwH,MAAM,EAAC,yCAAyC;YAChD1D,QAAQ,EAAE3E,CAAC,IAAIxC,UAAU,CAACwC,CAAC,CAAC4E,MAAM,CAAC0D,KAAK,CAAC,CAAC,CAAC;UAAE;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eACbzH,OAAA,CAACP,MAAM;UAACoG,OAAO,EAAExB,cAAe;UAACkE,OAAO,EAAC,WAAW;UAACjC,KAAK,EAAE5F,WAAW,KAAK,SAAS,GAAG,OAAO,GAAG,SAAU;UAACiI,QAAQ,EAAE,CAACnH,MAAM,CAAC6D,IAAI,CAAC,CAAC,IAAI,CAAC3D,OAAQ;UAACmG,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEM,UAAU,EAAE;UAAI,CAAE;UAAA7C,QAAA,EAAC;QAEnL;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACL7F,SAAS,iBACR5B,OAAA,CAACb,GAAG;QAAC0I,EAAE,EAAE;UAAE9B,QAAQ,EAAE,UAAU;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAES,MAAM,EAAE;QAAG,CAAE;QAAAd,QAAA,eACnE5F,OAAA,CAACH,WAAW;UAAC6M,YAAY,EAAExI,WAAY;UAACyI,eAAe,EAAE;QAAM;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACN,EACA/F,OAAO,iBACN1B,OAAA,CAACX,UAAU;QAACiH,KAAK,EAAC,SAAS;QAACC,QAAQ,EAAE,EAAG;QAACkE,EAAE,EAAE,CAAE;QAACO,EAAE,EAAE,GAAI;QAAApF,QAAA,GAAC,iCAC/B,eAAA5F,OAAA;UAAA4F,QAAA,EAASlE,OAAO,CAACmC;QAAI;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR;EAAA,eACD,CAAC;AAGP;AAACnH,EAAA,CAtnBuBF,uBAAuB;AAAAwM,EAAA,GAAvBxM,uBAAuB;AAAA,IAAAwM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}