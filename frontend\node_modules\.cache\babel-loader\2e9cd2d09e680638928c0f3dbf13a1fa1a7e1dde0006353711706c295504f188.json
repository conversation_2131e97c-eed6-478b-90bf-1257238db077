{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\components\\\\Chatbot\\\\Chatbot.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport axios from 'axios';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport { LanguageService } from '../../services/languageService';\nimport QuickSuggestions from './QuickSuggestions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BOT_AVATAR = 'https://cdn-icons-png.flaticon.com/512/4712/4712035.png'; // Example bot avatar\n\nconst Chatbot = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n  const [userLanguage, setUserLanguage] = useState(() => LanguageService.detectBrowserLanguage());\n  const [messages, setMessages] = useState([]);\n  const [uiMessages, setUiMessages] = useState(() => LanguageService.getUIMessages(LanguageService.detectBrowserLanguage()));\n  const [showSuggestions, setShowSuggestions] = useState(true);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [sessionId, setSessionId] = useState(null);\n  const messagesEndRef = useRef(null);\n\n  // Welcome messages in different languages\n  const welcomeMessages = {\n    fr: \"Bonjour! Je suis votre assistant LMS. Comment puis-je vous aider?\",\n    en: \"Hello! I'm your LMS assistant. How can I help you?\",\n    es: \"¡Hola! Soy tu asistente LMS. ¿Cómo puedo ayudarte?\",\n    ar: \"مرحبا! أنا مساعد نظام إدارة التعلم الخاص بك. كيف يمكنني مساعدتك؟\",\n    tn: \"أهلا وسهلا! أنا مساعد نظام التعليم متاعك. كيفاش نجم نعاونك؟\"\n  };\n\n  // Initialize with welcome message and update UI messages\n  useEffect(() => {\n    if (messages.length === 0) {\n      setMessages([{\n        text: welcomeMessages[userLanguage],\n        isBot: true\n      }]);\n    }\n    setUiMessages(LanguageService.getUIMessages(userLanguage));\n  }, [userLanguage, messages.length, welcomeMessages]);\n  const toggleChatbot = () => {\n    setIsOpen(!isOpen);\n  };\n  const handleInputChange = e => {\n    setInputMessage(e.target.value);\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Generate a unique session ID when the component mounts\n  useEffect(() => {\n    if (!sessionId) {\n      const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\n      setSessionId(newSessionId);\n    }\n  }, [sessionId]);\n\n  // Get user info from localStorage\n  const getUserInfo = () => {\n    try {\n      const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');\n      if (storedUser) {\n        const user = JSON.parse(storedUser);\n        // Ensure userId is a number (integer) as expected by the database\n        const userId = user.id ? parseInt(user.id, 10) : null;\n        return {\n          id: isNaN(userId) ? null : userId,\n          email: user.email\n        };\n      }\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n    }\n    return {\n      id: null,\n      email: null\n    };\n  };\n  const handleSuggestionClick = suggestion => {\n    setInputMessage(suggestion);\n    setShowSuggestions(false);\n  };\n  const sendMessage = async e => {\n    e.preventDefault();\n    if (!inputMessage.trim()) return;\n    setShowSuggestions(false);\n    const userMessage = {\n      text: inputMessage,\n      isBot: false\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n    try {\n      const userInfo = getUserInfo();\n      console.log('Sending to chatbot:', {\n        message: inputMessage,\n        sessionId: sessionId,\n        userId: userInfo.id,\n        userLanguage: userLanguage\n      });\n      const response = await axios.post('http://localhost:8000/chatbot/message', {\n        message: inputMessage,\n        sessionId: sessionId,\n        userId: userInfo.id,\n        userLanguage: userLanguage\n      });\n\n      // Update user language if detected\n      if (response.data.detectedLanguage && response.data.detectedLanguage !== userLanguage) {\n        setUserLanguage(response.data.detectedLanguage);\n      }\n      setMessages(prev => [...prev, {\n        text: response.data.response,\n        isBot: true\n      }]);\n    } catch (error) {\n      console.error('Chatbot error:', error);\n      const errorMessages = {\n        fr: \"Désolé, je n'ai pas pu traiter votre demande. Veuillez réessayer plus tard.\",\n        en: \"Sorry, I couldn't process your request. Please try again later.\",\n        es: \"Lo siento, no pude procesar tu solicitud. Por favor, inténtalo de nuevo más tarde.\",\n        ar: \"آسف، لم أتمكن من معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.\"\n      };\n      setMessages(prev => [...prev, {\n        text: errorMessages[userLanguage] || errorMessages.fr,\n        isBot: true\n      }]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      bottom: '24px',\n      right: '24px',\n      zIndex: 3000\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"btn btn-primary shadow-lg d-flex align-items-center justify-content-center\",\n      style: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        fontSize: 28,\n        boxShadow: '0 4px 16px rgba(0,0,0,0.15)',\n        transition: 'background 0.2s'\n      },\n      onClick: toggleChatbot,\n      \"aria-label\": isOpen ? uiMessages.close : uiMessages.open,\n      children: isOpen ? '✕' : /*#__PURE__*/_jsxDEV(\"span\", {\n        role: \"img\",\n        \"aria-label\": \"chat\",\n        children: \"\\uD83D\\uDCAC\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 25\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: 80,\n        right: 0,\n        width: 370,\n        maxWidth: '95vw',\n        height: 520,\n        borderRadius: 22,\n        boxShadow: '0 8px 32px rgba(0,0,0,0.18)',\n        overflow: 'hidden',\n        display: 'flex',\n        flexDirection: 'column',\n        background: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#007bff',\n          color: 'white',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'flex-start',\n          borderTopLeftRadius: 22,\n          borderTopRightRadius: 22,\n          minHeight: 64,\n          height: 64,\n          padding: '0 20px',\n          boxSizing: 'border-box',\n          overflow: 'hidden',\n          gap: 12,\n          flexShrink: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: BOT_AVATAR,\n          alt: \"Bot\",\n          style: {\n            width: 40,\n            height: 40,\n            borderRadius: '50%',\n            background: '#fff',\n            flexShrink: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontWeight: 600,\n            fontSize: 20,\n            lineHeight: '1.2',\n            whiteSpace: 'nowrap',\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          children: uiMessages.chatTitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#f7f8fa',\n          flex: 1,\n          overflow: 'auto',\n          padding: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          direction: LanguageService.getTextDirection(userLanguage)\n        },\n        children: [messages.map((msg, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            marginBottom: '8px',\n            alignItems: msg.isBot ? 'flex-start' : 'flex-end',\n            justifyContent: msg.isBot ? 'flex-start' : 'flex-end'\n          },\n          children: [msg.isBot && /*#__PURE__*/_jsxDEV(\"img\", {\n            src: BOT_AVATAR,\n            alt: \"Bot\",\n            style: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              marginRight: 8,\n              alignSelf: 'flex-end'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: msg.isBot ? 'white' : '#007bff',\n              color: msg.isBot ? '#333' : 'white',\n              borderRadius: msg.isBot ? '18px 18px 18px 4px' : '18px 18px 4px 18px',\n              padding: '10px 16px',\n              maxWidth: '75%',\n              fontSize: 15,\n              boxShadow: msg.isBot ? '0 1px 4px rgba(0,0,0,0.04)' : 'none',\n              marginLeft: msg.isBot ? 0 : 32,\n              marginRight: msg.isBot ? 32 : 0\n            },\n            children: msg.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            marginBottom: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: BOT_AVATAR,\n            alt: \"Bot\",\n            style: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              marginRight: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              color: '#333',\n              borderRadius: '18px 18px 18px 4px',\n              padding: '10px 16px',\n              fontSize: 15,\n              opacity: 0.7\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"spinner-border spinner-border-sm text-secondary me-2\",\n              role: \"status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 19\n            }, this), \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(QuickSuggestions, {\n          language: userLanguage,\n          onSuggestionClick: handleSuggestionClick,\n          isVisible: showSuggestions && messages.length <= 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          padding: '8px',\n          borderBottomLeftRadius: 22,\n          borderBottomRightRadius: 22,\n          flexShrink: 0,\n          borderTop: '1px solid #e0e3e8'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          style: {\n            display: 'flex'\n          },\n          onSubmit: sendMessage,\n          autoComplete: \"off\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-control me-2\",\n            value: inputMessage,\n            onChange: handleInputChange,\n            placeholder: uiMessages.placeholder,\n            disabled: isLoading,\n            style: {\n              borderRadius: 18,\n              fontSize: 15,\n              background: '#f4f6fa',\n              border: '1px solid #e0e3e8'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"btn btn-primary d-flex align-items-center justify-content-center\",\n            disabled: isLoading || !inputMessage.trim(),\n            style: {\n              borderRadius: 18,\n              minWidth: 44,\n              minHeight: 44,\n              fontSize: 20\n            },\n            \"aria-label\": uiMessages.send,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              role: \"img\",\n              \"aria-label\": \"send\",\n              children: \"\\u27A4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 137,\n    columnNumber: 5\n  }, this);\n};\n_s(Chatbot, \"/biEZ3BSy0s3SwpZU84JuXYV9l4=\");\n_c = Chatbot;\nexport default Chatbot;\nvar _c;\n$RefreshReg$(_c, \"Chatbot\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "axios", "LanguageService", "QuickSuggestions", "jsxDEV", "_jsxDEV", "BOT_AVATAR", "<PERSON><PERSON><PERSON>", "_s", "isOpen", "setIsOpen", "userLanguage", "setUserLanguage", "detectBrowserLanguage", "messages", "setMessages", "uiMessages", "setUiMessages", "getUIMessages", "showSuggestions", "setShowSuggestions", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "sessionId", "setSessionId", "messagesEndRef", "welcomeMessages", "fr", "en", "es", "ar", "tn", "length", "text", "isBot", "toggleChatbot", "handleInputChange", "e", "target", "value", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "newSessionId", "Date", "now", "Math", "random", "toString", "substring", "getUserInfo", "storedUser", "localStorage", "getItem", "sessionStorage", "user", "JSON", "parse", "userId", "id", "parseInt", "isNaN", "email", "error", "console", "handleSuggestionClick", "suggestion", "sendMessage", "preventDefault", "trim", "userMessage", "prev", "userInfo", "log", "message", "response", "post", "data", "detectedLanguage", "errorMessages", "style", "position", "bottom", "right", "zIndex", "children", "className", "width", "height", "borderRadius", "fontSize", "boxShadow", "transition", "onClick", "close", "open", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "overflow", "display", "flexDirection", "background", "color", "alignItems", "justifyContent", "borderTopLeftRadius", "borderTopRightRadius", "minHeight", "padding", "boxSizing", "gap", "flexShrink", "src", "alt", "fontWeight", "lineHeight", "whiteSpace", "textOverflow", "chatTitle", "flex", "direction", "getTextDirection", "map", "msg", "index", "marginBottom", "marginRight", "alignSelf", "marginLeft", "opacity", "language", "onSuggestionClick", "isVisible", "ref", "borderBottomLeftRadius", "borderBottomRightRadius", "borderTop", "onSubmit", "autoComplete", "type", "onChange", "placeholder", "disabled", "border", "min<PERSON><PERSON><PERSON>", "send", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/components/Chatbot/Chatbot.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport { LanguageService } from '../../services/languageService';\r\nimport QuickSuggestions from './QuickSuggestions';\r\n\r\nconst BOT_AVATAR = 'https://cdn-icons-png.flaticon.com/512/4712/4712035.png'; // Example bot avatar\r\n\r\nconst Chatbot = () => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [userLanguage, setUserLanguage] = useState(() => LanguageService.detectBrowserLanguage());\r\n  const [messages, setMessages] = useState([]);\r\n  const [uiMessages, setUiMessages] = useState(() => LanguageService.getUIMessages(LanguageService.detectBrowserLanguage()));\r\n  const [showSuggestions, setShowSuggestions] = useState(true);\r\n  const [inputMessage, setInputMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [sessionId, setSessionId] = useState(null);\r\n  const messagesEndRef = useRef(null);\r\n\r\n  // Welcome messages in different languages\r\n  const welcomeMessages = {\r\n    fr: \"Bonjour! Je suis votre assistant LMS. Comment puis-je vous aider?\",\r\n    en: \"Hello! I'm your LMS assistant. How can I help you?\",\r\n    es: \"¡Hola! Soy tu asistente LMS. ¿Cómo puedo ayudarte?\",\r\n    ar: \"مرحبا! أنا مساعد نظام إدارة التعلم الخاص بك. كيف يمكنني مساعدتك؟\",\r\n    tn: \"أهلا وسهلا! أنا مساعد نظام التعليم متاعك. كيفاش نجم نعاونك؟\"\r\n  };\r\n\r\n  // Initialize with welcome message and update UI messages\r\n  useEffect(() => {\r\n    if (messages.length === 0) {\r\n      setMessages([{ text: welcomeMessages[userLanguage], isBot: true }]);\r\n    }\r\n    setUiMessages(LanguageService.getUIMessages(userLanguage));\r\n  }, [userLanguage, messages.length, welcomeMessages]);\r\n\r\n  const toggleChatbot = () => {\r\n    setIsOpen(!isOpen);\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    setInputMessage(e.target.value);\r\n  };\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  // Generate a unique session ID when the component mounts\r\n  useEffect(() => {\r\n    if (!sessionId) {\r\n      const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;\r\n      setSessionId(newSessionId);\r\n    }\r\n  }, [sessionId]);\r\n\r\n  // Get user info from localStorage\r\n  const getUserInfo = () => {\r\n    try {\r\n      const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');\r\n      if (storedUser) {\r\n        const user = JSON.parse(storedUser);\r\n        // Ensure userId is a number (integer) as expected by the database\r\n        const userId = user.id ? parseInt(user.id, 10) : null;\r\n        return {\r\n          id: isNaN(userId) ? null : userId,\r\n          email: user.email\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing user data:', error);\r\n    }\r\n    return { id: null, email: null };\r\n  };\r\n\r\n  const handleSuggestionClick = (suggestion) => {\r\n    setInputMessage(suggestion);\r\n    setShowSuggestions(false);\r\n  };\r\n\r\n  const sendMessage = async (e) => {\r\n    e.preventDefault();\r\n    if (!inputMessage.trim()) return;\r\n\r\n    setShowSuggestions(false);\r\n\r\n    const userMessage = { text: inputMessage, isBot: false };\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputMessage('');\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const userInfo = getUserInfo();\r\n\r\n      console.log('Sending to chatbot:', {\r\n        message: inputMessage,\r\n        sessionId: sessionId,\r\n        userId: userInfo.id,\r\n        userLanguage: userLanguage\r\n      });\r\n\r\n      const response = await axios.post('http://localhost:8000/chatbot/message', {\r\n        message: inputMessage,\r\n        sessionId: sessionId,\r\n        userId: userInfo.id,\r\n        userLanguage: userLanguage\r\n      });\r\n      \r\n      // Update user language if detected\r\n      if (response.data.detectedLanguage && response.data.detectedLanguage !== userLanguage) {\r\n        setUserLanguage(response.data.detectedLanguage);\r\n      }\r\n      \r\n      setMessages(prev => [...prev, { text: response.data.response, isBot: true }]);\r\n    } catch (error) {\r\n      console.error('Chatbot error:', error);\r\n      const errorMessages = {\r\n        fr: \"Désolé, je n'ai pas pu traiter votre demande. Veuillez réessayer plus tard.\",\r\n        en: \"Sorry, I couldn't process your request. Please try again later.\",\r\n        es: \"Lo siento, no pude procesar tu solicitud. Por favor, inténtalo de nuevo más tarde.\",\r\n        ar: \"آسف، لم أتمكن من معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.\"\r\n      };\r\n      setMessages(prev => [...prev, {\r\n        text: errorMessages[userLanguage] || errorMessages.fr,\r\n        isBot: true\r\n      }]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div style={{ position: 'fixed', bottom: '24px', right: '24px', zIndex: 3000 }}>\r\n      {/* Floating Chat Button */}\r\n      <button\r\n        className=\"btn btn-primary shadow-lg d-flex align-items-center justify-content-center\"\r\n        style={{ width: 60, height: 60, borderRadius: '50%', fontSize: 28, boxShadow: '0 4px 16px rgba(0,0,0,0.15)', transition: 'background 0.2s' }}\r\n        onClick={toggleChatbot}\r\n        aria-label={isOpen ? uiMessages.close : uiMessages.open}\r\n      >\r\n        {isOpen ? '✕' : <span role=\"img\" aria-label=\"chat\">💬</span>}\r\n      </button>\r\n\r\n      {/* Chat Window */}\r\n      {isOpen && (\r\n        <div style={{ \r\n          position: 'absolute', \r\n          bottom: 80, \r\n          right: 0, \r\n          width: 370, \r\n          maxWidth: '95vw', \r\n          height: 520, \r\n          borderRadius: 22, \r\n          boxShadow: '0 8px 32px rgba(0,0,0,0.18)', \r\n          overflow: 'hidden', \r\n          display: 'flex', \r\n          flexDirection: 'column',\r\n          background: 'white'\r\n        }}>\r\n          {/* Header */}\r\n          <div style={{\r\n            background: '#007bff',\r\n            color: 'white',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'flex-start',\r\n            borderTopLeftRadius: 22,\r\n            borderTopRightRadius: 22,\r\n            minHeight: 64,\r\n            height: 64,\r\n            padding: '0 20px',\r\n            boxSizing: 'border-box',\r\n            overflow: 'hidden',\r\n            gap: 12,\r\n            flexShrink: 0\r\n          }}>\r\n            <img src={BOT_AVATAR} alt=\"Bot\" style={{ width: 40, height: 40, borderRadius: '50%', background: '#fff', flexShrink: 0 }} />\r\n            <div style={{ fontWeight: 600, fontSize: 20, lineHeight: '1.2', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>\r\n              {uiMessages.chatTitle}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Messages Container */}\r\n          <div style={{ \r\n            background: '#f7f8fa', \r\n            flex: 1, \r\n            overflow: 'auto', \r\n            padding: '12px',\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            direction: LanguageService.getTextDirection(userLanguage)\r\n          }}>\r\n            {messages.map((msg, index) => (\r\n              <div\r\n                key={index}\r\n                style={{\r\n                  display: 'flex',\r\n                  marginBottom: '8px',\r\n                  alignItems: msg.isBot ? 'flex-start' : 'flex-end',\r\n                  justifyContent: msg.isBot ? 'flex-start' : 'flex-end'\r\n                }}\r\n              >\r\n                {msg.isBot && (\r\n                  <img src={BOT_AVATAR} alt=\"Bot\" style={{ width: 32, height: 32, borderRadius: '50%', marginRight: 8, alignSelf: 'flex-end' }} />\r\n                )}\r\n                <div\r\n                  style={{\r\n                    background: msg.isBot ? 'white' : '#007bff',\r\n                    color: msg.isBot ? '#333' : 'white',\r\n                    borderRadius: msg.isBot ? '18px 18px 18px 4px' : '18px 18px 4px 18px',\r\n                    padding: '10px 16px',\r\n                    maxWidth: '75%',\r\n                    fontSize: 15,\r\n                    boxShadow: msg.isBot ? '0 1px 4px rgba(0,0,0,0.04)' : 'none',\r\n                    marginLeft: msg.isBot ? 0 : 32,\r\n                    marginRight: msg.isBot ? 32 : 0\r\n                  }}\r\n                >\r\n                  {msg.text}\r\n                </div>\r\n              </div>\r\n            ))}\r\n            {isLoading && (\r\n              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>\r\n                <img src={BOT_AVATAR} alt=\"Bot\" style={{ width: 32, height: 32, borderRadius: '50%', marginRight: 8 }} />\r\n                <div style={{ \r\n                  background: 'white', \r\n                  color: '#333', \r\n                  borderRadius: '18px 18px 18px 4px', \r\n                  padding: '10px 16px', \r\n                  fontSize: 15, \r\n                  opacity: 0.7 \r\n                }}>\r\n                  <span className=\"spinner-border spinner-border-sm text-secondary me-2\" role=\"status\" />\r\n                  ...\r\n                </div>\r\n              </div>\r\n            )}\r\n            <QuickSuggestions \r\n              language={userLanguage}\r\n              onSuggestionClick={handleSuggestionClick}\r\n              isVisible={showSuggestions && messages.length <= 1}\r\n            />\r\n            <div ref={messagesEndRef} />\r\n          </div>\r\n\r\n          {/* Input Container */}\r\n          <div style={{ \r\n            background: 'white', \r\n            padding: '8px', \r\n            borderBottomLeftRadius: 22, \r\n            borderBottomRightRadius: 22, \r\n            flexShrink: 0,\r\n            borderTop: '1px solid #e0e3e8'\r\n          }}>\r\n            <form style={{ display: 'flex' }} onSubmit={sendMessage} autoComplete=\"off\">\r\n              <input\r\n                type=\"text\"\r\n                className=\"form-control me-2\"\r\n                value={inputMessage}\r\n                onChange={handleInputChange}\r\n                placeholder={uiMessages.placeholder}\r\n                disabled={isLoading}\r\n                style={{ borderRadius: 18, fontSize: 15, background: '#f4f6fa', border: '1px solid #e0e3e8' }}\r\n              />\r\n              <button\r\n                type=\"submit\"\r\n                className=\"btn btn-primary d-flex align-items-center justify-content-center\"\r\n                disabled={isLoading || !inputMessage.trim()}\r\n                style={{ borderRadius: 18, minWidth: 44, minHeight: 44, fontSize: 20 }}\r\n                aria-label={uiMessages.send}\r\n              >\r\n                <span role=\"img\" aria-label=\"send\">➤</span>\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Chatbot;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sCAAsC;AAC7C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,UAAU,GAAG,yDAAyD,CAAC,CAAC;;AAE9E,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,MAAMI,eAAe,CAACW,qBAAqB,CAAC,CAAC,CAAC;EAC/F,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,MAAMI,eAAe,CAACgB,aAAa,CAAChB,eAAe,CAACW,qBAAqB,CAAC,CAAC,CAAC,CAAC;EAC1H,MAAM,CAACM,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM6B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM6B,eAAe,GAAG;IACtBC,EAAE,EAAE,mEAAmE;IACvEC,EAAE,EAAE,oDAAoD;IACxDC,EAAE,EAAE,oDAAoD;IACxDC,EAAE,EAAE,kEAAkE;IACtEC,EAAE,EAAE;EACN,CAAC;;EAED;EACAjC,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,CAACoB,MAAM,KAAK,CAAC,EAAE;MACzBnB,WAAW,CAAC,CAAC;QAAEoB,IAAI,EAAEP,eAAe,CAACjB,YAAY,CAAC;QAAEyB,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IACrE;IACAnB,aAAa,CAACf,eAAe,CAACgB,aAAa,CAACP,YAAY,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,YAAY,EAAEG,QAAQ,CAACoB,MAAM,EAAEN,eAAe,CAAC,CAAC;EAEpD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1B3B,SAAS,CAAC,CAACD,MAAM,CAAC;EACpB,CAAC;EAED,MAAM6B,iBAAiB,GAAIC,CAAC,IAAK;IAC/BjB,eAAe,CAACiB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EACjC,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAhB,cAAc,CAACiB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED9C,SAAS,CAAC,MAAM;IACd0C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5B,QAAQ,CAAC,CAAC;;EAEd;EACAd,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,SAAS,EAAE;MACd,MAAMsB,YAAY,GAAG,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;MAC3F3B,YAAY,CAACqB,YAAY,CAAC;IAC5B;EACF,CAAC,EAAE,CAACtB,SAAS,CAAC,CAAC;;EAEf;EACA,MAAM6B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI;MACF,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAIC,cAAc,CAACD,OAAO,CAAC,MAAM,CAAC;MACjF,IAAIF,UAAU,EAAE;QACd,MAAMI,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC;QACnC;QACA,MAAMO,MAAM,GAAGH,IAAI,CAACI,EAAE,GAAGC,QAAQ,CAACL,IAAI,CAACI,EAAE,EAAE,EAAE,CAAC,GAAG,IAAI;QACrD,OAAO;UACLA,EAAE,EAAEE,KAAK,CAACH,MAAM,CAAC,GAAG,IAAI,GAAGA,MAAM;UACjCI,KAAK,EAAEP,IAAI,CAACO;QACd,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;IACA,OAAO;MAAEJ,EAAE,EAAE,IAAI;MAAEG,KAAK,EAAE;IAAK,CAAC;EAClC,CAAC;EAED,MAAMG,qBAAqB,GAAIC,UAAU,IAAK;IAC5ChD,eAAe,CAACgD,UAAU,CAAC;IAC3BlD,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMmD,WAAW,GAAG,MAAOhC,CAAC,IAAK;IAC/BA,CAAC,CAACiC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACnD,YAAY,CAACoD,IAAI,CAAC,CAAC,EAAE;IAE1BrD,kBAAkB,CAAC,KAAK,CAAC;IAEzB,MAAMsD,WAAW,GAAG;MAAEvC,IAAI,EAAEd,YAAY;MAAEe,KAAK,EAAE;IAAM,CAAC;IACxDrB,WAAW,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,WAAW,CAAC,CAAC;IAC3CpD,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,MAAMoD,QAAQ,GAAGtB,WAAW,CAAC,CAAC;MAE9Bc,OAAO,CAACS,GAAG,CAAC,qBAAqB,EAAE;QACjCC,OAAO,EAAEzD,YAAY;QACrBI,SAAS,EAAEA,SAAS;QACpBqC,MAAM,EAAEc,QAAQ,CAACb,EAAE;QACnBpD,YAAY,EAAEA;MAChB,CAAC,CAAC;MAEF,MAAMoE,QAAQ,GAAG,MAAM9E,KAAK,CAAC+E,IAAI,CAAC,uCAAuC,EAAE;QACzEF,OAAO,EAAEzD,YAAY;QACrBI,SAAS,EAAEA,SAAS;QACpBqC,MAAM,EAAEc,QAAQ,CAACb,EAAE;QACnBpD,YAAY,EAAEA;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIoE,QAAQ,CAACE,IAAI,CAACC,gBAAgB,IAAIH,QAAQ,CAACE,IAAI,CAACC,gBAAgB,KAAKvE,YAAY,EAAE;QACrFC,eAAe,CAACmE,QAAQ,CAACE,IAAI,CAACC,gBAAgB,CAAC;MACjD;MAEAnE,WAAW,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAExC,IAAI,EAAE4C,QAAQ,CAACE,IAAI,CAACF,QAAQ;QAAE3C,KAAK,EAAE;MAAK,CAAC,CAAC,CAAC;IAC/E,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,MAAMgB,aAAa,GAAG;QACpBtD,EAAE,EAAE,6EAA6E;QACjFC,EAAE,EAAE,iEAAiE;QACrEC,EAAE,EAAE,oFAAoF;QACxFC,EAAE,EAAE;MACN,CAAC;MACDjB,WAAW,CAAC4D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC5BxC,IAAI,EAAEgD,aAAa,CAACxE,YAAY,CAAC,IAAIwE,aAAa,CAACtD,EAAE;QACrDO,KAAK,EAAE;MACT,CAAC,CAAC,CAAC;IACL,CAAC,SAAS;MACRZ,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEnB,OAAA;IAAK+E,KAAK,EAAE;MAAEC,QAAQ,EAAE,OAAO;MAAEC,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAK,CAAE;IAAAC,QAAA,gBAE7EpF,OAAA;MACEqF,SAAS,EAAC,4EAA4E;MACtFN,KAAK,EAAE;QAAEO,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE,EAAE;QAAEC,YAAY,EAAE,KAAK;QAAEC,QAAQ,EAAE,EAAE;QAAEC,SAAS,EAAE,6BAA6B;QAAEC,UAAU,EAAE;MAAkB,CAAE;MAC7IC,OAAO,EAAE5D,aAAc;MACvB,cAAY5B,MAAM,GAAGO,UAAU,CAACkF,KAAK,GAAGlF,UAAU,CAACmF,IAAK;MAAAV,QAAA,EAEvDhF,MAAM,GAAG,GAAG,gBAAGJ,OAAA;QAAM+F,IAAI,EAAC,KAAK;QAAC,cAAW,MAAM;QAAAX,QAAA,EAAC;MAAE;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,EAGR/F,MAAM,iBACLJ,OAAA;MAAK+E,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,CAAC;QACRI,KAAK,EAAE,GAAG;QACVc,QAAQ,EAAE,MAAM;QAChBb,MAAM,EAAE,GAAG;QACXC,YAAY,EAAE,EAAE;QAChBE,SAAS,EAAE,6BAA6B;QACxCW,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE;MACd,CAAE;MAAApB,QAAA,gBAEApF,OAAA;QAAK+E,KAAK,EAAE;UACVyB,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,OAAO;UACdH,OAAO,EAAE,MAAM;UACfI,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,YAAY;UAC5BC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,EAAE;UACxBC,SAAS,EAAE,EAAE;UACbvB,MAAM,EAAE,EAAE;UACVwB,OAAO,EAAE,QAAQ;UACjBC,SAAS,EAAE,YAAY;UACvBX,QAAQ,EAAE,QAAQ;UAClBY,GAAG,EAAE,EAAE;UACPC,UAAU,EAAE;QACd,CAAE;QAAA9B,QAAA,gBACApF,OAAA;UAAKmH,GAAG,EAAElH,UAAW;UAACmH,GAAG,EAAC,KAAK;UAACrC,KAAK,EAAE;YAAEO,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,YAAY,EAAE,KAAK;YAAEgB,UAAU,EAAE,MAAM;YAAEU,UAAU,EAAE;UAAE;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5HnG,OAAA;UAAK+E,KAAK,EAAE;YAAEsC,UAAU,EAAE,GAAG;YAAE5B,QAAQ,EAAE,EAAE;YAAE6B,UAAU,EAAE,KAAK;YAAEC,UAAU,EAAE,QAAQ;YAAElB,QAAQ,EAAE,QAAQ;YAAEmB,YAAY,EAAE;UAAW,CAAE;UAAApC,QAAA,EAClIzE,UAAU,CAAC8G;QAAS;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnG,OAAA;QAAK+E,KAAK,EAAE;UACVyB,UAAU,EAAE,SAAS;UACrBkB,IAAI,EAAE,CAAC;UACPrB,QAAQ,EAAE,MAAM;UAChBU,OAAO,EAAE,MAAM;UACfT,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBoB,SAAS,EAAE9H,eAAe,CAAC+H,gBAAgB,CAACtH,YAAY;QAC1D,CAAE;QAAA8E,QAAA,GACC3E,QAAQ,CAACoH,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACvB/H,OAAA;UAEE+E,KAAK,EAAE;YACLuB,OAAO,EAAE,MAAM;YACf0B,YAAY,EAAE,KAAK;YACnBtB,UAAU,EAAEoB,GAAG,CAAC/F,KAAK,GAAG,YAAY,GAAG,UAAU;YACjD4E,cAAc,EAAEmB,GAAG,CAAC/F,KAAK,GAAG,YAAY,GAAG;UAC7C,CAAE;UAAAqD,QAAA,GAED0C,GAAG,CAAC/F,KAAK,iBACR/B,OAAA;YAAKmH,GAAG,EAAElH,UAAW;YAACmH,GAAG,EAAC,KAAK;YAACrC,KAAK,EAAE;cAAEO,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,YAAY,EAAE,KAAK;cAAEyC,WAAW,EAAE,CAAC;cAAEC,SAAS,EAAE;YAAW;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAChI,eACDnG,OAAA;YACE+E,KAAK,EAAE;cACLyB,UAAU,EAAEsB,GAAG,CAAC/F,KAAK,GAAG,OAAO,GAAG,SAAS;cAC3C0E,KAAK,EAAEqB,GAAG,CAAC/F,KAAK,GAAG,MAAM,GAAG,OAAO;cACnCyD,YAAY,EAAEsC,GAAG,CAAC/F,KAAK,GAAG,oBAAoB,GAAG,oBAAoB;cACrEgF,OAAO,EAAE,WAAW;cACpBX,QAAQ,EAAE,KAAK;cACfX,QAAQ,EAAE,EAAE;cACZC,SAAS,EAAEoC,GAAG,CAAC/F,KAAK,GAAG,4BAA4B,GAAG,MAAM;cAC5DoG,UAAU,EAAEL,GAAG,CAAC/F,KAAK,GAAG,CAAC,GAAG,EAAE;cAC9BkG,WAAW,EAAEH,GAAG,CAAC/F,KAAK,GAAG,EAAE,GAAG;YAChC,CAAE;YAAAqD,QAAA,EAED0C,GAAG,CAAChG;UAAI;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GAzBD4B,KAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0BP,CACN,CAAC,EACDjF,SAAS,iBACRlB,OAAA;UAAK+E,KAAK,EAAE;YAAEuB,OAAO,EAAE,MAAM;YAAEI,UAAU,EAAE,QAAQ;YAAEsB,YAAY,EAAE;UAAM,CAAE;UAAA5C,QAAA,gBACzEpF,OAAA;YAAKmH,GAAG,EAAElH,UAAW;YAACmH,GAAG,EAAC,KAAK;YAACrC,KAAK,EAAE;cAAEO,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE,EAAE;cAAEC,YAAY,EAAE,KAAK;cAAEyC,WAAW,EAAE;YAAE;UAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzGnG,OAAA;YAAK+E,KAAK,EAAE;cACVyB,UAAU,EAAE,OAAO;cACnBC,KAAK,EAAE,MAAM;cACbjB,YAAY,EAAE,oBAAoB;cAClCuB,OAAO,EAAE,WAAW;cACpBtB,QAAQ,EAAE,EAAE;cACZ2C,OAAO,EAAE;YACX,CAAE;YAAAhD,QAAA,gBACApF,OAAA;cAAMqF,SAAS,EAAC,sDAAsD;cAACU,IAAI,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,OAEzF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDnG,OAAA,CAACF,gBAAgB;UACfuI,QAAQ,EAAE/H,YAAa;UACvBgI,iBAAiB,EAAEtE,qBAAsB;UACzCuE,SAAS,EAAEzH,eAAe,IAAIL,QAAQ,CAACoB,MAAM,IAAI;QAAE;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACFnG,OAAA;UAAKwI,GAAG,EAAElH;QAAe;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGNnG,OAAA;QAAK+E,KAAK,EAAE;UACVyB,UAAU,EAAE,OAAO;UACnBO,OAAO,EAAE,KAAK;UACd0B,sBAAsB,EAAE,EAAE;UAC1BC,uBAAuB,EAAE,EAAE;UAC3BxB,UAAU,EAAE,CAAC;UACbyB,SAAS,EAAE;QACb,CAAE;QAAAvD,QAAA,eACApF,OAAA;UAAM+E,KAAK,EAAE;YAAEuB,OAAO,EAAE;UAAO,CAAE;UAACsC,QAAQ,EAAE1E,WAAY;UAAC2E,YAAY,EAAC,KAAK;UAAAzD,QAAA,gBACzEpF,OAAA;YACE8I,IAAI,EAAC,MAAM;YACXzD,SAAS,EAAC,mBAAmB;YAC7BjD,KAAK,EAAEpB,YAAa;YACpB+H,QAAQ,EAAE9G,iBAAkB;YAC5B+G,WAAW,EAAErI,UAAU,CAACqI,WAAY;YACpCC,QAAQ,EAAE/H,SAAU;YACpB6D,KAAK,EAAE;cAAES,YAAY,EAAE,EAAE;cAAEC,QAAQ,EAAE,EAAE;cAAEe,UAAU,EAAE,SAAS;cAAE0C,MAAM,EAAE;YAAoB;UAAE;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACFnG,OAAA;YACE8I,IAAI,EAAC,QAAQ;YACbzD,SAAS,EAAC,kEAAkE;YAC5E4D,QAAQ,EAAE/H,SAAS,IAAI,CAACF,YAAY,CAACoD,IAAI,CAAC,CAAE;YAC5CW,KAAK,EAAE;cAAES,YAAY,EAAE,EAAE;cAAE2D,QAAQ,EAAE,EAAE;cAAErC,SAAS,EAAE,EAAE;cAAErB,QAAQ,EAAE;YAAG,CAAE;YACvE,cAAY9E,UAAU,CAACyI,IAAK;YAAAhE,QAAA,eAE5BpF,OAAA;cAAM+F,IAAI,EAAC,KAAK;cAAC,cAAW,MAAM;cAAAX,QAAA,EAAC;YAAC;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChG,EAAA,CApRID,OAAO;AAAAmJ,EAAA,GAAPnJ,OAAO;AAsRb,eAAeA,OAAO;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}