{"systemParams": "win32-x64-127", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@emotion/react@^11.14.0", "@emotion/styled@^11.14.0", "@mui/icons-material@^7.0.2", "@mui/material@^7.0.2", "@mui/x-data-grid@^8.2.0", "@radix-ui/react-alert-dialog@^1.1.14", "@radix-ui/react-checkbox@^1.3.2", "@radix-ui/react-dialog@^1.1.14", "@radix-ui/react-label@^2.1.7", "@radix-ui/react-radio-group@^1.3.7", "@radix-ui/react-select@^2.2.5", "@radix-ui/react-tabs@^1.1.12", "@testing-library/dom@^10.4.0", "@testing-library/jest-dom@^6.6.3", "@testing-library/react@^16.3.0", "@testing-library/user-event@^13.5.0", "axios@^1.9.0", "bootstrap-icons@^1.12.1", "bootstrap@^5.3.5", "class-variance-authority@^0.7.1", "clsx@^2.1.1", "date-fns@^4.1.0", "emoji-picker-react@^4.12.3", "firebase@9.23.0", "framer-motion@^12.12.2", "html2canvas@^1.4.1", "i18next-browser-languagedetector@^8.1.0", "i18next@^21.10.0", "lucide-react@^0.518.0", "mdb-react-ui-kit@^9.0.0", "react-bootstrap@^2.10.9", "react-dom@^18.0.0", "react-helmet@^6.1.0", "react-i18next@^11.18.6", "react-icons@^5.5.0", "react-player@^2.16.0", "react-router-dom@^7.5.2", "react-scripts@^5.0.1", "react-toastify@^11.0.5", "react@^18.0.0", "recharts@^2.15.3", "socket.io-client@^4.8.1", "tailwind-merge@^3.3.1", "typescript@^4.9.5", "web-vitals@^2.1.4", "webpack-cli@^6.0.1", "webpack@^5.99.6"], "lockfileEntries": {"@adobe/css-tools@^4.4.0": "https://registry.npmjs.org/@adobe/css-tools/-/css-tools-4.4.2.tgz", "@alloc/quick-lru@^5.2.0": "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@apideck/better-ajv-errors@^0.3.1": "https://registry.npmjs.org/@apideck/better-ajv-errors/-/better-ajv-errors-0.3.6.tgz", "@babel/code-frame@^7.0.0": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "@babel/code-frame@^7.10.4": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "@babel/code-frame@^7.12.13": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "@babel/code-frame@^7.16.0": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "@babel/code-frame@^7.26.2": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "@babel/code-frame@^7.8.3": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.26.2.tgz", "@babel/compat-data@^7.22.6": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz", "@babel/compat-data@^7.26.8": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.26.8.tgz", "@babel/core@^7.1.0": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz", "@babel/core@^7.11.1": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz", "@babel/core@^7.12.3": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz", "@babel/core@^7.16.0": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz", "@babel/core@^7.7.2": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz", "@babel/core@^7.8.0": "https://registry.npmjs.org/@babel/core/-/core-7.26.10.tgz", "@babel/eslint-parser@^7.16.3": "https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.27.0.tgz", "@babel/generator@^7.26.10": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.0.tgz", "@babel/generator@^7.27.0": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.0.tgz", "@babel/generator@^7.7.2": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.0.tgz", "@babel/helper-annotate-as-pure@^7.18.6": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz", "@babel/helper-annotate-as-pure@^7.25.9": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.25.9.tgz", "@babel/helper-compilation-targets@^7.22.6": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.0.tgz", "@babel/helper-compilation-targets@^7.25.9": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.0.tgz", "@babel/helper-compilation-targets@^7.26.5": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.0.tgz", "@babel/helper-create-class-features-plugin@^7.18.6": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz", "@babel/helper-create-class-features-plugin@^7.21.0": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz", "@babel/helper-create-class-features-plugin@^7.25.9": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz", "@babel/helper-create-class-features-plugin@^7.27.0": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.0.tgz", "@babel/helper-create-regexp-features-plugin@^7.18.6": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.0.tgz", "@babel/helper-create-regexp-features-plugin@^7.25.9": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.0.tgz", "@babel/helper-define-polyfill-provider@^0.6.3": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "@babel/helper-define-polyfill-provider@^0.6.4": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "@babel/helper-member-expression-to-functions@^7.25.9": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.25.9.tgz", "@babel/helper-module-imports@^7.10.4": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "@babel/helper-module-imports@^7.16.7": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "@babel/helper-module-imports@^7.25.9": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.25.9.tgz", "@babel/helper-module-transforms@^7.25.9": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "@babel/helper-module-transforms@^7.26.0": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.26.0.tgz", "@babel/helper-optimise-call-expression@^7.25.9": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.25.9.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.18.6": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.20.2": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.22.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.25.9": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.26.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.26.5.tgz", "@babel/helper-remap-async-to-generator@^7.25.9": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.25.9.tgz", "@babel/helper-replace-supers@^7.25.9": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.26.5.tgz", "@babel/helper-replace-supers@^7.26.5": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.26.5.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.20.0": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.25.9": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.25.9.tgz", "@babel/helper-string-parser@^7.25.9": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.25.9.tgz", "@babel/helper-validator-identifier@^7.25.9": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.25.9.tgz", "@babel/helper-validator-option@^7.25.9": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "@babel/helper-wrap-function@^7.25.9": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.25.9.tgz", "@babel/helpers@^7.26.10": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.0.tgz", "@babel/parser@^7.1.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz", "@babel/parser@^7.14.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz", "@babel/parser@^7.20.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz", "@babel/parser@^7.26.10": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz", "@babel/parser@^7.27.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz", "@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.25.9": "https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz", "@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.25.9": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-class-field-initializer-scope/-/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.25.9": "https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.25.9": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.25.9.tgz", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.25.9": "https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.25.9.tgz", "@babel/plugin-proposal-class-properties@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz", "@babel/plugin-proposal-decorators@^7.16.4": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.25.9.tgz", "@babel/plugin-proposal-nullish-coalescing-operator@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-nullish-coalescing-operator/-/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz", "@babel/plugin-proposal-numeric-separator@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-numeric-separator/-/plugin-proposal-numeric-separator-7.18.6.tgz", "@babel/plugin-proposal-optional-chaining@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-optional-chaining/-/plugin-proposal-optional-chaining-7.21.0.tgz", "@babel/plugin-proposal-private-methods@^7.16.0": "https://registry.npmjs.org/@babel/plugin-proposal-private-methods/-/plugin-proposal-private-methods-7.18.6.tgz", "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz", "@babel/plugin-proposal-private-property-in-object@^7.16.7": "https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.11.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.12.13": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-static-block@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "@babel/plugin-syntax-decorators@^7.25.9": "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.25.9.tgz", "@babel/plugin-syntax-flow@^7.26.0": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.26.0.tgz", "@babel/plugin-syntax-import-assertions@^7.26.0": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.26.0.tgz", "@babel/plugin-syntax-import-attributes@^7.24.7": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.26.0.tgz", "@babel/plugin-syntax-import-attributes@^7.26.0": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.26.0.tgz", "@babel/plugin-syntax-import-meta@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.25.9": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.25.9.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-private-property-in-object@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "@babel/plugin-syntax-top-level-await@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.25.9": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz", "@babel/plugin-syntax-typescript@^7.7.2": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.25.9.tgz", "@babel/plugin-syntax-unicode-sets-regex@^7.18.6": "https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz", "@babel/plugin-transform-arrow-functions@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.25.9.tgz", "@babel/plugin-transform-async-generator-functions@^7.26.8": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.26.8.tgz", "@babel/plugin-transform-async-to-generator@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.25.9.tgz", "@babel/plugin-transform-block-scoped-functions@^7.26.5": "https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.26.5.tgz", "@babel/plugin-transform-block-scoping@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.0.tgz", "@babel/plugin-transform-class-properties@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.25.9.tgz", "@babel/plugin-transform-class-static-block@^7.26.0": "https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.26.0.tgz", "@babel/plugin-transform-classes@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.25.9.tgz", "@babel/plugin-transform-computed-properties@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.25.9.tgz", "@babel/plugin-transform-destructuring@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.25.9.tgz", "@babel/plugin-transform-dotall-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.25.9.tgz", "@babel/plugin-transform-duplicate-keys@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.25.9.tgz", "@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-duplicate-named-capturing-groups-regex/-/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz", "@babel/plugin-transform-dynamic-import@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.25.9.tgz", "@babel/plugin-transform-exponentiation-operator@^7.26.3": "https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.26.3.tgz", "@babel/plugin-transform-export-namespace-from@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.25.9.tgz", "@babel/plugin-transform-flow-strip-types@^7.16.0": "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.26.5.tgz", "@babel/plugin-transform-for-of@^7.26.9": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.26.9.tgz", "@babel/plugin-transform-function-name@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.25.9.tgz", "@babel/plugin-transform-json-strings@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.25.9.tgz", "@babel/plugin-transform-literals@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.25.9.tgz", "@babel/plugin-transform-logical-assignment-operators@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.25.9.tgz", "@babel/plugin-transform-member-expression-literals@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.25.9.tgz", "@babel/plugin-transform-modules-amd@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.25.9.tgz", "@babel/plugin-transform-modules-commonjs@^7.26.3": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.26.3.tgz", "@babel/plugin-transform-modules-systemjs@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.25.9.tgz", "@babel/plugin-transform-modules-umd@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.25.9.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.25.9.tgz", "@babel/plugin-transform-new-target@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.25.9.tgz", "@babel/plugin-transform-nullish-coalescing-operator@^7.26.6": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.26.6.tgz", "@babel/plugin-transform-numeric-separator@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.25.9.tgz", "@babel/plugin-transform-object-rest-spread@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.25.9.tgz", "@babel/plugin-transform-object-super@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.25.9.tgz", "@babel/plugin-transform-optional-catch-binding@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.25.9.tgz", "@babel/plugin-transform-optional-chaining@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.25.9.tgz", "@babel/plugin-transform-parameters@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.25.9.tgz", "@babel/plugin-transform-private-methods@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.25.9.tgz", "@babel/plugin-transform-private-property-in-object@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.25.9.tgz", "@babel/plugin-transform-property-literals@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.25.9.tgz", "@babel/plugin-transform-react-constant-elements@^7.12.1": "https://registry.npmjs.org/@babel/plugin-transform-react-constant-elements/-/plugin-transform-react-constant-elements-7.25.9.tgz", "@babel/plugin-transform-react-display-name@^7.16.0": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.25.9.tgz", "@babel/plugin-transform-react-display-name@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.25.9.tgz", "@babel/plugin-transform-react-jsx-development@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.25.9.tgz", "@babel/plugin-transform-react-jsx@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.25.9.tgz", "@babel/plugin-transform-react-pure-annotations@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.25.9.tgz", "@babel/plugin-transform-regenerator@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.0.tgz", "@babel/plugin-transform-regexp-modifiers@^7.26.0": "https://registry.npmjs.org/@babel/plugin-transform-regexp-modifiers/-/plugin-transform-regexp-modifiers-7.26.0.tgz", "@babel/plugin-transform-reserved-words@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.25.9.tgz", "@babel/plugin-transform-runtime@^7.16.4": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.26.10.tgz", "@babel/plugin-transform-shorthand-properties@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.25.9.tgz", "@babel/plugin-transform-spread@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.25.9.tgz", "@babel/plugin-transform-sticky-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.25.9.tgz", "@babel/plugin-transform-template-literals@^7.26.8": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.26.8.tgz", "@babel/plugin-transform-typeof-symbol@^7.26.7": "https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.27.0.tgz", "@babel/plugin-transform-typescript@^7.27.0": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.0.tgz", "@babel/plugin-transform-unicode-escapes@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.25.9.tgz", "@babel/plugin-transform-unicode-property-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.25.9.tgz", "@babel/plugin-transform-unicode-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.25.9.tgz", "@babel/plugin-transform-unicode-sets-regex@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.25.9.tgz", "@babel/preset-env@^7.11.0": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.26.9.tgz", "@babel/preset-env@^7.12.1": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.26.9.tgz", "@babel/preset-env@^7.16.4": "https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.26.9.tgz", "@babel/preset-modules@0.1.6-no-external-plugins": "https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz", "@babel/preset-react@^7.12.5": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.26.3.tgz", "@babel/preset-react@^7.16.0": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.26.3.tgz", "@babel/preset-typescript@^7.16.0": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.0.tgz", "@babel/runtime@^7.11.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.12.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.14.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.16.3": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.17.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.18.3": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.23.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.24.7": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.26.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.27.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.5.5": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.6.3": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.8.4": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.8.7": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.25.9": "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz", "@babel/template@^7.26.9": "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz", "@babel/template@^7.27.0": "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz", "@babel/template@^7.3.3": "https://registry.npmjs.org/@babel/template/-/template-7.27.0.tgz", "@babel/traverse@^7.25.9": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz", "@babel/traverse@^7.26.10": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz", "@babel/traverse@^7.26.5": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz", "@babel/traverse@^7.26.8": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz", "@babel/traverse@^7.27.0": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz", "@babel/traverse@^7.7.2": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.0.tgz", "@babel/types@^7.0.0": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.12.6": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.20.7": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.25.9": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.26.10": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.27.0": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.3.3": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@babel/types@^7.4.4": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "@bcoe/v8-coverage@^0.2.3": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "@csstools/normalize.css@*": "https://registry.npmjs.org/@csstools/normalize.css/-/normalize.css-12.1.1.tgz", "@csstools/postcss-cascade-layers@^1.1.1": "https://registry.npmjs.org/@csstools/postcss-cascade-layers/-/postcss-cascade-layers-1.1.1.tgz", "@csstools/postcss-color-function@^1.1.1": "https://registry.npmjs.org/@csstools/postcss-color-function/-/postcss-color-function-1.1.1.tgz", "@csstools/postcss-font-format-keywords@^1.0.1": "https://registry.npmjs.org/@csstools/postcss-font-format-keywords/-/postcss-font-format-keywords-1.0.1.tgz", "@csstools/postcss-hwb-function@^1.0.2": "https://registry.npmjs.org/@csstools/postcss-hwb-function/-/postcss-hwb-function-1.0.2.tgz", "@csstools/postcss-ic-unit@^1.0.1": "https://registry.npmjs.org/@csstools/postcss-ic-unit/-/postcss-ic-unit-1.0.1.tgz", "@csstools/postcss-is-pseudo-class@^2.0.7": "https://registry.npmjs.org/@csstools/postcss-is-pseudo-class/-/postcss-is-pseudo-class-2.0.7.tgz", "@csstools/postcss-nested-calc@^1.0.0": "https://registry.npmjs.org/@csstools/postcss-nested-calc/-/postcss-nested-calc-1.0.0.tgz", "@csstools/postcss-normalize-display-values@^1.0.1": "https://registry.npmjs.org/@csstools/postcss-normalize-display-values/-/postcss-normalize-display-values-1.0.1.tgz", "@csstools/postcss-oklab-function@^1.1.1": "https://registry.npmjs.org/@csstools/postcss-oklab-function/-/postcss-oklab-function-1.1.1.tgz", "@csstools/postcss-progressive-custom-properties@^1.1.0": "https://registry.npmjs.org/@csstools/postcss-progressive-custom-properties/-/postcss-progressive-custom-properties-1.3.0.tgz", "@csstools/postcss-progressive-custom-properties@^1.3.0": "https://registry.npmjs.org/@csstools/postcss-progressive-custom-properties/-/postcss-progressive-custom-properties-1.3.0.tgz", "@csstools/postcss-stepped-value-functions@^1.0.1": "https://registry.npmjs.org/@csstools/postcss-stepped-value-functions/-/postcss-stepped-value-functions-1.0.1.tgz", "@csstools/postcss-text-decoration-shorthand@^1.0.0": "https://registry.npmjs.org/@csstools/postcss-text-decoration-shorthand/-/postcss-text-decoration-shorthand-1.0.0.tgz", "@csstools/postcss-trigonometric-functions@^1.0.2": "https://registry.npmjs.org/@csstools/postcss-trigonometric-functions/-/postcss-trigonometric-functions-1.0.2.tgz", "@csstools/postcss-unset-value@^1.0.2": "https://registry.npmjs.org/@csstools/postcss-unset-value/-/postcss-unset-value-1.0.2.tgz", "@csstools/selector-specificity@^2.0.0": "https://registry.npmjs.org/@csstools/selector-specificity/-/selector-specificity-2.2.0.tgz", "@csstools/selector-specificity@^2.0.2": "https://registry.npmjs.org/@csstools/selector-specificity/-/selector-specificity-2.2.0.tgz", "@discoveryjs/json-ext@^0.6.1": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.3.tgz", "@emotion/babel-plugin@^11.13.5": "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz", "@emotion/cache@^11.13.5": "https://registry.npmjs.org/@emotion/cache/-/cache-11.14.0.tgz", "@emotion/cache@^11.14.0": "https://registry.npmjs.org/@emotion/cache/-/cache-11.14.0.tgz", "@emotion/hash@^0.9.2": "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.2.tgz", "@emotion/is-prop-valid@^0.8.2": "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-0.8.8.tgz", "@emotion/is-prop-valid@^1.3.0": "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.3.1.tgz", "@emotion/memoize@0.7.4": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.4.tgz", "@emotion/memoize@^0.9.0": "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.9.0.tgz", "@emotion/react@^11.14.0": "https://registry.npmjs.org/@emotion/react/-/react-11.14.0.tgz", "@emotion/serialize@^1.3.3": "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.3.3.tgz", "@emotion/sheet@^1.4.0": "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.4.0.tgz", "@emotion/styled@^11.14.0": "https://registry.npmjs.org/@emotion/styled/-/styled-11.14.0.tgz", "@emotion/unitless@^0.10.0": "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.10.0.tgz", "@emotion/use-insertion-effect-with-fallbacks@^1.2.0": "https://registry.npmjs.org/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz", "@emotion/utils@^1.4.2": "https://registry.npmjs.org/@emotion/utils/-/utils-1.4.2.tgz", "@emotion/weak-memoize@^0.4.0": "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz", "@eslint-community/eslint-utils@^4.2.0": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.6.1.tgz", "@eslint-community/regexpp@^4.4.0": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "@eslint-community/regexpp@^4.6.1": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "@eslint/eslintrc@^2.1.4": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "@eslint/js@8.57.1": "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz", "@firebase/analytics-compat@0.2.6": "https://registry.npmjs.org/@firebase/analytics-compat/-/analytics-compat-0.2.6.tgz", "@firebase/analytics-types@0.8.0": "https://registry.npmjs.org/@firebase/analytics-types/-/analytics-types-0.8.0.tgz", "@firebase/analytics@0.10.0": "https://registry.npmjs.org/@firebase/analytics/-/analytics-0.10.0.tgz", "@firebase/app-check-compat@0.3.7": "https://registry.npmjs.org/@firebase/app-check-compat/-/app-check-compat-0.3.7.tgz", "@firebase/app-check-interop-types@0.3.0": "https://registry.npmjs.org/@firebase/app-check-interop-types/-/app-check-interop-types-0.3.0.tgz", "@firebase/app-check-types@0.5.0": "https://registry.npmjs.org/@firebase/app-check-types/-/app-check-types-0.5.0.tgz", "@firebase/app-check@0.8.0": "https://registry.npmjs.org/@firebase/app-check/-/app-check-0.8.0.tgz", "@firebase/app-compat@0.2.13": "https://registry.npmjs.org/@firebase/app-compat/-/app-compat-0.2.13.tgz", "@firebase/app-types@0.9.0": "https://registry.npmjs.org/@firebase/app-types/-/app-types-0.9.0.tgz", "@firebase/app@0.9.13": "https://registry.npmjs.org/@firebase/app/-/app-0.9.13.tgz", "@firebase/auth-compat@0.4.2": "https://registry.npmjs.org/@firebase/auth-compat/-/auth-compat-0.4.2.tgz", "@firebase/auth-interop-types@0.2.1": "https://registry.npmjs.org/@firebase/auth-interop-types/-/auth-interop-types-0.2.1.tgz", "@firebase/auth-types@0.12.0": "https://registry.npmjs.org/@firebase/auth-types/-/auth-types-0.12.0.tgz", "@firebase/auth@0.23.2": "https://registry.npmjs.org/@firebase/auth/-/auth-0.23.2.tgz", "@firebase/component@0.6.4": "https://registry.npmjs.org/@firebase/component/-/component-0.6.4.tgz", "@firebase/database-compat@0.3.4": "https://registry.npmjs.org/@firebase/database-compat/-/database-compat-0.3.4.tgz", "@firebase/database-types@0.10.4": "https://registry.npmjs.org/@firebase/database-types/-/database-types-0.10.4.tgz", "@firebase/database@0.14.4": "https://registry.npmjs.org/@firebase/database/-/database-0.14.4.tgz", "@firebase/firestore-compat@0.3.12": "https://registry.npmjs.org/@firebase/firestore-compat/-/firestore-compat-0.3.12.tgz", "@firebase/firestore-types@2.5.1": "https://registry.npmjs.org/@firebase/firestore-types/-/firestore-types-2.5.1.tgz", "@firebase/firestore@3.13.0": "https://registry.npmjs.org/@firebase/firestore/-/firestore-3.13.0.tgz", "@firebase/functions-compat@0.3.5": "https://registry.npmjs.org/@firebase/functions-compat/-/functions-compat-0.3.5.tgz", "@firebase/functions-types@0.6.0": "https://registry.npmjs.org/@firebase/functions-types/-/functions-types-0.6.0.tgz", "@firebase/functions@0.10.0": "https://registry.npmjs.org/@firebase/functions/-/functions-0.10.0.tgz", "@firebase/installations-compat@0.2.4": "https://registry.npmjs.org/@firebase/installations-compat/-/installations-compat-0.2.4.tgz", "@firebase/installations-types@0.5.0": "https://registry.npmjs.org/@firebase/installations-types/-/installations-types-0.5.0.tgz", "@firebase/installations@0.6.4": "https://registry.npmjs.org/@firebase/installations/-/installations-0.6.4.tgz", "@firebase/logger@0.4.0": "https://registry.npmjs.org/@firebase/logger/-/logger-0.4.0.tgz", "@firebase/messaging-compat@0.2.4": "https://registry.npmjs.org/@firebase/messaging-compat/-/messaging-compat-0.2.4.tgz", "@firebase/messaging-interop-types@0.2.0": "https://registry.npmjs.org/@firebase/messaging-interop-types/-/messaging-interop-types-0.2.0.tgz", "@firebase/messaging@0.12.4": "https://registry.npmjs.org/@firebase/messaging/-/messaging-0.12.4.tgz", "@firebase/performance-compat@0.2.4": "https://registry.npmjs.org/@firebase/performance-compat/-/performance-compat-0.2.4.tgz", "@firebase/performance-types@0.2.0": "https://registry.npmjs.org/@firebase/performance-types/-/performance-types-0.2.0.tgz", "@firebase/performance@0.6.4": "https://registry.npmjs.org/@firebase/performance/-/performance-0.6.4.tgz", "@firebase/remote-config-compat@0.2.4": "https://registry.npmjs.org/@firebase/remote-config-compat/-/remote-config-compat-0.2.4.tgz", "@firebase/remote-config-types@0.3.0": "https://registry.npmjs.org/@firebase/remote-config-types/-/remote-config-types-0.3.0.tgz", "@firebase/remote-config@0.4.4": "https://registry.npmjs.org/@firebase/remote-config/-/remote-config-0.4.4.tgz", "@firebase/storage-compat@0.3.2": "https://registry.npmjs.org/@firebase/storage-compat/-/storage-compat-0.3.2.tgz", "@firebase/storage-types@0.8.0": "https://registry.npmjs.org/@firebase/storage-types/-/storage-types-0.8.0.tgz", "@firebase/storage@0.11.2": "https://registry.npmjs.org/@firebase/storage/-/storage-0.11.2.tgz", "@firebase/util@1.9.3": "https://registry.npmjs.org/@firebase/util/-/util-1.9.3.tgz", "@firebase/webchannel-wrapper@0.10.1": "https://registry.npmjs.org/@firebase/webchannel-wrapper/-/webchannel-wrapper-0.10.1.tgz", "@floating-ui/core@^1.7.2": "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.2.tgz", "@floating-ui/dom@^1.7.2": "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.2.tgz", "@floating-ui/react-dom@^2.0.0": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.4.tgz", "@floating-ui/utils@^0.2.10": "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz", "@grpc/grpc-js@~1.7.0": "https://registry.npmjs.org/@grpc/grpc-js/-/grpc-js-1.7.3.tgz", "@grpc/proto-loader@^0.6.13": "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.6.13.tgz", "@grpc/proto-loader@^0.7.0": "https://registry.npmjs.org/@grpc/proto-loader/-/proto-loader-0.7.15.tgz", "@humanwhocodes/config-array@^0.13.0": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "@humanwhocodes/module-importer@^1.0.1": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "@humanwhocodes/object-schema@^2.0.3": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz", "@jest/console@^27.5.1": "https://registry.npmjs.org/@jest/console/-/console-27.5.1.tgz", "@jest/console@^28.1.3": "https://registry.npmjs.org/@jest/console/-/console-28.1.3.tgz", "@jest/core@^27.5.1": "https://registry.npmjs.org/@jest/core/-/core-27.5.1.tgz", "@jest/environment@^27.5.1": "https://registry.npmjs.org/@jest/environment/-/environment-27.5.1.tgz", "@jest/fake-timers@^27.5.1": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-27.5.1.tgz", "@jest/globals@^27.5.1": "https://registry.npmjs.org/@jest/globals/-/globals-27.5.1.tgz", "@jest/reporters@^27.5.1": "https://registry.npmjs.org/@jest/reporters/-/reporters-27.5.1.tgz", "@jest/schemas@^28.1.3": "https://registry.npmjs.org/@jest/schemas/-/schemas-28.1.3.tgz", "@jest/source-map@^27.5.1": "https://registry.npmjs.org/@jest/source-map/-/source-map-27.5.1.tgz", "@jest/test-result@^27.5.1": "https://registry.npmjs.org/@jest/test-result/-/test-result-27.5.1.tgz", "@jest/test-result@^28.1.3": "https://registry.npmjs.org/@jest/test-result/-/test-result-28.1.3.tgz", "@jest/test-sequencer@^27.5.1": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-27.5.1.tgz", "@jest/transform@^27.5.1": "https://registry.npmjs.org/@jest/transform/-/transform-27.5.1.tgz", "@jest/types@^27.5.1": "https://registry.npmjs.org/@jest/types/-/types-27.5.1.tgz", "@jest/types@^28.1.3": "https://registry.npmjs.org/@jest/types/-/types-28.1.3.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/set-array@^1.2.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "@jridgewell/source-map@^0.3.3": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@jridgewell/trace-mapping@^0.3.25": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@leichtgewicht/ip-codec@^2.0.1": "https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz", "@mui/core-downloads-tracker@^7.0.2": "https://registry.npmjs.org/@mui/core-downloads-tracker/-/core-downloads-tracker-7.0.2.tgz", "@mui/icons-material@^7.0.2": "https://registry.npmjs.org/@mui/icons-material/-/icons-material-7.0.2.tgz", "@mui/material@^7.0.2": "https://registry.npmjs.org/@mui/material/-/material-7.0.2.tgz", "@mui/private-theming@^7.0.2": "https://registry.npmjs.org/@mui/private-theming/-/private-theming-7.0.2.tgz", "@mui/styled-engine@^7.0.2": "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-7.0.2.tgz", "@mui/system@^7.0.2": "https://registry.npmjs.org/@mui/system/-/system-7.0.2.tgz", "@mui/types@^7.4.1": "https://registry.npmjs.org/@mui/types/-/types-7.4.1.tgz", "@mui/utils@^7.0.2": "https://registry.npmjs.org/@mui/utils/-/utils-7.0.2.tgz", "@mui/x-data-grid@^8.2.0": "https://registry.npmjs.org/@mui/x-data-grid/-/x-data-grid-8.2.0.tgz", "@mui/x-internals@8.2.0": "https://registry.npmjs.org/@mui/x-internals/-/x-internals-8.2.0.tgz", "@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1": "https://registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@nodelib/fs.walk@^1.2.8": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@pkgjs/parseargs@^0.11.0": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "@pmmmwh/react-refresh-webpack-plugin@^0.5.3": "https://registry.npmjs.org/@pmmmwh/react-refresh-webpack-plugin/-/react-refresh-webpack-plugin-0.5.16.tgz", "@popperjs/core@2.11.5": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.5.tgz", "@popperjs/core@^2.11.8": "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz", "@protobufjs/aspromise@^1.1.1": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "@protobufjs/aspromise@^1.1.2": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "@protobufjs/base64@^1.1.2": "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz", "@protobufjs/codegen@^2.0.4": "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz", "@protobufjs/eventemitter@^1.1.0": "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "@protobufjs/fetch@^1.1.0": "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz", "@protobufjs/float@^1.0.2": "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz", "@protobufjs/inquire@^1.1.0": "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz", "@protobufjs/path@^1.1.2": "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz", "@protobufjs/pool@^1.1.0": "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz", "@protobufjs/utf8@^1.1.0": "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz", "@radix-ui/number@1.1.1": "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.1.tgz", "@radix-ui/primitive@1.1.2": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz", "@radix-ui/react-alert-dialog@^1.1.14": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.14.tgz", "@radix-ui/react-arrow@1.1.7": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.7.tgz", "@radix-ui/react-checkbox@^1.3.2": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.2.tgz", "@radix-ui/react-collection@1.1.7": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.7.tgz", "@radix-ui/react-compose-refs@1.1.2": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "@radix-ui/react-context@1.1.2": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz", "@radix-ui/react-dialog@1.1.14": "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.14.tgz", "@radix-ui/react-dialog@^1.1.14": "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.14.tgz", "@radix-ui/react-direction@1.1.1": "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz", "@radix-ui/react-dismissable-layer@1.1.10": "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz", "@radix-ui/react-focus-guards@1.1.2": "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz", "@radix-ui/react-focus-scope@1.1.7": "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.7.tgz", "@radix-ui/react-id@1.1.1": "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz", "@radix-ui/react-label@^2.1.7": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.7.tgz", "@radix-ui/react-popper@1.2.7": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.7.tgz", "@radix-ui/react-portal@1.1.9": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.9.tgz", "@radix-ui/react-presence@1.1.4": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz", "@radix-ui/react-primitive@2.1.3": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz", "@radix-ui/react-radio-group@^1.3.7": "https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.3.7.tgz", "@radix-ui/react-roving-focus@1.1.10": "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz", "@radix-ui/react-select@^2.2.5": "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.2.5.tgz", "@radix-ui/react-slot@1.2.3": "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz", "@radix-ui/react-tabs@^1.1.12": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.12.tgz", "@radix-ui/react-use-callback-ref@1.1.1": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz", "@radix-ui/react-use-controllable-state@1.2.2": "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz", "@radix-ui/react-use-effect-event@0.0.2": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz", "@radix-ui/react-use-escape-keydown@1.1.1": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz", "@radix-ui/react-use-layout-effect@1.1.1": "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz", "@radix-ui/react-use-previous@1.1.1": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz", "@radix-ui/react-use-rect@1.1.1": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz", "@radix-ui/react-use-size@1.1.1": "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz", "@radix-ui/react-visually-hidden@1.2.3": "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.3.tgz", "@radix-ui/rect@1.1.1": "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.1.tgz", "@react-aria/ssr@^3.5.0": "https://registry.npmjs.org/@react-aria/ssr/-/ssr-3.9.8.tgz", "@restart/hooks@^0.4.9": "https://registry.npmjs.org/@restart/hooks/-/hooks-0.4.16.tgz", "@restart/hooks@^0.5.0": "https://registry.npmjs.org/@restart/hooks/-/hooks-0.5.1.tgz", "@restart/ui@^1.9.4": "https://registry.npmjs.org/@restart/ui/-/ui-1.9.4.tgz", "@rollup/plugin-babel@^5.2.0": "https://registry.npmjs.org/@rollup/plugin-babel/-/plugin-babel-5.3.1.tgz", "@rollup/plugin-node-resolve@^11.2.1": "https://registry.npmjs.org/@rollup/plugin-node-resolve/-/plugin-node-resolve-11.2.1.tgz", "@rollup/plugin-replace@^2.4.1": "https://registry.npmjs.org/@rollup/plugin-replace/-/plugin-replace-2.4.2.tgz", "@rollup/pluginutils@^3.1.0": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-3.1.0.tgz", "@rtsao/scc@^1.1.0": "https://registry.npmjs.org/@rtsao/scc/-/scc-1.1.0.tgz", "@rushstack/eslint-patch@^1.1.0": "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.11.0.tgz", "@sinclair/typebox@^0.24.1": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.24.51.tgz", "@sinonjs/commons@^1.7.0": "https://registry.npmjs.org/@sinonjs/commons/-/commons-1.8.6.tgz", "@sinonjs/fake-timers@^8.0.1": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-8.1.0.tgz", "@socket.io/component-emitter@~3.1.0": "https://registry.npmjs.org/@socket.io/component-emitter/-/component-emitter-3.1.2.tgz", "@surma/rollup-plugin-off-main-thread@^2.2.3": "https://registry.npmjs.org/@surma/rollup-plugin-off-main-thread/-/rollup-plugin-off-main-thread-2.2.3.tgz", "@svgr/babel-plugin-add-jsx-attribute@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-5.4.0.tgz", "@svgr/babel-plugin-remove-jsx-attribute@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-5.4.0.tgz", "@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1": "https://registry.npmjs.org/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz", "@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1": "https://registry.npmjs.org/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz", "@svgr/babel-plugin-svg-dynamic-title@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-5.4.0.tgz", "@svgr/babel-plugin-svg-em-dimensions@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-5.4.0.tgz", "@svgr/babel-plugin-transform-react-native-svg@^5.4.0": "https://registry.npmjs.org/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-5.4.0.tgz", "@svgr/babel-plugin-transform-svg-component@^5.5.0": "https://registry.npmjs.org/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-5.5.0.tgz", "@svgr/babel-preset@^5.5.0": "https://registry.npmjs.org/@svgr/babel-preset/-/babel-preset-5.5.0.tgz", "@svgr/core@^5.5.0": "https://registry.npmjs.org/@svgr/core/-/core-5.5.0.tgz", "@svgr/hast-util-to-babel-ast@^5.5.0": "https://registry.npmjs.org/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-5.5.0.tgz", "@svgr/plugin-jsx@^5.5.0": "https://registry.npmjs.org/@svgr/plugin-jsx/-/plugin-jsx-5.5.0.tgz", "@svgr/plugin-svgo@^5.5.0": "https://registry.npmjs.org/@svgr/plugin-svgo/-/plugin-svgo-5.5.0.tgz", "@svgr/webpack@^5.5.0": "https://registry.npmjs.org/@svgr/webpack/-/webpack-5.5.0.tgz", "@swc/helpers@^0.5.0": "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.17.tgz", "@testing-library/dom@^10.4.0": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.4.0.tgz", "@testing-library/jest-dom@^6.6.3": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.3.tgz", "@testing-library/react@^16.3.0": "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz", "@testing-library/user-event@^13.5.0": "https://registry.npmjs.org/@testing-library/user-event/-/user-event-13.5.0.tgz", "@tootallnate/once@1": "https://registry.npmjs.org/@tootallnate/once/-/once-1.1.2.tgz", "@trysound/sax@0.2.0": "https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz", "@types/aria-query@^5.0.1": "https://registry.npmjs.org/@types/aria-query/-/aria-query-5.0.4.tgz", "@types/babel__core@^7.0.0": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "@types/babel__core@^7.1.14": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "@types/babel__generator@*": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz", "@types/babel__template@*": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "@types/babel__traverse@*": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/babel__traverse@^7.0.4": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/body-parser@*": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz", "@types/bonjour@^3.5.9": "https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.13.tgz", "@types/connect-history-api-fallback@^1.3.5": "https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz", "@types/connect@*": "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz", "@types/d3-array@^3.0.3": "https://registry.npmjs.org/@types/d3-array/-/d3-array-3.2.1.tgz", "@types/d3-color@*": "https://registry.npmjs.org/@types/d3-color/-/d3-color-3.1.3.tgz", "@types/d3-ease@^3.0.0": "https://registry.npmjs.org/@types/d3-ease/-/d3-ease-3.0.2.tgz", "@types/d3-interpolate@^3.0.1": "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz", "@types/d3-path@*": "https://registry.npmjs.org/@types/d3-path/-/d3-path-3.1.1.tgz", "@types/d3-scale@^4.0.2": "https://registry.npmjs.org/@types/d3-scale/-/d3-scale-4.0.9.tgz", "@types/d3-shape@^3.1.0": "https://registry.npmjs.org/@types/d3-shape/-/d3-shape-3.1.7.tgz", "@types/d3-time@*": "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.4.tgz", "@types/d3-time@^3.0.0": "https://registry.npmjs.org/@types/d3-time/-/d3-time-3.0.4.tgz", "@types/d3-timer@^3.0.0": "https://registry.npmjs.org/@types/d3-timer/-/d3-timer-3.0.2.tgz", "@types/eslint-scope@^3.7.7": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz", "@types/eslint@*": "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz", "@types/eslint@^7.29.0 || ^8.4.1": "https://registry.npmjs.org/@types/eslint/-/eslint-8.56.12.tgz", "@types/estree@*": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "@types/estree@0.0.39": "https://registry.npmjs.org/@types/estree/-/estree-0.0.39.tgz", "@types/estree@^1.0.6": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "@types/express-serve-static-core@*": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "@types/express-serve-static-core@^4.17.33": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.6.tgz", "@types/express-serve-static-core@^5.0.0": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "@types/express@*": "https://registry.npmjs.org/@types/express/-/express-5.0.1.tgz", "@types/express@^4.17.13": "https://registry.npmjs.org/@types/express/-/express-4.17.21.tgz", "@types/graceful-fs@^4.1.2": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "@types/html-minifier-terser@^6.0.0": "https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "@types/http-errors@*": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz", "@types/http-proxy@^1.17.8": "https://registry.npmjs.org/@types/http-proxy/-/http-proxy-1.17.16.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-coverage@^2.0.1": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-report@*": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "@types/istanbul-reports@^3.0.0": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "@types/json-schema@*": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.4": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.5": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.8": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json-schema@^7.0.9": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "@types/json5@^0.0.29": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz", "@types/long@^4.0.1": "https://registry.npmjs.org/@types/long/-/long-4.0.2.tgz", "@types/mime@^1": "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz", "@types/node-forge@^1.3.0": "https://registry.npmjs.org/@types/node-forge/-/node-forge-1.3.11.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-22.15.2.tgz", "@types/node@>=12.12.47": "https://registry.npmjs.org/@types/node/-/node-22.15.2.tgz", "@types/node@>=13.7.0": "https://registry.npmjs.org/@types/node/-/node-22.15.2.tgz", "@types/parse-json@^4.0.0": "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz", "@types/prettier@^2.1.5": "https://registry.npmjs.org/@types/prettier/-/prettier-2.7.3.tgz", "@types/prop-types@*": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.14.tgz", "@types/prop-types@^15.7.12": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.14.tgz", "@types/prop-types@^15.7.14": "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.14.tgz", "@types/q@^1.5.1": "https://registry.npmjs.org/@types/q/-/q-1.5.8.tgz", "@types/qs@*": "https://registry.npmjs.org/@types/qs/-/qs-6.9.18.tgz", "@types/range-parser@*": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz", "@types/react-transition-group@^4.4.12": "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.12.tgz", "@types/react-transition-group@^4.4.6": "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.12.tgz", "@types/react@>=16.9.11": "https://registry.npmjs.org/@types/react/-/react-18.3.20.tgz", "@types/resolve@1.17.1": "https://registry.npmjs.org/@types/resolve/-/resolve-1.17.1.tgz", "@types/retry@0.12.0": "https://registry.npmjs.org/@types/retry/-/retry-0.12.0.tgz", "@types/semver@^7.3.12": "https://registry.npmjs.org/@types/semver/-/semver-7.7.0.tgz", "@types/send@*": "https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz", "@types/serve-index@^1.9.1": "https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.4.tgz", "@types/serve-static@*": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.7.tgz", "@types/serve-static@^1.13.10": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.7.tgz", "@types/sockjs@^0.3.33": "https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.36.tgz", "@types/stack-utils@^2.0.0": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz", "@types/trusted-types@^2.0.2": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "@types/warning@^3.0.3": "https://registry.npmjs.org/@types/warning/-/warning-3.0.3.tgz", "@types/ws@^8.5.5": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz", "@types/yargs-parser@*": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "@types/yargs@^16.0.0": "https://registry.npmjs.org/@types/yargs/-/yargs-16.0.9.tgz", "@types/yargs@^17.0.8": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz", "@typescript-eslint/eslint-plugin@^5.5.0": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-5.62.0.tgz", "@typescript-eslint/experimental-utils@^5.0.0": "https://registry.npmjs.org/@typescript-eslint/experimental-utils/-/experimental-utils-5.62.0.tgz", "@typescript-eslint/parser@^5.5.0": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.62.0.tgz", "@typescript-eslint/scope-manager@5.62.0": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz", "@typescript-eslint/type-utils@5.62.0": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-5.62.0.tgz", "@typescript-eslint/types@5.62.0": "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.62.0.tgz", "@typescript-eslint/typescript-estree@5.62.0": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz", "@typescript-eslint/utils@5.62.0": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.62.0.tgz", "@typescript-eslint/utils@^5.58.0": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-5.62.0.tgz", "@typescript-eslint/visitor-keys@5.62.0": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz", "@ungap/structured-clone@^1.2.0": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz", "@webassemblyjs/ast@1.14.1": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz", "@webassemblyjs/ast@^1.14.1": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz", "@webassemblyjs/floating-point-hex-parser@1.13.2": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz", "@webassemblyjs/helper-api-error@1.13.2": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz", "@webassemblyjs/helper-buffer@1.14.1": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz", "@webassemblyjs/helper-numbers@1.13.2": "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz", "@webassemblyjs/helper-wasm-bytecode@1.13.2": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz", "@webassemblyjs/helper-wasm-section@1.14.1": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz", "@webassemblyjs/ieee754@1.13.2": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz", "@webassemblyjs/leb128@1.13.2": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz", "@webassemblyjs/utf8@1.13.2": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz", "@webassemblyjs/wasm-edit@^1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz", "@webassemblyjs/wasm-gen@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz", "@webassemblyjs/wasm-opt@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz", "@webassemblyjs/wasm-parser@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "@webassemblyjs/wasm-parser@^1.14.1": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "@webassemblyjs/wast-printer@1.14.1": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz", "@webpack-cli/configtest@^3.0.1": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-3.0.1.tgz", "@webpack-cli/info@^3.0.1": "https://registry.npmjs.org/@webpack-cli/info/-/info-3.0.1.tgz", "@webpack-cli/serve@^3.0.1": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-3.0.1.tgz", "@xtuc/ieee754@^1.2.0": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "@xtuc/long@4.2.2": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "abab@^2.0.3": "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz", "abab@^2.0.5": "https://registry.npmjs.org/abab/-/abab-2.0.6.tgz", "accepts@~1.3.4": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@~1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "acorn-globals@^6.0.0": "https://registry.npmjs.org/acorn-globals/-/acorn-globals-6.0.0.tgz", "acorn-jsx@^5.3.2": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "acorn-walk@^7.1.1": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-7.2.0.tgz", "acorn@^7.1.1": "https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz", "acorn@^8.14.0": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "acorn@^8.2.4": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "acorn@^8.8.2": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "acorn@^8.9.0": "https://registry.npmjs.org/acorn/-/acorn-8.14.1.tgz", "address@^1.0.1": "https://registry.npmjs.org/address/-/address-1.2.2.tgz", "address@^1.1.2": "https://registry.npmjs.org/address/-/address-1.2.2.tgz", "adjust-sourcemap-loader@^4.0.0": "https://registry.npmjs.org/adjust-sourcemap-loader/-/adjust-sourcemap-loader-4.0.0.tgz", "agent-base@6": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "ajv-formats@^2.1.1": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz", "ajv-keywords@^3.4.1": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^3.5.2": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "ajv-keywords@^5.1.0": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "ajv@^6.12.2": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.4": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^6.12.5": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "ajv@^8.0.0": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "ajv@^8.6.0": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "ajv@^8.9.0": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "ansi-escapes@^4.2.1": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-escapes@^4.3.1": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-html-community@^0.0.8": "https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz", "ansi-html@^0.0.9": "https://registry.npmjs.org/ansi-html/-/ansi-html-0.0.9.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^5.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "any-promise@^1.0.0": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "anymatch@^3.0.3": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "arg@^5.0.2": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz", "argparse@^1.0.7": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "argparse@^2.0.1": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "aria-hidden@^1.2.4": "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.6.tgz", "aria-query@5.3.0": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz", "aria-query@^5.0.0": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz", "aria-query@^5.3.2": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.2.tgz", "array-buffer-byte-length@^1.0.1": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "array-buffer-byte-length@^1.0.2": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "array-flatten@1.1.1": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "array-includes@^3.1.6": "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz", "array-includes@^3.1.8": "https://registry.npmjs.org/array-includes/-/array-includes-3.1.8.tgz", "array-union@^2.1.0": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "array.prototype.findlast@^1.2.5": "https://registry.npmjs.org/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz", "array.prototype.findlastindex@^1.2.5": "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.6.tgz", "array.prototype.flat@^1.3.1": "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz", "array.prototype.flat@^1.3.2": "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz", "array.prototype.flatmap@^1.3.2": "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz", "array.prototype.flatmap@^1.3.3": "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz", "array.prototype.reduce@^1.0.6": "https://registry.npmjs.org/array.prototype.reduce/-/array.prototype.reduce-1.0.8.tgz", "array.prototype.tosorted@^1.1.4": "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz", "arraybuffer.prototype.slice@^1.0.4": "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz", "asap@~2.0.6": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "ast-types-flow@^0.0.8": "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.8.tgz", "async-function@^1.0.0": "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz", "async@^3.2.3": "https://registry.npmjs.org/async/-/async-3.2.6.tgz", "asynckit@^0.4.0": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "at-least-node@^1.0.0": "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz", "autoprefixer@^10.4.13": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "available-typed-arrays@^1.0.7": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "axe-core@^4.10.0": "https://registry.npmjs.org/axe-core/-/axe-core-4.10.3.tgz", "axios@^1.9.0": "https://registry.npmjs.org/axios/-/axios-1.9.0.tgz", "axobject-query@^4.1.0": "https://registry.npmjs.org/axobject-query/-/axobject-query-4.1.0.tgz", "babel-jest@^27.4.2": "https://registry.npmjs.org/babel-jest/-/babel-jest-27.5.1.tgz", "babel-jest@^27.5.1": "https://registry.npmjs.org/babel-jest/-/babel-jest-27.5.1.tgz", "babel-loader@^8.2.3": "https://registry.npmjs.org/babel-loader/-/babel-loader-8.4.1.tgz", "babel-plugin-istanbul@^6.1.1": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-jest-hoist@^27.5.1": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-27.5.1.tgz", "babel-plugin-macros@^3.1.0": "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz", "babel-plugin-named-asset-import@^0.3.8": "https://registry.npmjs.org/babel-plugin-named-asset-import/-/babel-plugin-named-asset-import-0.3.8.tgz", "babel-plugin-polyfill-corejs2@^0.4.10": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz", "babel-plugin-polyfill-corejs3@^0.11.0": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz", "babel-plugin-polyfill-regenerator@^0.6.1": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz", "babel-plugin-transform-react-remove-prop-types@^0.4.24": "https://registry.npmjs.org/babel-plugin-transform-react-remove-prop-types/-/babel-plugin-transform-react-remove-prop-types-0.4.24.tgz", "babel-preset-current-node-syntax@^1.0.0": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "babel-preset-jest@^27.5.1": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-27.5.1.tgz", "babel-preset-react-app@^10.0.1": "https://registry.npmjs.org/babel-preset-react-app/-/babel-preset-react-app-10.1.0.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base64-arraybuffer@^1.0.2": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz", "batch@0.6.1": "https://registry.npmjs.org/batch/-/batch-0.6.1.tgz", "bfj@^7.0.2": "https://registry.npmjs.org/bfj/-/bfj-7.1.0.tgz", "big.js@^5.2.2": "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "bluebird@^3.7.2": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "body-parser@1.20.3": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "bonjour-service@^1.0.11": "https://registry.npmjs.org/bonjour-service/-/bonjour-service-1.3.0.tgz", "boolbase@^1.0.0": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "boolbase@~1.0.0": "https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz", "bootstrap-icons@^1.12.1": "https://registry.npmjs.org/bootstrap-icons/-/bootstrap-icons-1.12.1.tgz", "bootstrap@^5.3.5": "https://registry.npmjs.org/bootstrap/-/bootstrap-5.3.5.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "brace-expansion@^2.0.1": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "braces@^3.0.3": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "browser-process-hrtime@^1.0.0": "https://registry.npmjs.org/browser-process-hrtime/-/browser-process-hrtime-1.0.0.tgz", "browserslist@^4.0.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "browserslist@^4.18.1": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "browserslist@^4.21.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "browserslist@^4.24.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "browserslist@^4.24.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.4.tgz", "bser@2.1.1": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "builtin-modules@^3.1.0": "https://registry.npmjs.org/builtin-modules/-/builtin-modules-3.3.0.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "call-bind-apply-helpers@^1.0.0": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind@^1.0.7": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "call-bind@^1.0.8": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "call-bound@^1.0.2": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "call-bound@^1.0.3": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "call-bound@^1.0.4": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "callsites@^3.0.0": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "camel-case@^4.1.2": "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz", "camelcase-css@^2.0.1": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz", "camelcase@^5.3.1": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^6.2.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "camelcase@^6.2.1": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "caniuse-api@^3.0.0": "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz", "caniuse-lite@^1.0.0": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001715.tgz", "caniuse-lite@^1.0.30001688": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001715.tgz", "caniuse-lite@^1.0.30001702": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001715.tgz", "case-sensitive-paths-webpack-plugin@^2.4.0": "https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz", "chalk@^2.4.1": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^3.0.0": "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz", "chalk@^4.0.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.0.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "char-regex@^1.0.2": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz", "char-regex@^2.0.0": "https://registry.npmjs.org/char-regex/-/char-regex-2.0.2.tgz", "check-types@^11.2.3": "https://registry.npmjs.org/check-types/-/check-types-11.2.3.tgz", "chokidar@^3.4.2": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "chokidar@^3.5.3": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "chokidar@^3.6.0": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "chrome-trace-event@^1.0.2": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz", "ci-info@^3.2.0": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz", "cjs-module-lexer@^1.0.0": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "class-variance-authority@^0.7.1": "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz", "classnames@^2.3.2": "https://registry.npmjs.org/classnames/-/classnames-2.5.1.tgz", "clean-css@^5.2.2": "https://registry.npmjs.org/clean-css/-/clean-css-5.3.3.tgz", "cliui@^7.0.2": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "cliui@^8.0.1": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "clone-deep@^4.0.1": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "clsx@1.1.1": "https://registry.npmjs.org/clsx/-/clsx-1.1.1.tgz", "clsx@^2.0.0": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz", "clsx@^2.1.1": "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz", "co@^4.6.0": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "coa@^2.0.2": "https://registry.npmjs.org/coa/-/coa-2.0.2.tgz", "collect-v8-coverage@^1.0.0": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "colord@^2.9.1": "https://registry.npmjs.org/colord/-/colord-2.9.3.tgz", "colorette@^2.0.10": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "colorette@^2.0.14": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "combined-stream@^1.0.8": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "commander@^12.1.0": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "commander@^2.20.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^4.0.0": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "commander@^7.2.0": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "commander@^8.3.0": "https://registry.npmjs.org/commander/-/commander-8.3.0.tgz", "common-tags@^1.8.0": "https://registry.npmjs.org/common-tags/-/common-tags-1.8.2.tgz", "commondir@^1.0.1": "https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz", "compressible@~2.0.18": "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz", "compression@^1.7.4": "https://registry.npmjs.org/compression/-/compression-1.8.0.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "confusing-browser-globals@^1.0.11": "https://registry.npmjs.org/confusing-browser-globals/-/confusing-browser-globals-1.0.11.tgz", "connect-history-api-fallback@^2.0.0": "https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz", "content-disposition@0.5.4": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "content-type@~1.0.4": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "content-type@~1.0.5": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "convert-source-map@^1.4.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^1.5.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^1.6.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^1.7.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "convert-source-map@^2.0.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "cookie-signature@1.0.6": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "cookie@0.7.1": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "cookie@^1.0.1": "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz", "core-js-compat@^3.40.0": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.41.0.tgz", "core-js-pure@^3.23.3": "https://registry.npmjs.org/core-js-pure/-/core-js-pure-3.41.0.tgz", "core-js@^3.19.2": "https://registry.npmjs.org/core-js/-/core-js-3.41.0.tgz", "core-util-is@~1.0.0": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "cosmiconfig@^6.0.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-6.0.0.tgz", "cosmiconfig@^7.0.0": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "cross-spawn@^7.0.2": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.3": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.6": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "crypto-random-string@^2.0.0": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz", "css-blank-pseudo@^3.0.3": "https://registry.npmjs.org/css-blank-pseudo/-/css-blank-pseudo-3.0.3.tgz", "css-declaration-sorter@^6.3.1": "https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-6.4.1.tgz", "css-has-pseudo@^3.0.4": "https://registry.npmjs.org/css-has-pseudo/-/css-has-pseudo-3.0.4.tgz", "css-line-break@^2.1.0": "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz", "css-loader@^6.5.1": "https://registry.npmjs.org/css-loader/-/css-loader-6.11.0.tgz", "css-minimizer-webpack-plugin@^3.2.0": "https://registry.npmjs.org/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz", "css-prefers-color-scheme@^6.0.3": "https://registry.npmjs.org/css-prefers-color-scheme/-/css-prefers-color-scheme-6.0.3.tgz", "css-select-base-adapter@^0.1.1": "https://registry.npmjs.org/css-select-base-adapter/-/css-select-base-adapter-0.1.1.tgz", "css-select@^2.0.0": "https://registry.npmjs.org/css-select/-/css-select-2.1.0.tgz", "css-select@^4.1.3": "https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz", "css-tree@1.0.0-alpha.37": "https://registry.npmjs.org/css-tree/-/css-tree-1.0.0-alpha.37.tgz", "css-tree@^1.1.2": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "css-tree@^1.1.3": "https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz", "css-what@^3.2.1": "https://registry.npmjs.org/css-what/-/css-what-3.4.2.tgz", "css-what@^6.0.1": "https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz", "css.escape@^1.5.1": "https://registry.npmjs.org/css.escape/-/css.escape-1.5.1.tgz", "cssdb@^7.1.0": "https://registry.npmjs.org/cssdb/-/cssdb-7.11.2.tgz", "cssesc@^3.0.0": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "cssnano-preset-default@^5.2.14": "https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz", "cssnano-utils@^3.1.0": "https://registry.npmjs.org/cssnano-utils/-/cssnano-utils-3.1.0.tgz", "cssnano@^5.0.6": "https://registry.npmjs.org/cssnano/-/cssnano-5.1.15.tgz", "csso@^4.0.2": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "csso@^4.2.0": "https://registry.npmjs.org/csso/-/csso-4.2.0.tgz", "cssom@^0.4.4": "https://registry.npmjs.org/cssom/-/cssom-0.4.4.tgz", "cssom@~0.3.6": "https://registry.npmjs.org/cssom/-/cssom-0.3.8.tgz", "cssstyle@^2.3.0": "https://registry.npmjs.org/cssstyle/-/cssstyle-2.3.0.tgz", "csstype@^3.0.2": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "csstype@^3.1.3": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "d3-array@2 - 3": "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz", "d3-array@2.10.0 - 3": "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz", "d3-array@^3.1.6": "https://registry.npmjs.org/d3-array/-/d3-array-3.2.4.tgz", "d3-color@1 - 3": "https://registry.npmjs.org/d3-color/-/d3-color-3.1.0.tgz", "d3-ease@^3.0.1": "https://registry.npmjs.org/d3-ease/-/d3-ease-3.0.1.tgz", "d3-format@1 - 3": "https://registry.npmjs.org/d3-format/-/d3-format-3.1.0.tgz", "d3-interpolate@1.2.0 - 3": "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz", "d3-interpolate@^3.0.1": "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-3.0.1.tgz", "d3-path@^3.1.0": "https://registry.npmjs.org/d3-path/-/d3-path-3.1.0.tgz", "d3-scale@^4.0.2": "https://registry.npmjs.org/d3-scale/-/d3-scale-4.0.2.tgz", "d3-shape@^3.1.0": "https://registry.npmjs.org/d3-shape/-/d3-shape-3.2.0.tgz", "d3-time-format@2 - 4": "https://registry.npmjs.org/d3-time-format/-/d3-time-format-4.1.0.tgz", "d3-time@1 - 3": "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz", "d3-time@2.1.1 - 3": "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz", "d3-time@^3.0.0": "https://registry.npmjs.org/d3-time/-/d3-time-3.1.0.tgz", "d3-timer@^3.0.1": "https://registry.npmjs.org/d3-timer/-/d3-timer-3.0.1.tgz", "damerau-levenshtein@^1.0.8": "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz", "data-urls@^2.0.0": "https://registry.npmjs.org/data-urls/-/data-urls-2.0.0.tgz", "data-view-buffer@^1.0.2": "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz", "data-view-byte-length@^1.0.2": "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz", "data-view-byte-offset@^1.0.1": "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz", "date-fns@^4.1.0": "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@4": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "debug@^2.6.0": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^3.2.7": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "debug@^4.1.1": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "debug@^4.3.1": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "debug@^4.3.2": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "debug@^4.3.4": "https://registry.npmjs.org/debug/-/debug-4.4.0.tgz", "debug@~4.3.1": "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz", "debug@~4.3.2": "https://registry.npmjs.org/debug/-/debug-4.3.7.tgz", "decimal.js-light@^2.4.1": "https://registry.npmjs.org/decimal.js-light/-/decimal.js-light-2.5.1.tgz", "decimal.js@^10.2.1": "https://registry.npmjs.org/decimal.js/-/decimal.js-10.5.0.tgz", "dedent@^0.7.0": "https://registry.npmjs.org/dedent/-/dedent-0.7.0.tgz", "deep-is@^0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "deep-is@~0.1.3": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "deepmerge@^4.0.0": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "deepmerge@^4.2.2": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "default-gateway@^6.0.3": "https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz", "define-data-property@^1.0.1": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "define-data-property@^1.1.4": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "define-lazy-prop@^2.0.0": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "define-properties@^1.1.3": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "define-properties@^1.2.1": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "delayed-stream@~1.0.0": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "depd@~1.1.2": "https://registry.npmjs.org/depd/-/depd-1.1.2.tgz", "dequal@^2.0.3": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "destroy@1.2.0": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "detect-newline@^3.0.0": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz", "detect-node-es@^1.1.0": "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz", "detect-node@^2.0.4": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "detect-port-alt@^1.1.6": "https://registry.npmjs.org/detect-port-alt/-/detect-port-alt-1.1.6.tgz", "didyoumean@^1.2.2": "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz", "diff-sequences@^27.5.1": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-27.5.1.tgz", "dir-glob@^3.0.1": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "dlv@^1.1.3": "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz", "dns-packet@^5.2.2": "https://registry.npmjs.org/dns-packet/-/dns-packet-5.6.1.tgz", "doctrine@^2.1.0": "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz", "doctrine@^3.0.0": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "dom-accessibility-api@^0.5.9": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz", "dom-accessibility-api@^0.6.3": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.6.3.tgz", "dom-converter@^0.2.0": "https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz", "dom-helpers@^5.0.1": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "dom-helpers@^5.2.0": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "dom-helpers@^5.2.1": "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz", "dom-serializer@0": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-0.2.2.tgz", "dom-serializer@^1.0.1": "https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz", "domelementtype@1": "https://registry.npmjs.org/domelementtype/-/domelementtype-1.3.1.tgz", "domelementtype@^2.0.1": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domelementtype@^2.2.0": "https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz", "domexception@^2.0.1": "https://registry.npmjs.org/domexception/-/domexception-2.0.1.tgz", "domhandler@^4.0.0": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.2.0": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domhandler@^4.3.1": "https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz", "domutils@^1.7.0": "https://registry.npmjs.org/domutils/-/domutils-1.7.0.tgz", "domutils@^2.5.2": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "domutils@^2.8.0": "https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz", "dot-case@^3.0.4": "https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz", "dotenv-expand@^5.1.0": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "dotenv@^10.0.0": "https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz", "dunder-proto@^1.0.0": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "dunder-proto@^1.0.1": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "duplexer@^0.1.2": "https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz", "eastasianwidth@^0.2.0": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "ejs@^3.1.6": "https://registry.npmjs.org/ejs/-/ejs-3.1.10.tgz", "electron-to-chromium@^1.5.73": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.142.tgz", "emittery@^0.10.2": "https://registry.npmjs.org/emittery/-/emittery-0.10.2.tgz", "emittery@^0.8.1": "https://registry.npmjs.org/emittery/-/emittery-0.8.1.tgz", "emoji-picker-react@^4.12.3": "https://registry.npmjs.org/emoji-picker-react/-/emoji-picker-react-4.12.3.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "emojis-list@^3.0.0": "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz", "encodeurl@~1.0.2": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "encodeurl@~2.0.0": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "engine.io-client@~6.6.1": "https://registry.npmjs.org/engine.io-client/-/engine.io-client-6.6.3.tgz", "engine.io-parser@~5.2.1": "https://registry.npmjs.org/engine.io-parser/-/engine.io-parser-5.2.3.tgz", "enhanced-resolve@^5.17.1": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.1.tgz", "entities@^2.0.0": "https://registry.npmjs.org/entities/-/entities-2.2.0.tgz", "envinfo@^7.14.0": "https://registry.npmjs.org/envinfo/-/envinfo-7.14.0.tgz", "error-ex@^1.3.1": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.6": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz", "es-abstract@^1.17.2": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-abstract@^1.17.5": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-abstract@^1.23.2": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-abstract@^1.23.3": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-abstract@^1.23.5": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-abstract@^1.23.6": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-abstract@^1.23.9": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.23.9.tgz", "es-array-method-boxes-properly@^1.0.0": "https://registry.npmjs.org/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz", "es-define-property@^1.0.0": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-define-property@^1.0.1": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "es-iterator-helpers@^1.2.1": "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz", "es-module-lexer@^1.2.1": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-set-tostringtag@^2.0.3": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "es-set-tostringtag@^2.1.0": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "es-shim-unscopables@^1.0.2": "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz", "es-shim-unscopables@^1.1.0": "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz", "es-to-primitive@^1.3.0": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-html@~1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^2.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "escodegen@^1.8.1": "https://registry.npmjs.org/escodegen/-/escodegen-1.14.3.tgz", "escodegen@^2.0.0": "https://registry.npmjs.org/escodegen/-/escodegen-2.1.0.tgz", "eslint-config-react-app@^7.0.1": "https://registry.npmjs.org/eslint-config-react-app/-/eslint-config-react-app-7.0.1.tgz", "eslint-import-resolver-node@^0.3.9": "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz", "eslint-module-utils@^2.12.0": "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.0.tgz", "eslint-plugin-flowtype@^8.0.3": "https://registry.npmjs.org/eslint-plugin-flowtype/-/eslint-plugin-flowtype-8.0.3.tgz", "eslint-plugin-import@^2.25.3": "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.31.0.tgz", "eslint-plugin-jest@^25.3.0": "https://registry.npmjs.org/eslint-plugin-jest/-/eslint-plugin-jest-25.7.0.tgz", "eslint-plugin-jsx-a11y@^6.5.1": "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.10.2.tgz", "eslint-plugin-react-hooks@^4.3.0": "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.2.tgz", "eslint-plugin-react@^7.27.1": "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.37.5.tgz", "eslint-plugin-testing-library@^5.0.1": "https://registry.npmjs.org/eslint-plugin-testing-library/-/eslint-plugin-testing-library-5.11.1.tgz", "eslint-scope@5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^5.1.1": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "eslint-scope@^7.2.2": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz", "eslint-visitor-keys@^2.1.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "eslint-visitor-keys@^3.3.0": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.1": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-visitor-keys@^3.4.3": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "eslint-webpack-plugin@^3.1.1": "https://registry.npmjs.org/eslint-webpack-plugin/-/eslint-webpack-plugin-3.2.0.tgz", "eslint@^8.3.0": "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz", "espree@^9.6.0": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "espree@^9.6.1": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "esprima@1.2.2": "https://registry.npmjs.org/esprima/-/esprima-1.2.2.tgz", "esprima@^4.0.0": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esprima@^4.0.1": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "esquery@^1.4.2": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz", "esrecurse@^4.3.0": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "estraverse@^4.1.1": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^4.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "estraverse@^5.1.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.2.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estraverse@^5.3.0": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "estree-walker@^1.0.1": "https://registry.npmjs.org/estree-walker/-/estree-walker-1.0.1.tgz", "esutils@^2.0.2": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "etag@~1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "eventemitter3@^4.0.0": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "eventemitter3@^4.0.1": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "events@^3.2.0": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "execa@^5.0.0": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "exit@^0.1.2": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "expect@^27.5.1": "https://registry.npmjs.org/expect/-/expect-27.5.1.tgz", "express@^4.17.3": "https://registry.npmjs.org/express/-/express-4.21.2.tgz", "fast-deep-equal@^3.1.1": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-equals@^5.0.1": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.2.tgz", "fast-glob@^3.2.9": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-glob@^3.3.2": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-json-stable-stringify@^2.0.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-json-stable-stringify@^2.1.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fast-levenshtein@^2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-levenshtein@~2.0.6": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "fast-uri@^3.0.1": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz", "fastest-levenshtein@^1.0.12": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz", "fastq@^1.6.0": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "faye-websocket@0.11.4": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "faye-websocket@^0.11.3": "https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz", "fb-watchman@^2.0.0": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz", "file-entry-cache@^6.0.1": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "file-loader@^6.2.0": "https://registry.npmjs.org/file-loader/-/file-loader-6.2.0.tgz", "filelist@^1.0.4": "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz", "filesize@^8.0.6": "https://registry.npmjs.org/filesize/-/filesize-8.0.7.tgz", "fill-range@^7.1.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "finalhandler@1.3.1": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "find-cache-dir@^3.3.1": "https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz", "find-root@^1.1.0": "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz", "find-up@^3.0.0": "https://registry.npmjs.org/find-up/-/find-up-3.0.0.tgz", "find-up@^4.0.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^4.1.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "firebase@9.23.0": "https://registry.npmjs.org/firebase/-/firebase-9.23.0.tgz", "flairup@1.0.0": "https://registry.npmjs.org/flairup/-/flairup-1.0.0.tgz", "flat-cache@^3.0.4": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz", "flat@^5.0.2": "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz", "flatted@^3.2.9": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz", "follow-redirects@^1.0.0": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "follow-redirects@^1.15.6": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "for-each@^0.3.3": "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz", "for-each@^0.3.5": "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz", "foreground-child@^3.1.0": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz", "fork-ts-checker-webpack-plugin@^6.5.0": "https://registry.npmjs.org/fork-ts-checker-webpack-plugin/-/fork-ts-checker-webpack-plugin-6.5.3.tgz", "form-data@^3.0.0": "https://registry.npmjs.org/form-data/-/form-data-3.0.3.tgz", "form-data@^4.0.0": "https://registry.npmjs.org/form-data/-/form-data-4.0.2.tgz", "forwarded@0.2.0": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fraction.js@^4.3.7": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "framer-motion@^10.16.4": "https://registry.npmjs.org/framer-motion/-/framer-motion-10.18.0.tgz", "framer-motion@^12.12.2": "https://registry.npmjs.org/framer-motion/-/framer-motion-12.19.1.tgz", "fresh@0.5.2": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "fs-extra@^10.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^9.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-extra@^9.0.1": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs-monkey@^1.0.4": "https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.6.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@^2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "function.prototype.name@^1.1.6": "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz", "function.prototype.name@^1.1.8": "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz", "functions-have-names@^1.2.3": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-intrinsic@^1.2.4": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.2.5": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.2.6": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.2.7": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.3.0": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-nonce@^1.0.0": "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz", "get-own-enumerable-property-symbols@^3.0.0": "https://registry.npmjs.org/get-own-enumerable-property-symbols/-/get-own-enumerable-property-symbols-3.0.2.tgz", "get-package-type@^0.1.0": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz", "get-proto@^1.0.0": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "get-proto@^1.0.1": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "get-stream@^6.0.0": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "get-symbol-description@^1.1.0": "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz", "glob-parent@^5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@^6.0.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-to-regexp@^0.4.1": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "glob@^10.3.10": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "glob@^7.1.1": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.2": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.4": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.6": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "global-modules@^2.0.0": "https://registry.npmjs.org/global-modules/-/global-modules-2.0.0.tgz", "global-prefix@^3.0.0": "https://registry.npmjs.org/global-prefix/-/global-prefix-3.0.0.tgz", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "globals@^13.19.0": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "globalthis@^1.0.4": "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz", "globby@^11.0.4": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "globby@^11.1.0": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "gopd@^1.0.1": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "gopd@^1.2.0": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "graceful-fs@^4.1.2": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.1.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.11": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.9": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graphemer@^1.4.0": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "gzip-size@^6.0.0": "https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz", "handle-thing@^2.0.0": "https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz", "harmony-reflect@^1.4.6": "https://registry.npmjs.org/harmony-reflect/-/harmony-reflect-1.6.2.tgz", "has-bigints@^1.0.2": "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "has-property-descriptors@^1.0.0": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "has-property-descriptors@^1.0.2": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "has-proto@^1.2.0": "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz", "has-symbols@^1.0.1": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-symbols@^1.0.3": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-symbols@^1.1.0": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "has-tostringtag@^1.0.2": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "he@^1.2.0": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "hoist-non-react-statics@^3.3.1": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hoopy@^0.1.4": "https://registry.npmjs.org/hoopy/-/hoopy-0.1.4.tgz", "hpack.js@^2.1.6": "https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz", "html-encoding-sniffer@^2.0.1": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-2.0.1.tgz", "html-entities@^2.1.0": "https://registry.npmjs.org/html-entities/-/html-entities-2.6.0.tgz", "html-entities@^2.3.2": "https://registry.npmjs.org/html-entities/-/html-entities-2.6.0.tgz", "html-escaper@^2.0.0": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz", "html-minifier-terser@^6.0.2": "https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz", "html-parse-stringify@^3.0.1": "https://registry.npmjs.org/html-parse-stringify/-/html-parse-stringify-3.0.1.tgz", "html-webpack-plugin@^5.5.0": "https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-5.6.3.tgz", "html2canvas@^1.4.1": "https://registry.yarnpkg.com/html2canvas/-/html2canvas-1.4.1.tgz#7cef1888311b5011d507794a066041b14669a543", "htmlparser2@^6.1.0": "https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz", "http-deceiver@^1.2.7": "https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "http-errors@~1.6.2": "https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz", "http-parser-js@>=0.5.1": "https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.10.tgz", "http-proxy-agent@^4.0.1": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-4.0.1.tgz", "http-proxy-middleware@^2.0.3": "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-2.0.9.tgz", "http-proxy@^1.18.1": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "https-proxy-agent@^5.0.0": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "human-signals@^2.1.0": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "i18next-browser-languagedetector@^8.1.0": "https://registry.npmjs.org/i18next-browser-languagedetector/-/i18next-browser-languagedetector-8.2.0.tgz", "i18next@^21.10.0": "https://registry.npmjs.org/i18next/-/i18next-21.10.0.tgz", "iconv-lite@0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "iconv-lite@^0.6.3": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "icss-utils@^5.0.0": "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz", "icss-utils@^5.1.0": "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz", "idb@7.0.1": "https://registry.npmjs.org/idb/-/idb-7.0.1.tgz", "idb@7.1.1": "https://registry.npmjs.org/idb/-/idb-7.1.1.tgz", "idb@^7.0.1": "https://registry.npmjs.org/idb/-/idb-7.1.1.tgz", "identity-obj-proxy@^3.0.0": "https://registry.npmjs.org/identity-obj-proxy/-/identity-obj-proxy-3.0.0.tgz", "ignore@^5.2.0": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "immer@^9.0.7": "https://registry.npmjs.org/immer/-/immer-9.0.21.tgz", "import-fresh@^3.1.0": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "import-fresh@^3.2.1": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "import-local@^3.0.2": "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz", "imurmurhash@^0.1.4": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "indent-string@^4.0.0": "https://registry.npmjs.org/indent-string/-/indent-string-4.0.0.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.1": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@^2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "ini@^1.3.5": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "internal-slot@^1.1.0": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz", "internmap@1 - 2": "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz", "interpret@^3.1.1": "https://registry.npmjs.org/interpret/-/interpret-3.1.1.tgz", "invariant@^2.2.4": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "ipaddr.js@1.9.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "ipaddr.js@^2.0.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.2.0.tgz", "is-array-buffer@^3.0.4": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "is-array-buffer@^3.0.5": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "is-arrayish@^0.2.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-async-function@^2.0.0": "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz", "is-bigint@^1.1.0": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-boolean-object@^1.2.1": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.2.tgz", "is-callable@^1.2.7": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "is-core-module@^2.13.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-core-module@^2.15.1": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-core-module@^2.16.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-data-view@^1.0.1": "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz", "is-data-view@^1.0.2": "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz", "is-date-object@^1.0.5": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz", "is-date-object@^1.1.0": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz", "is-docker@^2.0.0": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-docker@^2.1.1": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-finalizationregistry@^1.1.0": "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-generator-fn@^2.0.0": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "is-generator-function@^1.0.10": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz", "is-glob@^4.0.0": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-map@^2.0.3": "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz", "is-module@^1.0.0": "https://registry.npmjs.org/is-module/-/is-module-1.0.0.tgz", "is-number-object@^1.1.1": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-obj@^1.0.1": "https://registry.npmjs.org/is-obj/-/is-obj-1.0.1.tgz", "is-path-inside@^3.0.3": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz", "is-plain-obj@^3.0.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz", "is-plain-object@^2.0.4": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "is-potential-custom-element-name@^1.0.1": "https://registry.npmjs.org/is-potential-custom-element-name/-/is-potential-custom-element-name-1.0.1.tgz", "is-regex@^1.2.1": "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz", "is-regexp@^1.0.0": "https://registry.npmjs.org/is-regexp/-/is-regexp-1.0.0.tgz", "is-root@^2.1.0": "https://registry.npmjs.org/is-root/-/is-root-2.1.0.tgz", "is-set@^2.0.3": "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz", "is-shared-array-buffer@^1.0.4": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz", "is-stream@^2.0.0": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "is-string@^1.0.7": "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz", "is-string@^1.1.1": "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz", "is-symbol@^1.0.4": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz", "is-symbol@^1.1.1": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz", "is-typed-array@^1.1.13": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz", "is-typed-array@^1.1.14": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz", "is-typed-array@^1.1.15": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz", "is-typedarray@^1.0.0": "https://registry.npmjs.org/is-typedarray/-/is-typedarray-1.0.0.tgz", "is-weakmap@^2.0.2": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz", "is-weakref@^1.0.2": "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz", "is-weakref@^1.1.0": "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz", "is-weakset@^2.0.3": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz", "is-wsl@^2.2.0": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "isarray@^2.0.5": "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz", "isarray@~1.0.0": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "isobject@^3.0.1": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "istanbul-lib-coverage@^3.0.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "istanbul-lib-coverage@^3.2.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "istanbul-lib-instrument@^5.0.4": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "istanbul-lib-instrument@^5.1.0": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "istanbul-lib-report@^3.0.0": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "istanbul-lib-source-maps@^4.0.0": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "istanbul-reports@^3.1.3": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "iterator.prototype@^1.1.4": "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz", "jackspeak@^3.1.2": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz", "jake@^10.8.5": "https://registry.npmjs.org/jake/-/jake-10.9.2.tgz", "jest-changed-files@^27.5.1": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-27.5.1.tgz", "jest-circus@^27.5.1": "https://registry.npmjs.org/jest-circus/-/jest-circus-27.5.1.tgz", "jest-cli@^27.5.1": "https://registry.npmjs.org/jest-cli/-/jest-cli-27.5.1.tgz", "jest-config@^27.5.1": "https://registry.npmjs.org/jest-config/-/jest-config-27.5.1.tgz", "jest-diff@^27.5.1": "https://registry.npmjs.org/jest-diff/-/jest-diff-27.5.1.tgz", "jest-docblock@^27.5.1": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-27.5.1.tgz", "jest-each@^27.5.1": "https://registry.npmjs.org/jest-each/-/jest-each-27.5.1.tgz", "jest-environment-jsdom@^27.5.1": "https://registry.npmjs.org/jest-environment-jsdom/-/jest-environment-jsdom-27.5.1.tgz", "jest-environment-node@^27.5.1": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-27.5.1.tgz", "jest-get-type@^27.5.1": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-27.5.1.tgz", "jest-haste-map@^27.5.1": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-27.5.1.tgz", "jest-jasmine2@^27.5.1": "https://registry.npmjs.org/jest-jasmine2/-/jest-jasmine2-27.5.1.tgz", "jest-leak-detector@^27.5.1": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.5.1.tgz", "jest-matcher-utils@^27.5.1": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-27.5.1.tgz", "jest-message-util@^27.5.1": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-27.5.1.tgz", "jest-message-util@^28.1.3": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-28.1.3.tgz", "jest-mock@^27.5.1": "https://registry.npmjs.org/jest-mock/-/jest-mock-27.5.1.tgz", "jest-pnp-resolver@^1.2.2": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "jest-regex-util@^27.5.1": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-27.5.1.tgz", "jest-regex-util@^28.0.0": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-28.0.2.tgz", "jest-resolve-dependencies@^27.5.1": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-27.5.1.tgz", "jest-resolve@^27.4.2": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-27.5.1.tgz", "jest-resolve@^27.5.1": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-27.5.1.tgz", "jest-runner@^27.5.1": "https://registry.npmjs.org/jest-runner/-/jest-runner-27.5.1.tgz", "jest-runtime@^27.5.1": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-27.5.1.tgz", "jest-serializer@^27.5.1": "https://registry.npmjs.org/jest-serializer/-/jest-serializer-27.5.1.tgz", "jest-snapshot@^27.5.1": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-27.5.1.tgz", "jest-util@^27.5.1": "https://registry.npmjs.org/jest-util/-/jest-util-27.5.1.tgz", "jest-util@^28.1.3": "https://registry.npmjs.org/jest-util/-/jest-util-28.1.3.tgz", "jest-validate@^27.5.1": "https://registry.npmjs.org/jest-validate/-/jest-validate-27.5.1.tgz", "jest-watch-typeahead@^1.0.0": "https://registry.npmjs.org/jest-watch-typeahead/-/jest-watch-typeahead-1.1.0.tgz", "jest-watcher@^27.5.1": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-27.5.1.tgz", "jest-watcher@^28.0.0": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-28.1.3.tgz", "jest-worker@^26.2.1": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz", "jest-worker@^27.0.2": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^27.4.5": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^27.5.1": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "jest-worker@^28.0.2": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.3.tgz", "jest@^27.4.3": "https://registry.npmjs.org/jest/-/jest-27.5.1.tgz", "jiti@^1.21.6": "https://registry.npmjs.org/jiti/-/jiti-1.21.7.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@^3.13.1": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "jsdom@^16.6.0": "https://registry.npmjs.org/jsdom/-/jsdom-16.7.0.tgz", "jsesc@^3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "jsesc@~3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz", "json-buffer@3.0.1": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "json-parse-even-better-errors@^2.3.0": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-parse-even-better-errors@^2.3.1": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "json-schema-traverse@^0.4.1": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "json-schema-traverse@^1.0.0": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "json-schema@^0.4.0": "https://registry.npmjs.org/json-schema/-/json-schema-0.4.0.tgz", "json-stable-stringify-without-jsonify@^1.0.1": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "json5@^1.0.2": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "json5@^2.1.2": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "json5@^2.2.0": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "json5@^2.2.3": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "jsonfile@^6.0.1": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "jsonpath@^1.1.1": "https://registry.npmjs.org/jsonpath/-/jsonpath-1.1.1.tgz", "jsonpointer@^5.0.0": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-5.0.1.tgz", "jsx-ast-utils@^2.4.1 || ^3.0.0": "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz", "jsx-ast-utils@^3.3.5": "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz", "keyv@^4.5.3": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "kind-of@^6.0.2": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "kleur@^3.0.3": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "klona@^2.0.4": "https://registry.npmjs.org/klona/-/klona-2.0.6.tgz", "klona@^2.0.5": "https://registry.npmjs.org/klona/-/klona-2.0.6.tgz", "language-subtag-registry@^0.3.20": "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.23.tgz", "language-tags@^1.0.9": "https://registry.npmjs.org/language-tags/-/language-tags-1.0.9.tgz", "launch-editor@^2.6.0": "https://registry.npmjs.org/launch-editor/-/launch-editor-2.10.0.tgz", "leven@^3.1.0": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "levn@^0.4.1": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "levn@~0.3.0": "https://registry.npmjs.org/levn/-/levn-0.3.0.tgz", "lilconfig@^2.0.3": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz", "lilconfig@^3.0.0": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz", "lilconfig@^3.1.3": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz", "lines-and-columns@^1.1.6": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "load-script@^1.0.0": "https://registry.npmjs.org/load-script/-/load-script-1.0.0.tgz", "loader-runner@^4.2.0": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz", "loader-utils@^2.0.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz", "loader-utils@^2.0.4": "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz", "loader-utils@^3.2.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-3.3.1.tgz", "locate-path@^3.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-3.0.0.tgz", "locate-path@^5.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "lodash.camelcase@^4.3.0": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "lodash.debounce@^4.0.8": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.memoize@^4.1.2": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "lodash.merge@^4.6.2": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "lodash.sortby@^4.7.0": "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz", "lodash.uniq@^4.5.0": "https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz", "lodash@^4.17.20": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.17.21": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "lodash@^4.7.0": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "long@^4.0.0": "https://registry.npmjs.org/long/-/long-4.0.0.tgz", "long@^5.0.0": "https://registry.npmjs.org/long/-/long-5.3.2.tgz", "loose-envify@^1.0.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.1.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "loose-envify@^1.4.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "lower-case@^2.0.2": "https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz", "lru-cache@^10.2.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "lru-cache@^5.1.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "lucide-react@^0.518.0": "https://registry.npmjs.org/lucide-react/-/lucide-react-0.518.0.tgz", "lz-string@^1.5.0": "https://registry.npmjs.org/lz-string/-/lz-string-1.5.0.tgz", "magic-string@^0.25.0": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz", "magic-string@^0.25.7": "https://registry.npmjs.org/magic-string/-/magic-string-0.25.9.tgz", "make-dir@^3.0.2": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "make-dir@^3.1.0": "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz", "make-dir@^4.0.0": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz", "makeerror@1.0.12": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "math-intrinsics@^1.1.0": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "mdb-react-ui-kit@^9.0.0": "https://registry.npmjs.org/mdb-react-ui-kit/-/mdb-react-ui-kit-9.0.0.tgz", "mdn-data@2.0.14": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz", "mdn-data@2.0.4": "https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.4.tgz", "media-typer@0.3.0": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "memfs@^3.1.2": "https://registry.npmjs.org/memfs/-/memfs-3.6.0.tgz", "memfs@^3.4.3": "https://registry.npmjs.org/memfs/-/memfs-3.6.0.tgz", "memoize-one@^5.1.1": "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz", "merge-descriptors@1.0.3": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "merge-stream@^2.0.0": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "merge2@^1.3.0": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge2@^1.4.1": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "methods@~1.1.2": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "micromatch@^4.0.2": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.4": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.5": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.8": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "mime-types@^2.1.12": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.27": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.31": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@^2.1.35": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.17": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.24": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.34": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mimic-fn@^2.1.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "min-indent@^1.0.0": "https://registry.npmjs.org/min-indent/-/min-indent-1.0.1.tgz", "mini-css-extract-plugin@^2.4.5": "https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.9.2.tgz", "minimalistic-assert@^1.0.0": "https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "minimatch@^3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.0.5": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.2": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^5.0.1": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "minimatch@^9.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "minimist@^1.2.0": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minimist@^1.2.6": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minipass@^5.0.0 || ^6.0.2 || ^7.0.0": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "mkdirp@~0.5.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "motion-dom@^12.19.0": "https://registry.npmjs.org/motion-dom/-/motion-dom-12.19.0.tgz", "motion-utils@^12.19.0": "https://registry.npmjs.org/motion-utils/-/motion-utils-12.19.0.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.1": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "multicast-dns@^7.2.5": "https://registry.npmjs.org/multicast-dns/-/multicast-dns-7.2.5.tgz", "mz@^2.7.0": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz", "nanoid@^3.3.8": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "natural-compare-lite@^1.4.0": "https://registry.npmjs.org/natural-compare-lite/-/natural-compare-lite-1.4.0.tgz", "natural-compare@^1.4.0": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "negotiator@0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "negotiator@~0.6.4": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz", "neo-async@^2.6.2": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "no-case@^3.0.4": "https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz", "node-fetch@2.6.7": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "node-forge@^1": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "node-int64@^0.4.0": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "normalize-url@^6.0.1": "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz", "npm-run-path@^4.0.1": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "nth-check@^1.0.2": "https://registry.npmjs.org/nth-check/-/nth-check-1.0.2.tgz", "nth-check@^2.0.1": "https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz", "nwsapi@^2.2.0": "https://registry.npmjs.org/nwsapi/-/nwsapi-2.2.20.tgz", "object-assign@^4.0.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-assign@^4.1.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "object-hash@^3.0.0": "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz", "object-inspect@^1.13.3": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "object-keys@^1.1.1": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "object.assign@^4.1.4": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "object.assign@^4.1.7": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "object.entries@^1.1.9": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.9.tgz", "object.fromentries@^2.0.8": "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz", "object.getownpropertydescriptors@^2.1.0": "https://registry.npmjs.org/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.8.tgz", "object.groupby@^1.0.3": "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz", "object.values@^1.1.0": "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz", "object.values@^1.1.6": "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz", "object.values@^1.2.0": "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz", "object.values@^1.2.1": "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz", "obuf@^1.0.0": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "obuf@^1.1.2": "https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz", "on-finished@2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "on-headers@~1.0.2": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "onetime@^5.1.2": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "open@^8.0.9": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "open@^8.4.0": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "optionator@^0.8.1": "https://registry.npmjs.org/optionator/-/optionator-0.8.3.tgz", "optionator@^0.9.3": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz", "own-keys@^1.0.1": "https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz", "p-limit@^2.0.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^2.2.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^3.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-3.0.0.tgz", "p-locate@^4.1.0": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "p-retry@^4.5.0": "https://registry.npmjs.org/p-retry/-/p-retry-4.6.2.tgz", "p-try@^2.0.0": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "package-json-from-dist@^1.0.0": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "param-case@^3.0.4": "https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz", "parent-module@^1.0.0": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "parse-json@^5.0.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parse-json@^5.2.0": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "parse5@6.0.1": "https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz", "parseurl@~1.3.2": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "parseurl@~1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "pascal-case@^3.1.2": "https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz", "path-exists@^3.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-3.0.0.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.0.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-key@^3.1.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^1.11.1": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "path-to-regexp@0.1.12": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "path-type@^4.0.0": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "performance-now@^2.1.0": "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz", "picocolors@^0.2.1": "https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.2": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.3": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "pify@^2.3.0": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "pirates@^4.0.1": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "pirates@^4.0.4": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "pkg-dir@^4.1.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "pkg-dir@^4.2.0": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "pkg-up@^3.1.0": "https://registry.npmjs.org/pkg-up/-/pkg-up-3.1.0.tgz", "possible-typed-array-names@^1.0.0": "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "postcss-attribute-case-insensitive@^5.0.2": "https://registry.npmjs.org/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-5.0.2.tgz", "postcss-browser-comments@^4": "https://registry.npmjs.org/postcss-browser-comments/-/postcss-browser-comments-4.0.0.tgz", "postcss-calc@^8.2.3": "https://registry.npmjs.org/postcss-calc/-/postcss-calc-8.2.4.tgz", "postcss-clamp@^4.1.0": "https://registry.npmjs.org/postcss-clamp/-/postcss-clamp-4.1.0.tgz", "postcss-color-functional-notation@^4.2.4": "https://registry.npmjs.org/postcss-color-functional-notation/-/postcss-color-functional-notation-4.2.4.tgz", "postcss-color-hex-alpha@^8.0.4": "https://registry.npmjs.org/postcss-color-hex-alpha/-/postcss-color-hex-alpha-8.0.4.tgz", "postcss-color-rebeccapurple@^7.1.1": "https://registry.npmjs.org/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-7.1.1.tgz", "postcss-colormin@^5.3.1": "https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-5.3.1.tgz", "postcss-convert-values@^5.1.3": "https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz", "postcss-custom-media@^8.0.2": "https://registry.npmjs.org/postcss-custom-media/-/postcss-custom-media-8.0.2.tgz", "postcss-custom-properties@^12.1.10": "https://registry.npmjs.org/postcss-custom-properties/-/postcss-custom-properties-12.1.11.tgz", "postcss-custom-selectors@^6.0.3": "https://registry.npmjs.org/postcss-custom-selectors/-/postcss-custom-selectors-6.0.3.tgz", "postcss-dir-pseudo-class@^6.0.5": "https://registry.npmjs.org/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-6.0.5.tgz", "postcss-discard-comments@^5.1.2": "https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz", "postcss-discard-duplicates@^5.1.0": "https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz", "postcss-discard-empty@^5.1.1": "https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz", "postcss-discard-overridden@^5.1.0": "https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz", "postcss-double-position-gradients@^3.1.2": "https://registry.npmjs.org/postcss-double-position-gradients/-/postcss-double-position-gradients-3.1.2.tgz", "postcss-env-function@^4.0.6": "https://registry.npmjs.org/postcss-env-function/-/postcss-env-function-4.0.6.tgz", "postcss-flexbugs-fixes@^5.0.2": "https://registry.npmjs.org/postcss-flexbugs-fixes/-/postcss-flexbugs-fixes-5.0.2.tgz", "postcss-focus-visible@^6.0.4": "https://registry.npmjs.org/postcss-focus-visible/-/postcss-focus-visible-6.0.4.tgz", "postcss-focus-within@^5.0.4": "https://registry.npmjs.org/postcss-focus-within/-/postcss-focus-within-5.0.4.tgz", "postcss-font-variant@^5.0.0": "https://registry.npmjs.org/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz", "postcss-gap-properties@^3.0.5": "https://registry.npmjs.org/postcss-gap-properties/-/postcss-gap-properties-3.0.5.tgz", "postcss-image-set-function@^4.0.7": "https://registry.npmjs.org/postcss-image-set-function/-/postcss-image-set-function-4.0.7.tgz", "postcss-import@^15.1.0": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz", "postcss-initial@^4.0.1": "https://registry.npmjs.org/postcss-initial/-/postcss-initial-4.0.1.tgz", "postcss-js@^4.0.1": "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz", "postcss-lab-function@^4.2.1": "https://registry.npmjs.org/postcss-lab-function/-/postcss-lab-function-4.2.1.tgz", "postcss-load-config@^4.0.2": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz", "postcss-loader@^6.2.1": "https://registry.npmjs.org/postcss-loader/-/postcss-loader-6.2.1.tgz", "postcss-logical@^5.0.4": "https://registry.npmjs.org/postcss-logical/-/postcss-logical-5.0.4.tgz", "postcss-media-minmax@^5.0.0": "https://registry.npmjs.org/postcss-media-minmax/-/postcss-media-minmax-5.0.0.tgz", "postcss-merge-longhand@^5.1.7": "https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz", "postcss-merge-rules@^5.1.4": "https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz", "postcss-minify-font-values@^5.1.0": "https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz", "postcss-minify-gradients@^5.1.1": "https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz", "postcss-minify-params@^5.1.4": "https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz", "postcss-minify-selectors@^5.2.1": "https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz", "postcss-modules-extract-imports@^3.1.0": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz", "postcss-modules-local-by-default@^4.0.5": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.2.0.tgz", "postcss-modules-scope@^3.2.0": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz", "postcss-modules-values@^4.0.0": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz", "postcss-nested@^6.2.0": "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz", "postcss-nesting@^10.2.0": "https://registry.npmjs.org/postcss-nesting/-/postcss-nesting-10.2.0.tgz", "postcss-normalize-charset@^5.1.0": "https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz", "postcss-normalize-display-values@^5.1.0": "https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz", "postcss-normalize-positions@^5.1.1": "https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz", "postcss-normalize-repeat-style@^5.1.1": "https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz", "postcss-normalize-string@^5.1.0": "https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz", "postcss-normalize-timing-functions@^5.1.0": "https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz", "postcss-normalize-unicode@^5.1.1": "https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz", "postcss-normalize-url@^5.1.0": "https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz", "postcss-normalize-whitespace@^5.1.1": "https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz", "postcss-normalize@^10.0.1": "https://registry.npmjs.org/postcss-normalize/-/postcss-normalize-10.0.1.tgz", "postcss-opacity-percentage@^1.1.2": "https://registry.npmjs.org/postcss-opacity-percentage/-/postcss-opacity-percentage-1.1.3.tgz", "postcss-ordered-values@^5.1.3": "https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz", "postcss-overflow-shorthand@^3.0.4": "https://registry.npmjs.org/postcss-overflow-shorthand/-/postcss-overflow-shorthand-3.0.4.tgz", "postcss-page-break@^3.0.4": "https://registry.npmjs.org/postcss-page-break/-/postcss-page-break-3.0.4.tgz", "postcss-place@^7.0.5": "https://registry.npmjs.org/postcss-place/-/postcss-place-7.0.5.tgz", "postcss-preset-env@^7.0.1": "https://registry.npmjs.org/postcss-preset-env/-/postcss-preset-env-7.8.3.tgz", "postcss-pseudo-class-any-link@^7.1.6": "https://registry.npmjs.org/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-7.1.6.tgz", "postcss-reduce-initial@^5.1.2": "https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz", "postcss-reduce-transforms@^5.1.0": "https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz", "postcss-replace-overflow-wrap@^4.0.0": "https://registry.npmjs.org/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz", "postcss-selector-not@^6.0.1": "https://registry.npmjs.org/postcss-selector-not/-/postcss-selector-not-6.0.1.tgz", "postcss-selector-parser@^6.0.10": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.4": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.5": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.9": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.1.1": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.1.2": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^7.0.0": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-7.1.0.tgz", "postcss-svgo@^5.1.0": "https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-5.1.0.tgz", "postcss-unique-selectors@^5.1.1": "https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz", "postcss-value-parser@^4.0.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.1.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^7.0.35": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "postcss@^8.3.5": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "postcss@^8.4.33": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "postcss@^8.4.4": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "postcss@^8.4.47": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "prelude-ls@^1.2.1": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "prelude-ls@~1.1.2": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.1.2.tgz", "pretty-bytes@^5.3.0": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz", "pretty-bytes@^5.4.1": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz", "pretty-error@^4.0.0": "https://registry.npmjs.org/pretty-error/-/pretty-error-4.0.0.tgz", "pretty-format@^27.0.2": "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz", "pretty-format@^27.5.1": "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz", "pretty-format@^28.1.3": "https://registry.npmjs.org/pretty-format/-/pretty-format-28.1.3.tgz", "process-nextick-args@~2.0.0": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "promise@^8.1.0": "https://registry.npmjs.org/promise/-/promise-8.3.0.tgz", "prompts@^2.0.1": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "prompts@^2.4.2": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "prop-types-extra@^1.1.0": "https://registry.npmjs.org/prop-types-extra/-/prop-types-extra-1.1.1.tgz", "prop-types@^15.6.2": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.7.2": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "prop-types@^15.8.1": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "protobufjs@^6.11.3": "https://registry.npmjs.org/protobufjs/-/protobufjs-6.11.4.tgz", "protobufjs@^7.2.5": "https://registry.npmjs.org/protobufjs/-/protobufjs-7.5.3.tgz", "proxy-addr@~2.0.7": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "proxy-from-env@^1.1.0": "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz", "psl@^1.1.33": "https://registry.npmjs.org/psl/-/psl-1.15.0.tgz", "punycode@^2.1.0": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "punycode@^2.1.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "punycode@^2.3.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "q@^1.1.2": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "qs@6.13.0": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz", "querystringify@^2.1.1": "https://registry.npmjs.org/querystringify/-/querystringify-2.2.0.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "raf@^3.4.1": "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz", "randombytes@^2.1.0": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "range-parser@^1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "range-parser@~1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "raw-body@2.5.2": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "react-app-polyfill@^3.0.0": "https://registry.npmjs.org/react-app-polyfill/-/react-app-polyfill-3.0.0.tgz", "react-bootstrap@^2.10.9": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.9.tgz", "react-dev-utils@^12.0.1": "https://registry.npmjs.org/react-dev-utils/-/react-dev-utils-12.0.1.tgz", "react-dom@^18.0.0": "https://registry.npmjs.org/react-dom/-/react-dom-18.3.1.tgz", "react-error-overlay@^6.0.11": "https://registry.npmjs.org/react-error-overlay/-/react-error-overlay-6.1.0.tgz", "react-fast-compare@^3.0.1": "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz", "react-fast-compare@^3.1.1": "https://registry.npmjs.org/react-fast-compare/-/react-fast-compare-3.2.2.tgz", "react-helmet@^6.1.0": "https://registry.npmjs.org/react-helmet/-/react-helmet-6.1.0.tgz", "react-i18next@^11.18.6": "https://registry.npmjs.org/react-i18next/-/react-i18next-11.18.6.tgz", "react-icons@^5.5.0": "https://registry.npmjs.org/react-icons/-/react-icons-5.5.0.tgz", "react-is@^16.13.1": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^16.3.2": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^16.7.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^17.0.1": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "react-is@^18.0.0": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "react-is@^18.3.1": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "react-is@^19.1.0": "https://registry.npmjs.org/react-is/-/react-is-19.1.0.tgz", "react-lifecycles-compat@^3.0.4": "https://registry.npmjs.org/react-lifecycles-compat/-/react-lifecycles-compat-3.0.4.tgz", "react-player@^2.16.0": "https://registry.npmjs.org/react-player/-/react-player-2.16.0.tgz", "react-popper@2.3.0": "https://registry.npmjs.org/react-popper/-/react-popper-2.3.0.tgz", "react-refresh@^0.11.0": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.11.0.tgz", "react-remove-scroll-bar@^2.3.7": "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz", "react-remove-scroll@^2.6.3": "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.7.1.tgz", "react-router-dom@^7.5.2": "https://registry.npmjs.org/react-router-dom/-/react-router-dom-7.5.2.tgz", "react-router@7.5.2": "https://registry.npmjs.org/react-router/-/react-router-7.5.2.tgz", "react-scripts@^5.0.1": "https://registry.npmjs.org/react-scripts/-/react-scripts-5.0.1.tgz", "react-side-effect@^2.1.0": "https://registry.npmjs.org/react-side-effect/-/react-side-effect-2.1.2.tgz", "react-smooth@^4.0.4": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.4.tgz", "react-style-singleton@^2.2.2": "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz", "react-style-singleton@^2.2.3": "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz", "react-toastify@^11.0.5": "https://registry.npmjs.org/react-toastify/-/react-toastify-11.0.5.tgz", "react-transition-group@^4.4.5": "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz", "react@^18.0.0": "https://registry.npmjs.org/react/-/react-18.3.1.tgz", "read-cache@^1.0.0": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz", "readable-stream@^2.0.1": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "readable-stream@^3.0.6": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "recharts-scale@^0.4.4": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.5.tgz", "recharts@^2.15.3": "https://registry.npmjs.org/recharts/-/recharts-2.15.3.tgz", "rechoir@^0.8.0": "https://registry.npmjs.org/rechoir/-/rechoir-0.8.0.tgz", "recursive-readdir@^2.2.2": "https://registry.npmjs.org/recursive-readdir/-/recursive-readdir-2.2.3.tgz", "redent@^3.0.0": "https://registry.npmjs.org/redent/-/redent-3.0.0.tgz", "reflect.getprototypeof@^1.0.6": "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz", "reflect.getprototypeof@^1.0.9": "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz", "regenerate-unicode-properties@^10.2.0": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz", "regenerate@^1.4.2": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz", "regenerator-runtime@^0.13.9": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regenerator-transform@^0.15.2": "https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.2.tgz", "regex-parser@^2.2.11": "https://registry.npmjs.org/regex-parser/-/regex-parser-2.3.1.tgz", "regexp.prototype.flags@^1.5.3": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz", "regexpu-core@^6.2.0": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz", "regjsgen@^0.8.0": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz", "regjsparser@^0.12.0": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz", "relateurl@^0.2.7": "https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz", "renderkid@^3.0.0": "https://registry.npmjs.org/renderkid/-/renderkid-3.0.0.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "requires-port@^1.0.0": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "reselect@^5.1.1": "https://registry.npmjs.org/reselect/-/reselect-5.1.1.tgz", "resolve-cwd@^3.0.0": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "resolve-from@^4.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "resolve-url-loader@^4.0.0": "https://registry.npmjs.org/resolve-url-loader/-/resolve-url-loader-4.0.0.tgz", "resolve.exports@^1.1.0": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-1.1.1.tgz", "resolve@^1.1.7": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.14.2": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.19.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.20.0": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.4": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.8": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^2.0.0-next.5": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz", "retry@^0.13.1": "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz", "reusify@^1.0.4": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "rimraf@^3.0.0": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rimraf@^3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "rollup-plugin-terser@^7.0.0": "https://registry.npmjs.org/rollup-plugin-terser/-/rollup-plugin-terser-7.0.2.tgz", "rollup@^2.43.1": "https://registry.npmjs.org/rollup/-/rollup-2.79.2.tgz", "run-parallel@^1.1.9": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "safe-array-concat@^1.1.2": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz", "safe-array-concat@^1.1.3": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@>=5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@^5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-buffer@~5.1.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.1.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "safe-buffer@~5.2.0": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safe-push-apply@^1.0.0": "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz", "safe-regex-test@^1.0.3": "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "safe-regex-test@^1.1.0": "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "safer-buffer@>= 2.1.2 < 3.0.0": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "sanitize.css@*": "https://registry.npmjs.org/sanitize.css/-/sanitize.css-13.0.0.tgz", "sass-loader@^12.3.0": "https://registry.npmjs.org/sass-loader/-/sass-loader-12.6.0.tgz", "sax@~1.2.4": "https://registry.npmjs.org/sax/-/sax-1.2.4.tgz", "saxes@^5.0.1": "https://registry.npmjs.org/saxes/-/saxes-5.0.1.tgz", "scheduler@^0.23.2": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz", "schema-utils@2.7.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.0.tgz", "schema-utils@^2.6.5": "https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz", "schema-utils@^3.0.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "schema-utils@^4.0.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "schema-utils@^4.2.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "schema-utils@^4.3.0": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.2.tgz", "select-hose@^2.0.0": "https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz", "selfsigned@^2.1.1": "https://registry.npmjs.org/selfsigned/-/selfsigned-2.4.1.tgz", "semver@^6.0.0": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^6.3.0": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^6.3.1": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^7.3.2": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "semver@^7.3.5": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "semver@^7.3.7": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "semver@^7.5.3": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.7.1.tgz", "send@0.19.0": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "serialize-javascript@^4.0.0": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-4.0.0.tgz", "serialize-javascript@^6.0.0": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "serialize-javascript@^6.0.2": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "serve-index@^1.9.1": "https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz", "serve-static@1.16.2": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "set-cookie-parser@^2.6.0": "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz", "set-function-length@^1.2.2": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz", "set-function-name@^2.0.2": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz", "set-proto@^1.0.0": "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz", "setprototypeof@1.1.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "shallow-clone@^3.0.0": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz", "shebang-command@^2.0.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "shell-quote@^1.7.3": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.2.tgz", "shell-quote@^1.8.1": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.2.tgz", "side-channel-list@^1.0.0": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "side-channel-map@^1.0.1": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "side-channel-weakmap@^1.0.2": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "side-channel@^1.0.6": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "side-channel@^1.1.0": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "signal-exit@^3.0.2": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.3": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^4.0.1": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "sisteransi@^1.0.5": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "slash@^4.0.0": "https://registry.npmjs.org/slash/-/slash-4.0.0.tgz", "socket.io-client@^4.8.1": "https://registry.npmjs.org/socket.io-client/-/socket.io-client-4.8.1.tgz", "socket.io-parser@~4.2.4": "https://registry.npmjs.org/socket.io-parser/-/socket.io-parser-4.2.4.tgz", "sockjs@^0.3.24": "https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz", "source-list-map@^2.0.0": "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz", "source-list-map@^2.0.1": "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz", "source-map-js@^1.0.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-loader@^3.0.0": "https://registry.npmjs.org/source-map-loader/-/source-map-loader-3.0.2.tgz", "source-map-support@^0.5.6": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.20": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map@0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.5.7": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.7.3": "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz", "source-map@^0.8.0-beta.0": "https://registry.npmjs.org/source-map/-/source-map-0.8.0-beta.0.tgz", "source-map@~0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@~0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "sourcemap-codec@^1.4.8": "https://registry.npmjs.org/sourcemap-codec/-/sourcemap-codec-1.4.8.tgz", "spdy-transport@^3.0.0": "https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz", "spdy@^4.0.2": "https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz", "sprintf-js@~1.0.2": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "stable@^0.1.8": "https://registry.npmjs.org/stable/-/stable-0.1.8.tgz", "stack-utils@^2.0.3": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "stackframe@^1.3.4": "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz", "static-eval@2.0.2": "https://registry.npmjs.org/static-eval/-/static-eval-2.0.2.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "statuses@>= 1.4.0 < 2": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "string-length@^4.0.1": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz", "string-length@^5.0.1": "https://registry.npmjs.org/string-length/-/string-length-5.0.1.tgz", "string-natural-compare@^3.0.1": "https://registry.npmjs.org/string-natural-compare/-/string-natural-compare-3.0.1.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string.prototype.includes@^2.0.1": "https://registry.npmjs.org/string.prototype.includes/-/string.prototype.includes-2.0.1.tgz", "string.prototype.matchall@^4.0.12": "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz", "string.prototype.matchall@^4.0.6": "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz", "string.prototype.repeat@^1.0.0": "https://registry.npmjs.org/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz", "string.prototype.trim@^1.2.10": "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz", "string.prototype.trimend@^1.0.8": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz", "string.prototype.trimend@^1.0.9": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz", "string.prototype.trimstart@^1.0.8": "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz", "string_decoder@^1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "string_decoder@~1.1.1": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "stringify-object@^3.3.0": "https://registry.npmjs.org/stringify-object/-/stringify-object-3.3.0.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-bom@^3.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "strip-bom@^4.0.0": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz", "strip-comments@^2.0.1": "https://registry.npmjs.org/strip-comments/-/strip-comments-2.0.1.tgz", "strip-final-newline@^2.0.0": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "strip-indent@^3.0.0": "https://registry.npmjs.org/strip-indent/-/strip-indent-3.0.0.tgz", "strip-json-comments@^3.1.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "style-loader@^3.3.1": "https://registry.npmjs.org/style-loader/-/style-loader-3.3.4.tgz", "stylehacks@^5.1.1": "https://registry.npmjs.org/stylehacks/-/stylehacks-5.1.1.tgz", "stylis@4.2.0": "https://registry.npmjs.org/stylis/-/stylis-4.2.0.tgz", "sucrase@^3.35.0": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^7.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "supports-hyperlinks@^2.0.0": "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "svg-parser@^2.0.2": "https://registry.npmjs.org/svg-parser/-/svg-parser-2.0.4.tgz", "svgo@^1.2.2": "https://registry.npmjs.org/svgo/-/svgo-1.3.2.tgz", "svgo@^2.7.0": "https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz", "symbol-tree@^3.2.4": "https://registry.npmjs.org/symbol-tree/-/symbol-tree-3.2.4.tgz", "tailwind-merge@^3.3.1": "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.3.1.tgz", "tailwindcss@^3.0.2": "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.17.tgz", "tapable@^1.0.0": "https://registry.npmjs.org/tapable/-/tapable-1.1.3.tgz", "tapable@^2.0.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "tapable@^2.1.1": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "tapable@^2.2.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "tapable@^2.2.1": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "temp-dir@^2.0.0": "https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz", "tempy@^0.6.0": "https://registry.npmjs.org/tempy/-/tempy-0.6.0.tgz", "terminal-link@^2.0.0": "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz", "terser-webpack-plugin@^5.2.5": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz", "terser-webpack-plugin@^5.3.11": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.14.tgz", "terser@^5.0.0": "https://registry.npmjs.org/terser/-/terser-5.39.0.tgz", "terser@^5.10.0": "https://registry.npmjs.org/terser/-/terser-5.39.0.tgz", "terser@^5.31.1": "https://registry.npmjs.org/terser/-/terser-5.39.0.tgz", "test-exclude@^6.0.0": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "text-segmentation@^1.0.3": "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz", "text-table@^0.2.0": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "thenify-all@^1.0.0": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz", "throat@^6.0.1": "https://registry.npmjs.org/throat/-/throat-6.0.2.tgz", "thunky@^1.0.2": "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz", "tiny-invariant@^1.3.1": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz", "tmpl@1.0.5": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "tough-cookie@^4.0.0": "https://registry.npmjs.org/tough-cookie/-/tough-cookie-4.1.4.tgz", "tr46@^1.0.1": "https://registry.npmjs.org/tr46/-/tr46-1.0.1.tgz", "tr46@^2.1.0": "https://registry.npmjs.org/tr46/-/tr46-2.1.0.tgz", "tr46@~0.0.3": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "tryer@^1.0.1": "https://registry.npmjs.org/tryer/-/tryer-1.0.1.tgz", "ts-interface-checker@^0.1.9": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "tsconfig-paths@^3.15.0": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz", "tslib@^1.8.1": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "tslib@^2.0.0": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "tslib@^2.0.3": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "tslib@^2.1.0": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "tslib@^2.4.0": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "tslib@^2.8.0": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "tsutils@^3.21.0": "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz", "turbo-stream@2.4.0": "https://registry.npmjs.org/turbo-stream/-/turbo-stream-2.4.0.tgz", "type-check@^0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-check@~0.3.2": "https://registry.npmjs.org/type-check/-/type-check-0.3.2.tgz", "type-check@~0.4.0": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "type-detect@4.0.8": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "type-fest@^0.16.0": "https://registry.npmjs.org/type-fest/-/type-fest-0.16.0.tgz", "type-fest@^0.20.2": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "type-fest@^0.21.3": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "type-is@~1.6.18": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "typed-array-buffer@^1.0.3": "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz", "typed-array-byte-length@^1.0.3": "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz", "typed-array-byte-offset@^1.0.4": "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz", "typed-array-length@^1.0.7": "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz", "typedarray-to-buffer@^3.1.5": "https://registry.npmjs.org/typedarray-to-buffer/-/typedarray-to-buffer-3.1.5.tgz", "typescript@^4.9.5": "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz", "unbox-primitive@^1.1.0": "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz", "uncontrollable@^7.2.1": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.2.1.tgz", "uncontrollable@^8.0.4": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-8.0.4.tgz", "underscore@1.12.1": "https://registry.npmjs.org/underscore/-/underscore-1.12.1.tgz", "undici-types@~6.21.0": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "unicode-canonical-property-names-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "unicode-match-property-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "unicode-match-property-value-ecmascript@^2.1.0": "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz", "unicode-property-aliases-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "unique-string@^2.0.0": "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz", "universalify@^0.2.0": "https://registry.npmjs.org/universalify/-/universalify-0.2.0.tgz", "universalify@^2.0.0": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "unpipe@1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unquote@~1.1.1": "https://registry.npmjs.org/unquote/-/unquote-1.1.1.tgz", "upath@^1.2.0": "https://registry.npmjs.org/upath/-/upath-1.2.0.tgz", "update-browserslist-db@^1.1.1": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "uri-js@^4.2.2": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "url-parse@^1.5.3": "https://registry.npmjs.org/url-parse/-/url-parse-1.5.10.tgz", "use-callback-ref@^1.3.3": "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz", "use-sidecar@^1.1.3": "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz", "use-sync-external-store@^1.5.0": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "util-deprecate@^1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@^1.0.2": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util-deprecate@~1.0.1": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "util.promisify@~1.0.0": "https://registry.npmjs.org/util.promisify/-/util.promisify-1.0.1.tgz", "utila@~0.4": "https://registry.npmjs.org/utila/-/utila-0.4.0.tgz", "utils-merge@1.0.1": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "utrie@^1.0.2": "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz", "uuid@^8.3.2": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "v8-to-istanbul@^8.1.0": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-8.1.1.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "victory-vendor@^36.6.8": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2.tgz", "void-elements@3.1.0": "https://registry.npmjs.org/void-elements/-/void-elements-3.1.0.tgz", "w3c-hr-time@^1.0.2": "https://registry.npmjs.org/w3c-hr-time/-/w3c-hr-time-1.0.2.tgz", "w3c-xmlserializer@^2.0.0": "https://registry.npmjs.org/w3c-xmlserializer/-/w3c-xmlserializer-2.0.0.tgz", "walker@^1.0.7": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "warning@^4.0.0": "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz", "warning@^4.0.2": "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz", "warning@^4.0.3": "https://registry.npmjs.org/warning/-/warning-4.0.3.tgz", "watchpack@^2.4.1": "https://registry.npmjs.org/watchpack/-/watchpack-2.4.2.tgz", "wbuf@^1.1.0": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "wbuf@^1.7.3": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "web-vitals@^2.1.4": "https://registry.npmjs.org/web-vitals/-/web-vitals-2.1.4.tgz", "webidl-conversions@^3.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "webidl-conversions@^4.0.2": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-4.0.2.tgz", "webidl-conversions@^5.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz", "webidl-conversions@^6.1.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-6.1.0.tgz", "webpack-cli@^6.0.1": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-6.0.1.tgz", "webpack-dev-middleware@^5.3.4": "https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-5.3.4.tgz", "webpack-dev-server@^4.6.0": "https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.15.2.tgz", "webpack-manifest-plugin@^4.0.2": "https://registry.npmjs.org/webpack-manifest-plugin/-/webpack-manifest-plugin-4.1.1.tgz", "webpack-merge@^6.0.1": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-6.0.1.tgz", "webpack-sources@^1.4.3": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-1.4.3.tgz", "webpack-sources@^2.2.0": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-2.3.1.tgz", "webpack-sources@^3.2.3": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz", "webpack@^5.64.4": "https://registry.npmjs.org/webpack/-/webpack-5.99.6.tgz", "webpack@^5.99.6": "https://registry.npmjs.org/webpack/-/webpack-5.99.6.tgz", "websocket-driver@>=0.5.1": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-driver@^0.7.4": "https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz", "websocket-extensions@>=0.1.1": "https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz", "whatwg-encoding@^1.0.5": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-1.0.5.tgz", "whatwg-fetch@^3.6.2": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz", "whatwg-mimetype@^2.3.0": "https://registry.npmjs.org/whatwg-mimetype/-/whatwg-mimetype-2.3.0.tgz", "whatwg-url@^5.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "whatwg-url@^7.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-7.1.0.tgz", "whatwg-url@^8.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz", "whatwg-url@^8.5.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-8.7.0.tgz", "which-boxed-primitive@^1.1.0": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "which-boxed-primitive@^1.1.1": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "which-builtin-type@^1.2.1": "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz", "which-collection@^1.0.2": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz", "which-typed-array@^1.1.16": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz", "which-typed-array@^1.1.18": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz", "which@^1.3.1": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "which@^2.0.1": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "wildcard@^2.0.1": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz", "word-wrap@^1.2.5": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "word-wrap@~1.2.3": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "workbox-background-sync@6.6.1": "https://registry.npmjs.org/workbox-background-sync/-/workbox-background-sync-6.6.1.tgz", "workbox-broadcast-update@6.6.1": "https://registry.npmjs.org/workbox-broadcast-update/-/workbox-broadcast-update-6.6.1.tgz", "workbox-build@6.6.1": "https://registry.npmjs.org/workbox-build/-/workbox-build-6.6.1.tgz", "workbox-cacheable-response@6.6.1": "https://registry.npmjs.org/workbox-cacheable-response/-/workbox-cacheable-response-6.6.1.tgz", "workbox-core@6.6.1": "https://registry.npmjs.org/workbox-core/-/workbox-core-6.6.1.tgz", "workbox-expiration@6.6.1": "https://registry.npmjs.org/workbox-expiration/-/workbox-expiration-6.6.1.tgz", "workbox-google-analytics@6.6.1": "https://registry.npmjs.org/workbox-google-analytics/-/workbox-google-analytics-6.6.1.tgz", "workbox-navigation-preload@6.6.1": "https://registry.npmjs.org/workbox-navigation-preload/-/workbox-navigation-preload-6.6.1.tgz", "workbox-precaching@6.6.1": "https://registry.npmjs.org/workbox-precaching/-/workbox-precaching-6.6.1.tgz", "workbox-range-requests@6.6.1": "https://registry.npmjs.org/workbox-range-requests/-/workbox-range-requests-6.6.1.tgz", "workbox-recipes@6.6.1": "https://registry.npmjs.org/workbox-recipes/-/workbox-recipes-6.6.1.tgz", "workbox-routing@6.6.1": "https://registry.npmjs.org/workbox-routing/-/workbox-routing-6.6.1.tgz", "workbox-strategies@6.6.1": "https://registry.npmjs.org/workbox-strategies/-/workbox-strategies-6.6.1.tgz", "workbox-streams@6.6.1": "https://registry.npmjs.org/workbox-streams/-/workbox-streams-6.6.1.tgz", "workbox-sw@6.6.1": "https://registry.npmjs.org/workbox-sw/-/workbox-sw-6.6.1.tgz", "workbox-webpack-plugin@^6.4.1": "https://registry.npmjs.org/workbox-webpack-plugin/-/workbox-webpack-plugin-6.6.1.tgz", "workbox-window@6.6.1": "https://registry.npmjs.org/workbox-window/-/workbox-window-6.6.1.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "write-file-atomic@^3.0.0": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-3.0.3.tgz", "ws@^7.4.6": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "ws@^8.13.0": "https://registry.npmjs.org/ws/-/ws-8.18.1.tgz", "ws@~8.17.1": "https://registry.npmjs.org/ws/-/ws-8.17.1.tgz", "xml-name-validator@^3.0.0": "https://registry.npmjs.org/xml-name-validator/-/xml-name-validator-3.0.0.tgz", "xmlchars@^2.2.0": "https://registry.npmjs.org/xmlchars/-/xmlchars-2.2.0.tgz", "xmlhttprequest-ssl@~2.1.1": "https://registry.npmjs.org/xmlhttprequest-ssl/-/xmlhttprequest-ssl-2.1.2.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yallist@^3.0.2": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "yaml@^1.10.0": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yaml@^1.10.2": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yaml@^1.7.2": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz", "yaml@^2.3.4": "https://registry.npmjs.org/yaml/-/yaml-2.7.1.tgz", "yargs-parser@^20.2.2": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "yargs-parser@^21.1.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@^16.2.0": "https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz", "yargs@^17.7.2": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "yocto-queue@^0.1.0": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"}, "files": [], "artifacts": {}}