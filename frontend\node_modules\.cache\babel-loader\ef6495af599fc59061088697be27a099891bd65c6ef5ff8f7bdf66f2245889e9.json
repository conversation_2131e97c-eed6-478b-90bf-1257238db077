{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\components\\\\Session2ChatPopup.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Paper, Typography, Stack, TextField, IconButton, Button, Avatar } from \"@mui/material\";\nimport AddPhotoAlternateIcon from \"@mui/icons-material/AddPhotoAlternate\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SOCKET_URL = \"http://localhost:8000\";\nexport default function UnifiedSessionChatPopup({\n  user\n}) {\n  _s();\n  // const globalSocketRef = useRef(null);\n\n  const [generalChatMessages, setGeneralChatMessages] = useState([]);\n  const generalSocketRef = useRef(null);\n\n  // Which chat is active: \"session\" or \"seance\"\n  const [selectedTab, setSelectedTab] = useState(\"session\");\n\n  // Session2 data\n  const [session2s, setSession2s] = useState([]); // All sessions user is in\n  const [session2Id, setSession2Id] = useState(null);\n\n  // Seance data\n  const [seances, setSeances] = useState([]); // All seances for current session\n  const [seanceId, setSeanceId] = useState(null);\n\n  // Chat states (messages, input, etc)\n  const [sessionChatMessages, setSessionChatMessages] = useState([]);\n  const [seanceChatMessages, setSeanceChatMessages] = useState([]);\n  const [newMsg, setNewMsg] = useState(\"\");\n  const [newFile, setNewFile] = useState(null);\n  const [showEmoji, setShowEmoji] = useState(false);\n  const [showTooltip, setShowTooltip] = useState(false);\n  const chatBottomRef = useRef();\n  const fileInputRef = useRef();\n  const sessionSocketRef = useRef(null);\n  const seanceSocketRef = useRef(null);\n\n  // UI popup\n  const [open, setOpen] = useState(false);\n  useEffect(() => {\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [sessionChatMessages, seanceChatMessages, generalChatMessages, selectedTab, open]);\n\n  //genralechat \n  useEffect(() => {\n    // Only connect once\n    if (generalSocketRef.current) {\n      generalSocketRef.current.disconnect();\n    }\n    const s = io(`${SOCKET_URL}/general-chat`, {\n      transports: [\"websocket\"]\n    });\n    generalSocketRef.current = s;\n\n    // Fetch messages\n    s.emit(\"fetchGeneralMessages\");\n    s.on(\"generalMessages\", msgs => setGeneralChatMessages(msgs));\n    s.on(\"newGeneralMessage\", msg => setGeneralChatMessages(prev => [...prev, msg]));\n    s.on(\"deleteGeneralMessage\", ({\n      id\n    }) => setGeneralChatMessages(prev => prev.filter(m => m.id !== id)));\n    return () => s.disconnect();\n  }, []); // []: only once\n\n  // Fetch session2s for the user\n  useEffect(() => {\n    if (!(user !== null && user !== void 0 && user.id)) return;\n    fetch(`http://localhost:8000/users/${user.id}/sessions2`).then(res => res.json()).then(data => {\n      var _sessions$0$id, _sessions$;\n      // Normalize (handles both array of userSession2 or array of session2)\n      const sessions = data.map(s => {\n        var _s$session;\n        return s.session2 ? {\n          id: s.session2.id,\n          name: s.session2.name\n        } : s.session2Id ? {\n          id: s.session2Id,\n          name: ((_s$session = s.session2) === null || _s$session === void 0 ? void 0 : _s$session.name) || \"\"\n        } : s;\n      });\n      setSession2s(sessions);\n      setSession2Id((_sessions$0$id = (_sessions$ = sessions[0]) === null || _sessions$ === void 0 ? void 0 : _sessions$.id) !== null && _sessions$0$id !== void 0 ? _sessions$0$id : null);\n    });\n  }, [user === null || user === void 0 ? void 0 : user.id]);\n\n  // Fetch seances for current session2\n  useEffect(() => {\n    if (!session2Id) {\n      setSeances([]);\n      setSeanceId(null);\n      return;\n    }\n    fetch(`http://localhost:8000/seance-formateur/session/${session2Id}`).then(res => res.json()).then(data => {\n      var _data$0$id, _data$;\n      setSeances(data);\n      setSeanceId((_data$0$id = (_data$ = data[0]) === null || _data$ === void 0 ? void 0 : _data$.id) !== null && _data$0$id !== void 0 ? _data$0$id : null); // pick first as default, or let user choose if multiple\n    });\n  }, [session2Id]);\n\n  // Fetch messages for both chats\n  useEffect(() => {\n    if (session2Id) {\n      fetch(`http://localhost:8000/session2-chat-messages/${session2Id}`).then(res => res.json()).then(msgs => setSessionChatMessages(msgs));\n    }\n    if (seanceId) {\n      fetch(`http://localhost:8000/chat-messages/${seanceId}`).then(res => res.json()).then(msgs => setSeanceChatMessages(msgs));\n    }\n  }, [session2Id, seanceId]);\n\n  // Session chat socket\n  useEffect(() => {\n    if (!session2Id) return;\n    if (sessionSocketRef.current) {\n      sessionSocketRef.current.emit(\"leaveSession2\", {\n        session2Id: Number(session2Id)\n      });\n      sessionSocketRef.current.disconnect();\n    }\n    const s = io(SOCKET_URL, {\n      transports: [\"websocket\"]\n    });\n    sessionSocketRef.current = s;\n    s.emit(\"joinSession2\", {\n      session2Id: Number(session2Id)\n    });\n    s.on(\"newSession2Message\", msg => setSessionChatMessages(prev => [...prev, msg]));\n    s.on(\"deleteSession2Message\", payload => setSessionChatMessages(prev => prev.filter(m => m.id !== payload.id)));\n    s.on(\"clearSession2Messages\", () => setSessionChatMessages([]));\n    return () => {\n      s.emit(\"leaveSession2\", {\n        session2Id: Number(session2Id)\n      });\n      s.disconnect();\n    };\n  }, [session2Id]);\n\n  // Seance chat socket\n  useEffect(() => {\n    if (!seanceId) return;\n    if (seanceSocketRef.current) {\n      seanceSocketRef.current.emit(\"leaveSeance\", {\n        seanceId: Number(seanceId)\n      });\n      seanceSocketRef.current.disconnect();\n    }\n    const s = io(SOCKET_URL, {\n      transports: [\"websocket\"]\n    });\n    seanceSocketRef.current = s;\n    s.emit(\"joinSeance\", {\n      seanceId: Number(seanceId)\n    });\n    s.on(\"newSeanceMessage\", msg => setSeanceChatMessages(prev => [...prev, msg]));\n    s.on(\"deleteSeanceMessage\", payload => setSeanceChatMessages(prev => prev.filter(m => m.id !== payload.id)));\n    s.on(\"clearSeanceMessages\", () => setSeanceChatMessages([]));\n    return () => {\n      s.emit(\"leaveSeance\", {\n        seanceId: Number(seanceId)\n      });\n      s.disconnect();\n    };\n  }, [seanceId]);\n\n  // Scroll to bottom\n  useEffect(() => {\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  }, [sessionChatMessages, seanceChatMessages, selectedTab, open]);\n\n  // Tab switch resets input\n  useEffect(() => {\n    setNewMsg(\"\");\n    setNewFile(null);\n    setShowEmoji(false);\n  }, [selectedTab, session2Id, seanceId]);\n\n  // Emoji\n  const handleEmoji = e => {\n    setNewMsg(prev => prev + e.emoji);\n    setShowEmoji(false);\n  };\n\n  // Send message (text/file) for current tab\n  const handleChatSend = async () => {\n    let socket;\n    if (selectedTab === \"session\") socket = sessionSocketRef.current;else if (selectedTab === \"seance\") socket = seanceSocketRef.current;else if (selectedTab === \"general\") socket = generalSocketRef.current;\n    if (!socket) return;\n\n    // File message\n    if (newFile) {\n      const formData = new FormData();\n      formData.append(\"file\", newFile);\n      let uploadUrl = \"\";\n      if (selectedTab === \"general\") {\n        uploadUrl = \"http://localhost:8000/general-chat-messages/upload-chat\";\n      } else if (selectedTab === \"session\") {\n        formData.append(\"session2Id\", session2Id);\n        uploadUrl = \"http://localhost:8000/session2-chat-messages/upload-chat\";\n      } else if (selectedTab === \"seance\") {\n        formData.append(\"seanceId\", seanceId);\n        uploadUrl = \"http://localhost:8000/chat-messages/upload-chat\";\n      }\n      try {\n        const res = await fetch(uploadUrl, {\n          method: \"POST\",\n          body: formData\n        });\n        const fileData = await res.json();\n        if (selectedTab === \"general\") {\n          socket.emit(\"sendGeneralMessage\", {\n            content: fileData.fileUrl,\n            type: fileData.fileType || \"file\",\n            senderId: user.id\n          });\n        } else if (selectedTab === \"session\") {\n          socket.emit(\"sendSession2Message\", {\n            content: fileData.fileUrl,\n            type: fileData.fileType || \"file\",\n            session2Id: Number(session2Id),\n            senderId: user.id\n          });\n        } else {\n          socket.emit(\"sendSeanceMessage\", {\n            content: fileData.fileUrl,\n            type: fileData.fileType || \"file\",\n            seanceId: Number(seanceId),\n            senderId: user.id\n          });\n        }\n      } catch {\n        alert(\"Erreur upload fichier\");\n      }\n      setNewFile(null);\n      if (fileInputRef.current) fileInputRef.current.value = \"\";\n      return;\n    }\n    // Text message\n    if (newMsg.trim()) {\n      if (selectedTab === \"general\") {\n        socket.emit(\"sendGeneralMessage\", {\n          content: newMsg,\n          type: \"text\",\n          senderId: user.id\n        });\n      } else if (selectedTab === \"session\") {\n        socket.emit(\"sendSession2Message\", {\n          content: newMsg,\n          type: \"text\",\n          session2Id: Number(session2Id),\n          senderId: user.id\n        });\n      } else {\n        socket.emit(\"sendSeanceMessage\", {\n          content: newMsg,\n          type: \"text\",\n          seanceId: Number(seanceId),\n          senderId: user.id\n        });\n      }\n      setNewMsg(\"\");\n    }\n  };\n\n  // Delete message\n  const handleDeleteMsg = async msgId => {\n    if (selectedTab === \"session\") {\n      await fetch(`http://localhost:8000/session2-chat-messages/${msgId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      setSessionChatMessages(prev => prev.filter(m => m.id !== msgId));\n    } else if (selectedTab === \"seance\") {\n      await fetch(`http://localhost:8000/chat-messages/${msgId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      setSeanceChatMessages(prev => prev.filter(m => m.id !== msgId));\n    } else if (selectedTab === \"general\") {\n      await fetch(`http://localhost:8000/general-chat-messages/${msgId}`, {\n        method: \"DELETE\",\n        headers: {\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          userId: user.id\n        })\n      });\n      setGeneralChatMessages(prev => prev.filter(m => m.id !== msgId));\n    }\n  };\n\n  // --- UI ---\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => setOpen(!open),\n      style: {\n        position: \"fixed\",\n        bottom: \"104px\",\n        right: \"24px\",\n        width: \"64px\",\n        height: \"64px\",\n        borderRadius: \"50%\",\n        background: open ? \"#fff\" : \"#d32f2f\",\n        color: open ? \"#d32f2f\" : \"#fff\",\n        fontSize: \"28px\",\n        boxShadow: \"0 4px 24px rgba(0, 0, 0, 0.22)\",\n        border: open ? \"3px solid #d32f2f\" : \"none\",\n        zIndex: 2000,\n        cursor: \"pointer\",\n        transition: \"all 0.18s ease-in-out\",\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        outline: \"none\"\n      },\n      \"aria-label\": open ? \"Close chat\" : \"Open chat\",\n      onMouseOver: e => {\n        e.currentTarget.style.background = open ? \"#fbeaec\" : \"#e53935\";\n      },\n      onMouseOut: e => {\n        e.currentTarget.style.background = open ? \"#fff\" : \"#d32f2f\";\n      },\n      onFocus: e => {\n        e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(211, 47, 47, 0.4)\";\n      },\n      onBlur: e => {\n        e.currentTarget.style.boxShadow = \"0 4px 24px rgba(0, 0, 0, 0.22)\";\n      },\n      children: open ? \"✕\" : \"💬\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), showTooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"fixed\",\n        bottom: 166,\n        right: 24,\n        fontSize: \"12px\",\n        color: \"white\",\n        background: \"rgba(0,0,0,0.8)\",\n        padding: \"6px 12px\",\n        borderRadius: \"6px\",\n        whiteSpace: \"nowrap\",\n        zIndex: 2001\n      },\n      children: \"Chat de session\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 9\n    }, this), open && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        position: \"fixed\",\n        bottom: 180,\n        right: 32,\n        width: 410,\n        maxHeight: \"74vh\",\n        borderRadius: 4,\n        boxShadow: 10,\n        zIndex: 2100,\n        display: \"flex\",\n        flexDirection: \"column\",\n        p: 0,\n        overflow: \"hidden\",\n        background: \"#f9f9fa\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        bgcolor: \"#fff\",\n        px: 2,\n        pt: 1.5,\n        pb: 0.5,\n        borderBottom: \"1px solid #e8e8e8\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: selectedTab === \"session\" ? \"contained\" : \"text\",\n          onClick: () => setSelectedTab(\"session\"),\n          color: \"error\",\n          size: \"small\",\n          sx: {\n            borderRadius: 3,\n            fontWeight: 600,\n            boxShadow: selectedTab === \"session\" ? 3 : 0,\n            mx: 1,\n            px: 2\n          },\n          children: \"Session Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: selectedTab === \"seance\" ? \"contained\" : \"text\",\n          onClick: () => setSelectedTab(\"seance\"),\n          color: \"primary\",\n          size: \"small\",\n          disabled: !seanceId,\n          sx: {\n            borderRadius: 3,\n            fontWeight: 600,\n            boxShadow: selectedTab === \"seance\" ? 3 : 0,\n            mx: 1,\n            px: 2\n          },\n          children: \"S\\xE9ance Chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: selectedTab === \"general\" ? \"contained\" : \"text\",\n          onClick: () => setSelectedTab(\"general\"),\n          color: \"secondary\",\n          size: \"small\",\n          sx: {\n            borderRadius: 3,\n            fontWeight: 600,\n            boxShadow: selectedTab === \"general\" ? 3 : 0,\n            mx: 1,\n            px: 2\n          },\n          children: \"Chat G\\xE9n\\xE9ral\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 11\n      }, this), session2s.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        bgcolor: \"#f6f6fc\",\n        px: 2,\n        py: 1.5,\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: session2Id,\n          onChange: e => setSession2Id(Number(e.target.value)),\n          style: {\n            width: \"100%\",\n            padding: \"6px 10px\",\n            borderRadius: 8,\n            border: \"1px solid #ccc\",\n            fontWeight: 500\n          },\n          children: session2s.map(s => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: s.id,\n            children: s.name\n          }, s.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 13\n      }, this), selectedTab === \"seance\" && seances.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        bgcolor: \"#f6f6fc\",\n        px: 2,\n        py: 1.5,\n        children: /*#__PURE__*/_jsxDEV(\"select\", {\n          value: seanceId,\n          onChange: e => setSeanceId(Number(e.target.value)),\n          style: {\n            width: \"100%\",\n            padding: \"6px 10px\",\n            borderRadius: 8,\n            border: \"1px solid #ccc\",\n            fontWeight: 500\n          },\n          children: seances.map(s => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: s.id,\n            children: s.title || `Séance ${s.id}`\n          }, s.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          pt: 1.5,\n          flex: 1,\n          overflowY: \"auto\",\n          background: \"#f7f7fa\",\n          borderBottom: \"1px solid #eee\",\n          minHeight: 200,\n          scrollbarWidth: \"thin\",\n          \"&::-webkit-scrollbar\": {\n            width: \"7px\",\n            background: \"#eaeaea\",\n            borderRadius: 5\n          },\n          \"&::-webkit-scrollbar-thumb\": {\n            background: \"#e1e1e1\",\n            borderRadius: 5\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 1,\n          children: [(selectedTab === \"session\" ? sessionChatMessages : selectedTab === \"seance\" ? seanceChatMessages : generalChatMessages).map((msg, i) => {\n            var _msg$sender, _msg$sender2, _msg$sender3, _msg$sender4, _msg$sender5, _msg$sender6, _msg$sender6$profileP, _msg$sender7, _msg$sender8, _msg$sender9, _msg$sender9$name, _msg$sender9$name$, _msg$sender10, _msg$sender11, _msg$sender12;\n            return /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: \"flex\",\n                alignItems: \"flex-start\",\n                gap: 1.5,\n                bgcolor: ((_msg$sender = msg.sender) === null || _msg$sender === void 0 ? void 0 : _msg$sender.id) === user.id ? \"#fff8f8\" : \"#fff\",\n                borderRadius: 3,\n                boxShadow: ((_msg$sender2 = msg.sender) === null || _msg$sender2 === void 0 ? void 0 : _msg$sender2.id) === user.id ? \"0 1px 8px #ffe0e0\" : \"0 1px 8px #e2e2ef0c\",\n                border: \"1px solid #f2f2f3\",\n                px: 1.5,\n                py: 1,\n                mr: ((_msg$sender3 = msg.sender) === null || _msg$sender3 === void 0 ? void 0 : _msg$sender3.id) === user.id ? 0 : \"auto\",\n                ml: ((_msg$sender4 = msg.sender) === null || _msg$sender4 === void 0 ? void 0 : _msg$sender4.id) === user.id ? \"auto\" : 0,\n                maxWidth: \"85%\"\n              },\n              children: [(_msg$sender5 = msg.sender) !== null && _msg$sender5 !== void 0 && _msg$sender5.profilePic ? /*#__PURE__*/_jsxDEV(\"img\", {\n                src: (_msg$sender6 = msg.sender) !== null && _msg$sender6 !== void 0 && (_msg$sender6$profileP = _msg$sender6.profilePic) !== null && _msg$sender6$profileP !== void 0 && _msg$sender6$profileP.startsWith('http') ? msg.sender.profilePic : `http://localhost:8000${((_msg$sender7 = msg.sender) === null || _msg$sender7 === void 0 ? void 0 : _msg$sender7.profilePic) || '/profile-pics/default.png'}`,\n                alt: (_msg$sender8 = msg.sender) === null || _msg$sender8 === void 0 ? void 0 : _msg$sender8.name,\n                style: {\n                  width: 32,\n                  height: 32,\n                  borderRadius: \"50%\",\n                  marginTop: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 32,\n                  height: 32,\n                  bgcolor: \"#d5dde9\",\n                  mt: 0.25\n                },\n                children: ((_msg$sender9 = msg.sender) === null || _msg$sender9 === void 0 ? void 0 : (_msg$sender9$name = _msg$sender9.name) === null || _msg$sender9$name === void 0 ? void 0 : (_msg$sender9$name$ = _msg$sender9$name[0]) === null || _msg$sender9$name$ === void 0 ? void 0 : _msg$sender9$name$.toUpperCase()) || \"?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    fontWeight: \"bold\",\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 15\n                    },\n                    children: ((_msg$sender10 = msg.sender) === null || _msg$sender10 === void 0 ? void 0 : _msg$sender10.name) || \"Anonyme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 531,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      color: \"#aaa\",\n                      fontSize: 11.5,\n                      fontWeight: 500,\n                      ml: 1,\n                      mt: 0.3\n                    },\n                    children: [(_msg$sender11 = msg.sender) !== null && _msg$sender11 !== void 0 && _msg$sender11.role ? \"· \" + msg.sender.role : \"\", msg.createdAt && /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        marginLeft: 8\n                      },\n                      children: new Date(msg.createdAt).toLocaleTimeString([], {\n                        hour: \"2-digit\",\n                        minute: \"2-digit\"\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 545,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 23\n                  }, this), ((_msg$sender12 = msg.sender) === null || _msg$sender12 === void 0 ? void 0 : _msg$sender12.id) === user.id && /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDeleteMsg(msg.id),\n                    color: \"error\",\n                    sx: {\n                      ml: \"auto\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 557,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  mt: 0.5,\n                  children: [msg.type === \"text\" && /*#__PURE__*/_jsxDEV(Typography, {\n                    sx: {\n                      fontSize: 15,\n                      color: \"#222\"\n                    },\n                    children: msg.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 25\n                  }, this), msg.type === \"image\" && /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: msg.content,\n                    alt: \"img\",\n                    style: {\n                      maxWidth: 170,\n                      borderRadius: 7,\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 569,\n                    columnNumber: 25\n                  }, this), msg.type === \"audio\" && /*#__PURE__*/_jsxDEV(\"audio\", {\n                    controls: true,\n                    src: msg.content,\n                    style: {\n                      maxWidth: 150,\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this), msg.type === \"video\" && /*#__PURE__*/_jsxDEV(\"video\", {\n                    controls: true,\n                    src: msg.content,\n                    style: {\n                      maxWidth: 160,\n                      borderRadius: 7,\n                      marginTop: 4\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 575,\n                    columnNumber: 25\n                  }, this), msg.type === \"file\" && /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: msg.content,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    style: {\n                      display: \"block\",\n                      marginTop: 4,\n                      color: \"#0072ff\"\n                    },\n                    children: [\"\\uD83D\\uDCCE \", msg.content.split(\"/\").pop()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)]\n            }, msg.id || i, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this);\n          }), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: chatBottomRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          background: \"#fff\",\n          borderTop: \"1px solid #e8e8e8\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          value: newMsg,\n          size: \"small\",\n          placeholder: \"\\xC9cris un message\\u2026\",\n          onChange: e => setNewMsg(e.target.value),\n          onKeyDown: e => e.key === \"Enter\" && handleChatSend(),\n          sx: {\n            background: \"#f8f8f8\",\n            borderRadius: 2\n          },\n          inputProps: {\n            style: {\n              fontSize: 15\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setShowEmoji(v => !v),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            role: \"img\",\n            \"aria-label\": \"emoji\",\n            children: \"\\uD83D\\uDE00\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          component: \"label\",\n          color: newFile ? \"success\" : \"primary\",\n          children: [/*#__PURE__*/_jsxDEV(AddPhotoAlternateIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            hidden: true,\n            ref: fileInputRef,\n            type: \"file\",\n            accept: \"image/*,video/*,audio/*,application/pdf\",\n            onChange: e => setNewFile(e.target.files[0])\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleChatSend,\n          variant: \"contained\",\n          color: selectedTab === \"session\" ? \"error\" : \"primary\",\n          disabled: !newMsg.trim() && !newFile,\n          sx: {\n            px: 2,\n            fontWeight: 600\n          },\n          children: \"Envoyer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 622,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 11\n      }, this), showEmoji && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"absolute\",\n          bottom: 90,\n          right: 30,\n          zIndex: 11\n        },\n        children: /*#__PURE__*/_jsxDEV(EmojiPicker, {\n          onEmojiClick: handleEmoji,\n          autoFocusSearch: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 13\n      }, this), newFile && /*#__PURE__*/_jsxDEV(Typography, {\n        color: \"primary\",\n        fontSize: 13,\n        ml: 2,\n        mt: 0.5,\n        children: [\"Fichier pr\\xEAt \\xE0 envoyer : \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: newFile.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 40\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(UnifiedSessionChatPopup, \"UkAzFNf0Ff6T3i/3gCFjtgEvcxY=\");\n_c = UnifiedSessionChatPopup;\nvar _c;\n$RefreshReg$(_c, \"UnifiedSessionChatPopup\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "Paper", "Typography", "<PERSON><PERSON>", "TextField", "IconButton", "<PERSON><PERSON>", "Avatar", "AddPhotoAlternateIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SOCKET_URL", "UnifiedSessionChatPopup", "user", "_s", "generalChatMessages", "setGeneralChatMessages", "generalSocketRef", "selectedTab", "setSelectedTab", "session2s", "setSession2s", "session2Id", "setSession2Id", "seances", "setSeances", "seanceId", "setSeanceId", "sessionChatMessages", "setSessionChatMessages", "seanceChatMessages", "setSeanceChatMessages", "newMsg", "setNewMsg", "newFile", "setNewFile", "showE<PERSON>ji", "setShowEmoji", "showTooltip", "setShowTooltip", "chatBottomRef", "fileInputRef", "sessionSocketRef", "seanceSocketRef", "open", "<PERSON><PERSON><PERSON>", "current", "scrollIntoView", "behavior", "disconnect", "s", "io", "transports", "emit", "on", "msgs", "msg", "prev", "id", "filter", "m", "fetch", "then", "res", "json", "data", "_sessions$0$id", "_sessions$", "sessions", "map", "_s$session", "session2", "name", "_data$0$id", "_data$", "Number", "payload", "handleEmoji", "e", "emoji", "handleChatSend", "socket", "formData", "FormData", "append", "uploadUrl", "method", "body", "fileData", "content", "fileUrl", "type", "fileType", "senderId", "alert", "value", "trim", "handleDeleteMsg", "msgId", "headers", "JSON", "stringify", "userId", "children", "onClick", "style", "position", "bottom", "right", "width", "height", "borderRadius", "background", "color", "fontSize", "boxShadow", "border", "zIndex", "cursor", "transition", "display", "alignItems", "justifyContent", "outline", "onMouseOver", "currentTarget", "onMouseOut", "onFocus", "onBlur", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "padding", "whiteSpace", "sx", "maxHeight", "flexDirection", "p", "overflow", "bgcolor", "px", "pt", "pb", "borderBottom", "variant", "size", "fontWeight", "mx", "disabled", "length", "py", "onChange", "target", "title", "flex", "overflowY", "minHeight", "scrollbarWidth", "spacing", "i", "_msg$sender", "_msg$sender2", "_msg$sender3", "_msg$sender4", "_msg$sender5", "_msg$sender6", "_msg$sender6$profileP", "_msg$sender7", "_msg$sender8", "_msg$sender9", "_msg$sender9$name", "_msg$sender9$name$", "_msg$sender10", "_msg$sender11", "_msg$sender12", "gap", "sender", "mr", "ml", "max<PERSON><PERSON><PERSON>", "profilePic", "src", "startsWith", "alt", "marginTop", "mt", "toUpperCase", "role", "createdAt", "marginLeft", "Date", "toLocaleTimeString", "hour", "minute", "DeleteIcon", "controls", "href", "rel", "split", "pop", "ref", "borderTop", "fullWidth", "placeholder", "onKeyDown", "key", "inputProps", "v", "component", "hidden", "accept", "files", "EmojiPicker", "onEmojiClick", "autoFocusSearch", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/components/Session2ChatPopup.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport {\r\n  Box, Paper, Typography, Stack, TextField, IconButton, Button, Avatar,\r\n} from \"@mui/material\";\r\nimport AddPhotoAlternateIcon from \"@mui/icons-material/AddPhotoAlternate\";\r\n\r\n\r\nconst SOCKET_URL = \"http://localhost:8000\";\r\n\r\nexport default function UnifiedSessionChatPopup({ user }) {\r\n\r\n  // const globalSocketRef = useRef(null);\r\n\r\n\r\n  const [generalChatMessages, setGeneralChatMessages] = useState([]);\r\n  const generalSocketRef = useRef(null);\r\n\r\n  // Which chat is active: \"session\" or \"seance\"\r\n  const [selectedTab, setSelectedTab] = useState(\"session\");\r\n\r\n  // Session2 data\r\n  const [session2s, setSession2s] = useState([]); // All sessions user is in\r\n  const [session2Id, setSession2Id] = useState(null);\r\n\r\n  // Seance data\r\n  const [seances, setSeances] = useState([]); // All seances for current session\r\n  const [seanceId, setSeanceId] = useState(null);\r\n\r\n  // Chat states (messages, input, etc)\r\n  const [sessionChatMessages, setSessionChatMessages] = useState([]);\r\n  const [seanceChatMessages, setSeanceChatMessages] = useState([]);\r\n\r\n  const [newMsg, setNewMsg] = useState(\"\");\r\n  const [newFile, setNewFile] = useState(null);\r\n  const [showEmoji, setShowEmoji] = useState(false);\r\n  const [showTooltip, setShowTooltip] = useState(false);\r\n\r\n  const chatBottomRef = useRef();\r\n  const fileInputRef = useRef();\r\n  const sessionSocketRef = useRef(null);\r\n  const seanceSocketRef = useRef(null);\r\n\r\n  // UI popup\r\n  const [open, setOpen] = useState(false);\r\n  useEffect(() => {\r\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [sessionChatMessages, seanceChatMessages, generalChatMessages, selectedTab, open]);\r\n\r\n  //genralechat \r\n  useEffect(() => {\r\n    // Only connect once\r\n    if (generalSocketRef.current) {\r\n      generalSocketRef.current.disconnect();\r\n    }\r\n    const s = io(`${SOCKET_URL}/general-chat`, { transports: [\"websocket\"] });\r\n    generalSocketRef.current = s;\r\n\r\n    // Fetch messages\r\n    s.emit(\"fetchGeneralMessages\");\r\n    s.on(\"generalMessages\", msgs => setGeneralChatMessages(msgs));\r\n    s.on(\"newGeneralMessage\", msg =>\r\n      setGeneralChatMessages(prev => [...prev, msg])\r\n    );\r\n    s.on(\"deleteGeneralMessage\", ({ id }) =>\r\n      setGeneralChatMessages(prev => prev.filter(m => m.id !== id))\r\n    );\r\n    return () => s.disconnect();\r\n  }, []); // []: only once\r\n\r\n  // Fetch session2s for the user\r\n  useEffect(() => {\r\n    if (!user?.id) return;\r\n    fetch(`http://localhost:8000/users/${user.id}/sessions2`)\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        // Normalize (handles both array of userSession2 or array of session2)\r\n        const sessions = data.map(s =>\r\n          s.session2\r\n            ? { id: s.session2.id, name: s.session2.name }\r\n            : s.session2Id\r\n              ? { id: s.session2Id, name: s.session2?.name || \"\" }\r\n              : s\r\n        );\r\n        setSession2s(sessions);\r\n        setSession2Id(sessions[0]?.id ?? null);\r\n      });\r\n  }, [user?.id]);\r\n\r\n  // Fetch seances for current session2\r\n  useEffect(() => {\r\n    if (!session2Id) {\r\n      setSeances([]);\r\n      setSeanceId(null);\r\n      return;\r\n    }\r\n    fetch(`http://localhost:8000/seance-formateur/session/${session2Id}`)\r\n      .then(res => res.json())\r\n      .then(data => {\r\n        setSeances(data);\r\n        setSeanceId(data[0]?.id ?? null); // pick first as default, or let user choose if multiple\r\n      });\r\n  }, [session2Id]);\r\n\r\n  // Fetch messages for both chats\r\n  useEffect(() => {\r\n    if (session2Id) {\r\n      fetch(`http://localhost:8000/session2-chat-messages/${session2Id}`)\r\n        .then(res => res.json())\r\n        .then(msgs => setSessionChatMessages(msgs));\r\n    }\r\n    if (seanceId) {\r\n      fetch(`http://localhost:8000/chat-messages/${seanceId}`)\r\n        .then(res => res.json())\r\n        .then(msgs => setSeanceChatMessages(msgs));\r\n    }\r\n  }, [session2Id, seanceId]);\r\n\r\n  // Session chat socket\r\n  useEffect(() => {\r\n    if (!session2Id) return;\r\n    if (sessionSocketRef.current) {\r\n      sessionSocketRef.current.emit(\"leaveSession2\", { session2Id: Number(session2Id) });\r\n      sessionSocketRef.current.disconnect();\r\n    }\r\n    const s = io(SOCKET_URL, { transports: [\"websocket\"] });\r\n    sessionSocketRef.current = s;\r\n    s.emit(\"joinSession2\", { session2Id: Number(session2Id) });\r\n    s.on(\"newSession2Message\", msg => setSessionChatMessages(prev => [...prev, msg]));\r\n    s.on(\"deleteSession2Message\", payload => setSessionChatMessages(prev => prev.filter(m => m.id !== payload.id)));\r\n    s.on(\"clearSession2Messages\", () => setSessionChatMessages([]));\r\n    return () => {\r\n      s.emit(\"leaveSession2\", { session2Id: Number(session2Id) });\r\n      s.disconnect();\r\n    };\r\n  }, [session2Id]);\r\n\r\n  // Seance chat socket\r\n  useEffect(() => {\r\n    if (!seanceId) return;\r\n    if (seanceSocketRef.current) {\r\n      seanceSocketRef.current.emit(\"leaveSeance\", { seanceId: Number(seanceId) });\r\n      seanceSocketRef.current.disconnect();\r\n    }\r\n    const s = io(SOCKET_URL, { transports: [\"websocket\"] });\r\n    seanceSocketRef.current = s;\r\n    s.emit(\"joinSeance\", { seanceId: Number(seanceId) });\r\n    s.on(\"newSeanceMessage\", msg => setSeanceChatMessages(prev => [...prev, msg]));\r\n    s.on(\"deleteSeanceMessage\", payload => setSeanceChatMessages(prev => prev.filter(m => m.id !== payload.id)));\r\n    s.on(\"clearSeanceMessages\", () => setSeanceChatMessages([]));\r\n    return () => {\r\n      s.emit(\"leaveSeance\", { seanceId: Number(seanceId) });\r\n      s.disconnect();\r\n    };\r\n  }, [seanceId]);\r\n\r\n  // Scroll to bottom\r\n  useEffect(() => {\r\n    if (chatBottomRef.current) chatBottomRef.current.scrollIntoView({ behavior: \"smooth\" });\r\n  }, [sessionChatMessages, seanceChatMessages, selectedTab, open]);\r\n\r\n  // Tab switch resets input\r\n  useEffect(() => {\r\n    setNewMsg(\"\");\r\n    setNewFile(null);\r\n    setShowEmoji(false);\r\n  }, [selectedTab, session2Id, seanceId]);\r\n\r\n  // Emoji\r\n  const handleEmoji = (e) => {\r\n    setNewMsg((prev) => prev + e.emoji);\r\n    setShowEmoji(false);\r\n  };\r\n\r\n  // Send message (text/file) for current tab\r\n  const handleChatSend = async () => {\r\n    let socket;\r\n    if (selectedTab === \"session\") socket = sessionSocketRef.current;\r\n    else if (selectedTab === \"seance\") socket = seanceSocketRef.current;\r\n    else if (selectedTab === \"general\") socket = generalSocketRef.current;\r\n    if (!socket) return;\r\n\r\n    // File message\r\n    if (newFile) {\r\n      const formData = new FormData();\r\n      formData.append(\"file\", newFile);\r\n\r\n      let uploadUrl = \"\";\r\n      if (selectedTab === \"general\") {\r\n        uploadUrl = \"http://localhost:8000/general-chat-messages/upload-chat\";\r\n      } else if (selectedTab === \"session\") {\r\n        formData.append(\"session2Id\", session2Id);\r\n        uploadUrl = \"http://localhost:8000/session2-chat-messages/upload-chat\";\r\n      } else if (selectedTab === \"seance\") {\r\n        formData.append(\"seanceId\", seanceId);\r\n        uploadUrl = \"http://localhost:8000/chat-messages/upload-chat\";\r\n      }\r\n      try {\r\n        const res = await fetch(uploadUrl, {\r\n          method: \"POST\",\r\n          body: formData,\r\n        });\r\n        const fileData = await res.json();\r\n        if (selectedTab === \"general\") {\r\n          socket.emit(\"sendGeneralMessage\", {\r\n            content: fileData.fileUrl,\r\n            type: fileData.fileType || \"file\",\r\n            senderId: user.id,\r\n          });\r\n        } else if (selectedTab === \"session\") {\r\n          socket.emit(\"sendSession2Message\", {\r\n            content: fileData.fileUrl,\r\n            type: fileData.fileType || \"file\",\r\n            session2Id: Number(session2Id),\r\n            senderId: user.id,\r\n          });\r\n        } else {\r\n          socket.emit(\"sendSeanceMessage\", {\r\n            content: fileData.fileUrl,\r\n            type: fileData.fileType || \"file\",\r\n            seanceId: Number(seanceId),\r\n            senderId: user.id,\r\n          });\r\n        }\r\n      } catch {\r\n        alert(\"Erreur upload fichier\");\r\n      }\r\n      setNewFile(null);\r\n      if (fileInputRef.current) fileInputRef.current.value = \"\";\r\n      return;\r\n    }\r\n    // Text message\r\n    if (newMsg.trim()) {\r\n      if (selectedTab === \"general\") {\r\n        socket.emit(\"sendGeneralMessage\", {\r\n          content: newMsg,\r\n          type: \"text\",\r\n          senderId: user.id,\r\n        });\r\n      } else if (selectedTab === \"session\") {\r\n        socket.emit(\"sendSession2Message\", {\r\n          content: newMsg,\r\n          type: \"text\",\r\n          session2Id: Number(session2Id),\r\n          senderId: user.id,\r\n        });\r\n      } else {\r\n        socket.emit(\"sendSeanceMessage\", {\r\n          content: newMsg,\r\n          type: \"text\",\r\n          seanceId: Number(seanceId),\r\n          senderId: user.id,\r\n        });\r\n      }\r\n      setNewMsg(\"\");\r\n    }\r\n  };\r\n\r\n\r\n  // Delete message\r\n  const handleDeleteMsg = async (msgId) => {\r\n    if (selectedTab === \"session\") {\r\n      await fetch(`http://localhost:8000/session2-chat-messages/${msgId}`, {\r\n        method: \"DELETE\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ userId: user.id }),\r\n      });\r\n      setSessionChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    } else if (selectedTab === \"seance\") {\r\n      await fetch(`http://localhost:8000/chat-messages/${msgId}`, {\r\n        method: \"DELETE\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ userId: user.id }),\r\n      });\r\n      setSeanceChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    } else if (selectedTab === \"general\") {\r\n      await fetch(`http://localhost:8000/general-chat-messages/${msgId}`, {\r\n        method: \"DELETE\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({ userId: user.id }),\r\n      });\r\n      setGeneralChatMessages((prev) => prev.filter((m) => m.id !== msgId));\r\n    }\r\n  };\r\n\r\n\r\n  // --- UI ---\r\n  return (\r\n    <>\r\n      {/* Floating Button */}\r\n      <button\r\n        onClick={() => setOpen(!open)}\r\n        style={{\r\n          position: \"fixed\",\r\n          bottom: \"104px\",\r\n          right: \"24px\",\r\n          width: \"64px\",\r\n          height: \"64px\",\r\n          borderRadius: \"50%\",\r\n          background: open ? \"#fff\" : \"#d32f2f\",\r\n          color: open ? \"#d32f2f\" : \"#fff\",\r\n          fontSize: \"28px\",\r\n          boxShadow: \"0 4px 24px rgba(0, 0, 0, 0.22)\",\r\n          border: open ? \"3px solid #d32f2f\" : \"none\",\r\n          zIndex: 2000,\r\n          cursor: \"pointer\",\r\n          transition: \"all 0.18s ease-in-out\",\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          justifyContent: \"center\",\r\n          outline: \"none\"\r\n        }}\r\n        aria-label={open ? \"Close chat\" : \"Open chat\"}\r\n        onMouseOver={(e) => {\r\n          e.currentTarget.style.background = open ? \"#fbeaec\" : \"#e53935\";\r\n        }}\r\n        onMouseOut={(e) => {\r\n          e.currentTarget.style.background = open ? \"#fff\" : \"#d32f2f\";\r\n        }}\r\n        onFocus={(e) => {\r\n          e.currentTarget.style.boxShadow = \"0 0 0 3px rgba(211, 47, 47, 0.4)\";\r\n        }}\r\n        onBlur={(e) => {\r\n          e.currentTarget.style.boxShadow = \"0 4px 24px rgba(0, 0, 0, 0.22)\";\r\n        }}\r\n      >\r\n        {open ? \"✕\" : \"💬\"}\r\n      </button>\r\n      {/* Tooltip au survol */}\r\n      {showTooltip && (\r\n        <div style={{\r\n          position: \"fixed\",\r\n          bottom: 166,\r\n          right: 24,\r\n          fontSize: \"12px\",\r\n          color: \"white\",\r\n          background: \"rgba(0,0,0,0.8)\",\r\n          padding: \"6px 12px\",\r\n          borderRadius: \"6px\",\r\n          whiteSpace: \"nowrap\",\r\n          zIndex: 2001\r\n        }}>\r\n          Chat de session\r\n        </div>\r\n      )}\r\n\r\n      {/* Popup */}\r\n      {open && (\r\n        <Paper\r\n          sx={{\r\n            position: \"fixed\",\r\n            bottom: 180,\r\n            right: 32,\r\n            width: 410,\r\n            maxHeight: \"74vh\",\r\n            borderRadius: 4,\r\n            boxShadow: 10,\r\n            zIndex: 2100,\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            p: 0,\r\n            overflow: \"hidden\",\r\n            background: \"#f9f9fa\",\r\n          }}\r\n        >\r\n          {/* Tabs */}\r\n          <Box\r\n            display=\"flex\"\r\n            alignItems=\"center\"\r\n            bgcolor=\"#fff\"\r\n            px={2}\r\n            pt={1.5}\r\n            pb={0.5}\r\n            borderBottom=\"1px solid #e8e8e8\"\r\n          >\r\n            <Button\r\n              variant={selectedTab === \"session\" ? \"contained\" : \"text\"}\r\n              onClick={() => setSelectedTab(\"session\")}\r\n              color=\"error\"\r\n              size=\"small\"\r\n              sx={{\r\n                borderRadius: 3,\r\n                fontWeight: 600,\r\n                boxShadow: selectedTab === \"session\" ? 3 : 0,\r\n                mx: 1,\r\n                px: 2\r\n              }}\r\n            >\r\n              Session Chat\r\n            </Button>\r\n            <Button\r\n              variant={selectedTab === \"seance\" ? \"contained\" : \"text\"}\r\n              onClick={() => setSelectedTab(\"seance\")}\r\n              color=\"primary\"\r\n              size=\"small\"\r\n              disabled={!seanceId}\r\n              sx={{\r\n                borderRadius: 3,\r\n                fontWeight: 600,\r\n                boxShadow: selectedTab === \"seance\" ? 3 : 0,\r\n                mx: 1,\r\n                px: 2\r\n              }}\r\n            >\r\n              Séance Chat\r\n            </Button>\r\n            <Button\r\n              variant={selectedTab === \"general\" ? \"contained\" : \"text\"}\r\n              onClick={() => setSelectedTab(\"general\")}\r\n              color=\"secondary\"\r\n              size=\"small\"\r\n              sx={{\r\n                borderRadius: 3,\r\n                fontWeight: 600,\r\n                boxShadow: selectedTab === \"general\" ? 3 : 0,\r\n                mx: 1,\r\n                px: 2\r\n              }}\r\n            >\r\n              Chat Général\r\n            </Button>\r\n\r\n          </Box>\r\n          {/* Session selector */}\r\n          {session2s.length > 1 && (\r\n            <Box bgcolor=\"#f6f6fc\" px={2} py={1.5}>\r\n              <select\r\n                value={session2Id}\r\n                onChange={e => setSession2Id(Number(e.target.value))}\r\n                style={{\r\n                  width: \"100%\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: 8,\r\n                  border: \"1px solid #ccc\",\r\n                  fontWeight: 500\r\n                }}\r\n              >\r\n                {session2s.map(s => (\r\n                  <option value={s.id} key={s.id}>{s.name}</option>\r\n                ))}\r\n              </select>\r\n            </Box>\r\n          )}\r\n          {/* Seance selector */}\r\n          {selectedTab === \"seance\" && seances.length > 1 && (\r\n            <Box bgcolor=\"#f6f6fc\" px={2} py={1.5}>\r\n              <select\r\n                value={seanceId}\r\n                onChange={e => setSeanceId(Number(e.target.value))}\r\n                style={{\r\n                  width: \"100%\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: 8,\r\n                  border: \"1px solid #ccc\",\r\n                  fontWeight: 500\r\n                }}\r\n              >\r\n                {seances.map(s => (\r\n                  <option value={s.id} key={s.id}>{s.title || `Séance ${s.id}`}</option>\r\n                ))}\r\n              </select>\r\n            </Box>\r\n          )}\r\n\r\n          {/* Chat Messages */}\r\n          <Box\r\n            sx={{\r\n              p: 2,\r\n              pt: 1.5,\r\n              flex: 1,\r\n              overflowY: \"auto\",\r\n              background: \"#f7f7fa\",\r\n              borderBottom: \"1px solid #eee\",\r\n              minHeight: 200,\r\n              scrollbarWidth: \"thin\",\r\n              \"&::-webkit-scrollbar\": {\r\n                width: \"7px\",\r\n                background: \"#eaeaea\",\r\n                borderRadius: 5\r\n              },\r\n              \"&::-webkit-scrollbar-thumb\": {\r\n                background: \"#e1e1e1\",\r\n                borderRadius: 5\r\n              }\r\n            }}\r\n          >\r\n            <Stack spacing={1}>\r\n              {(\r\n                selectedTab === \"session\"\r\n                  ? sessionChatMessages\r\n                  : selectedTab === \"seance\"\r\n                    ? seanceChatMessages\r\n                    : generalChatMessages\r\n              ).map((msg, i) => (\r\n                <Box\r\n                  key={msg.id || i}\r\n                  sx={{\r\n                    display: \"flex\",\r\n                    alignItems: \"flex-start\",\r\n                    gap: 1.5,\r\n                    bgcolor: msg.sender?.id === user.id ? \"#fff8f8\" : \"#fff\",\r\n                    borderRadius: 3,\r\n                    boxShadow: msg.sender?.id === user.id ? \"0 1px 8px #ffe0e0\" : \"0 1px 8px #e2e2ef0c\",\r\n                    border: \"1px solid #f2f2f3\",\r\n                    px: 1.5,\r\n                    py: 1,\r\n                    mr: msg.sender?.id === user.id ? 0 : \"auto\",\r\n                    ml: msg.sender?.id === user.id ? \"auto\" : 0,\r\n                    maxWidth: \"85%\",\r\n                  }}\r\n                >\r\n                  {/* Avatar */}\r\n                  {msg.sender?.profilePic\r\n                    ? (\r\n                      <img\r\n                        src={\r\n                          msg.sender?.profilePic?.startsWith('http')\r\n                            ? msg.sender.profilePic\r\n                            : `http://localhost:8000${msg.sender?.profilePic || '/profile-pics/default.png'}`\r\n                        }\r\n                        alt={msg.sender?.name}\r\n                        style={{ width: 32, height: 32, borderRadius: \"50%\", marginTop: 2 }}\r\n                      />\r\n                    ) : (\r\n                      <Avatar sx={{ width: 32, height: 32, bgcolor: \"#d5dde9\", mt: 0.25 }}>\r\n                        {msg.sender?.name?.[0]?.toUpperCase() || \"?\"}\r\n                      </Avatar>\r\n                    )\r\n                  }\r\n                  <Box sx={{ flex: 1 }}>\r\n                    <Box display=\"flex\" alignItems=\"center\">\r\n                      <Typography variant=\"subtitle2\" fontWeight=\"bold\" color=\"primary\" sx={{ fontSize: 15 }}>\r\n                        {msg.sender?.name || \"Anonyme\"}\r\n                      </Typography>\r\n                      <Typography\r\n                        sx={{\r\n                          color: \"#aaa\",\r\n                          fontSize: 11.5,\r\n                          fontWeight: 500,\r\n                          ml: 1,\r\n                          mt: 0.3\r\n                        }}\r\n                      >\r\n                        {msg.sender?.role ? \"· \" + msg.sender.role : \"\"}\r\n                        {msg.createdAt && (\r\n                          <span style={{ marginLeft: 8 }}>\r\n                            {new Date(msg.createdAt).toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" })}\r\n                          </span>\r\n                        )}\r\n                      </Typography>\r\n                      {msg.sender?.id === user.id && (\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={() => handleDeleteMsg(msg.id)}\r\n                          color=\"error\"\r\n                          sx={{ ml: \"auto\" }}\r\n                        >\r\n                          <DeleteIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      )}\r\n                    </Box>\r\n                    {/* Message Content */}\r\n                    <Box mt={0.5}>\r\n                      {msg.type === \"text\" && (\r\n                        <Typography sx={{ fontSize: 15, color: \"#222\" }}>\r\n                          {msg.content}\r\n                        </Typography>\r\n                      )}\r\n                      {msg.type === \"image\" && (\r\n                        <img src={msg.content} alt=\"img\" style={{ maxWidth: 170, borderRadius: 7, marginTop: 4 }} />\r\n                      )}\r\n                      {msg.type === \"audio\" && (\r\n                        <audio controls src={msg.content} style={{ maxWidth: 150, marginTop: 4 }} />\r\n                      )}\r\n                      {msg.type === \"video\" && (\r\n                        <video controls src={msg.content} style={{ maxWidth: 160, borderRadius: 7, marginTop: 4 }} />\r\n                      )}\r\n                      {msg.type === \"file\" && (\r\n                        <a href={msg.content} target=\"_blank\" rel=\"noopener noreferrer\" style={{ display: \"block\", marginTop: 4, color: \"#0072ff\" }}>\r\n                          📎 {msg.content.split(\"/\").pop()}\r\n                        </a>\r\n                      )}\r\n                    </Box>\r\n                  </Box>\r\n                </Box>\r\n              ))}\r\n              <div ref={chatBottomRef} />\r\n            </Stack>\r\n          </Box>\r\n\r\n          {/* Input */}\r\n          <Box sx={{\r\n            p: 2,\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            gap: 1,\r\n            background: \"#fff\",\r\n            borderTop: \"1px solid #e8e8e8\"\r\n          }}>\r\n            <TextField\r\n              fullWidth\r\n              value={newMsg}\r\n              size=\"small\"\r\n              placeholder=\"Écris un message…\"\r\n              onChange={e => setNewMsg(e.target.value)}\r\n              onKeyDown={e => e.key === \"Enter\" && handleChatSend()}\r\n              sx={{ background: \"#f8f8f8\", borderRadius: 2 }}\r\n              inputProps={{ style: { fontSize: 15 } }}\r\n            />\r\n            <IconButton onClick={() => setShowEmoji(v => !v)}>\r\n              <span role=\"img\" aria-label=\"emoji\">😀</span>\r\n            </IconButton>\r\n            <IconButton component=\"label\" color={newFile ? \"success\" : \"primary\"}>\r\n              <AddPhotoAlternateIcon />\r\n              <input\r\n                hidden\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\"image/*,video/*,audio/*,application/pdf\"\r\n                onChange={e => setNewFile(e.target.files[0])}\r\n              />\r\n            </IconButton>\r\n            <Button onClick={handleChatSend} variant=\"contained\" color={selectedTab === \"session\" ? \"error\" : \"primary\"} disabled={!newMsg.trim() && !newFile} sx={{ px: 2, fontWeight: 600 }}>\r\n              Envoyer\r\n            </Button>\r\n          </Box>\r\n          {showEmoji && (\r\n            <Box sx={{ position: \"absolute\", bottom: 90, right: 30, zIndex: 11 }}>\r\n              <EmojiPicker onEmojiClick={handleEmoji} autoFocusSearch={false} />\r\n            </Box>\r\n          )}\r\n          {newFile && (\r\n            <Typography color=\"primary\" fontSize={13} ml={2} mt={0.5}>\r\n              Fichier prêt à envoyer : <strong>{newFile.name}</strong>\r\n            </Typography>\r\n          )}\r\n        </Paper>\r\n      )}\r\n    </>\r\n  );\r\n\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM,QAC/D,eAAe;AACtB,OAAOC,qBAAqB,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG1E,MAAMC,UAAU,GAAG,uBAAuB;AAE1C,eAAe,SAASC,uBAAuBA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,EAAA;EAExD;;EAGA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAMqB,gBAAgB,GAAGpB,MAAM,CAAC,IAAI,CAAC;;EAErC;EACA,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,SAAS,CAAC;;EAEzD;EACA,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM4C,aAAa,GAAG3C,MAAM,CAAC,CAAC;EAC9B,MAAM4C,YAAY,GAAG5C,MAAM,CAAC,CAAC;EAC7B,MAAM6C,gBAAgB,GAAG7C,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM8C,eAAe,GAAG9C,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM,CAAC+C,IAAI,EAAEC,OAAO,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvCD,SAAS,CAAC,MAAM;IACd,IAAI6C,aAAa,CAACM,OAAO,EAAEN,aAAa,CAACM,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACzF,CAAC,EAAE,CAACpB,mBAAmB,EAAEE,kBAAkB,EAAEf,mBAAmB,EAAEG,WAAW,EAAE0B,IAAI,CAAC,CAAC;;EAErF;EACAjD,SAAS,CAAC,MAAM;IACd;IACA,IAAIsB,gBAAgB,CAAC6B,OAAO,EAAE;MAC5B7B,gBAAgB,CAAC6B,OAAO,CAACG,UAAU,CAAC,CAAC;IACvC;IACA,MAAMC,CAAC,GAAGC,EAAE,CAAC,GAAGxC,UAAU,eAAe,EAAE;MAAEyC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACzEnC,gBAAgB,CAAC6B,OAAO,GAAGI,CAAC;;IAE5B;IACAA,CAAC,CAACG,IAAI,CAAC,sBAAsB,CAAC;IAC9BH,CAAC,CAACI,EAAE,CAAC,iBAAiB,EAAEC,IAAI,IAAIvC,sBAAsB,CAACuC,IAAI,CAAC,CAAC;IAC7DL,CAAC,CAACI,EAAE,CAAC,mBAAmB,EAAEE,GAAG,IAC3BxC,sBAAsB,CAACyC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAC/C,CAAC;IACDN,CAAC,CAACI,EAAE,CAAC,sBAAsB,EAAE,CAAC;MAAEI;IAAG,CAAC,KAClC1C,sBAAsB,CAACyC,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKA,EAAE,CAAC,CAC9D,CAAC;IACD,OAAO,MAAMR,CAAC,CAACD,UAAU,CAAC,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI,EAACkB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6C,EAAE,GAAE;IACfG,KAAK,CAAC,+BAA+BhD,IAAI,CAAC6C,EAAE,YAAY,CAAC,CACtDI,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MAAA,IAAAC,cAAA,EAAAC,UAAA;MACd;MACA,MAAMC,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAACnB,CAAC;QAAA,IAAAoB,UAAA;QAAA,OACzBpB,CAAC,CAACqB,QAAQ,GACN;UAAEb,EAAE,EAAER,CAAC,CAACqB,QAAQ,CAACb,EAAE;UAAEc,IAAI,EAAEtB,CAAC,CAACqB,QAAQ,CAACC;QAAK,CAAC,GAC5CtB,CAAC,CAAC5B,UAAU,GACV;UAAEoC,EAAE,EAAER,CAAC,CAAC5B,UAAU;UAAEkD,IAAI,EAAE,EAAAF,UAAA,GAAApB,CAAC,CAACqB,QAAQ,cAAAD,UAAA,uBAAVA,UAAA,CAAYE,IAAI,KAAI;QAAG,CAAC,GAClDtB,CAAC;MAAA,CACT,CAAC;MACD7B,YAAY,CAAC+C,QAAQ,CAAC;MACtB7C,aAAa,EAAA2C,cAAA,IAAAC,UAAA,GAACC,QAAQ,CAAC,CAAC,CAAC,cAAAD,UAAA,uBAAXA,UAAA,CAAaT,EAAE,cAAAQ,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;IACxC,CAAC,CAAC;EACN,CAAC,EAAE,CAACrD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,EAAE,CAAC,CAAC;;EAEd;EACA/D,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,UAAU,EAAE;MACfG,UAAU,CAAC,EAAE,CAAC;MACdE,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IACAkC,KAAK,CAAC,kDAAkDvC,UAAU,EAAE,CAAC,CAClEwC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MAAA,IAAAQ,UAAA,EAAAC,MAAA;MACZjD,UAAU,CAACwC,IAAI,CAAC;MAChBtC,WAAW,EAAA8C,UAAA,IAAAC,MAAA,GAACT,IAAI,CAAC,CAAC,CAAC,cAAAS,MAAA,uBAAPA,MAAA,CAAShB,EAAE,cAAAe,UAAA,cAAAA,UAAA,GAAI,IAAI,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;EACN,CAAC,EAAE,CAACnD,UAAU,CAAC,CAAC;;EAEhB;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI2B,UAAU,EAAE;MACduC,KAAK,CAAC,gDAAgDvC,UAAU,EAAE,CAAC,CAChEwC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACP,IAAI,IAAI1B,sBAAsB,CAAC0B,IAAI,CAAC,CAAC;IAC/C;IACA,IAAI7B,QAAQ,EAAE;MACZmC,KAAK,CAAC,uCAAuCnC,QAAQ,EAAE,CAAC,CACrDoC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACP,IAAI,IAAIxB,qBAAqB,CAACwB,IAAI,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE,CAACjC,UAAU,EAAEI,QAAQ,CAAC,CAAC;;EAE1B;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,UAAU,EAAE;IACjB,IAAIoB,gBAAgB,CAACI,OAAO,EAAE;MAC5BJ,gBAAgB,CAACI,OAAO,CAACO,IAAI,CAAC,eAAe,EAAE;QAAE/B,UAAU,EAAEqD,MAAM,CAACrD,UAAU;MAAE,CAAC,CAAC;MAClFoB,gBAAgB,CAACI,OAAO,CAACG,UAAU,CAAC,CAAC;IACvC;IACA,MAAMC,CAAC,GAAGC,EAAE,CAACxC,UAAU,EAAE;MAAEyC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACvDV,gBAAgB,CAACI,OAAO,GAAGI,CAAC;IAC5BA,CAAC,CAACG,IAAI,CAAC,cAAc,EAAE;MAAE/B,UAAU,EAAEqD,MAAM,CAACrD,UAAU;IAAE,CAAC,CAAC;IAC1D4B,CAAC,CAACI,EAAE,CAAC,oBAAoB,EAAEE,GAAG,IAAI3B,sBAAsB,CAAC4B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC,CAAC;IACjFN,CAAC,CAACI,EAAE,CAAC,uBAAuB,EAAEsB,OAAO,IAAI/C,sBAAsB,CAAC4B,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKkB,OAAO,CAAClB,EAAE,CAAC,CAAC,CAAC;IAC/GR,CAAC,CAACI,EAAE,CAAC,uBAAuB,EAAE,MAAMzB,sBAAsB,CAAC,EAAE,CAAC,CAAC;IAC/D,OAAO,MAAM;MACXqB,CAAC,CAACG,IAAI,CAAC,eAAe,EAAE;QAAE/B,UAAU,EAAEqD,MAAM,CAACrD,UAAU;MAAE,CAAC,CAAC;MAC3D4B,CAAC,CAACD,UAAU,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAAC3B,UAAU,CAAC,CAAC;;EAEhB;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,QAAQ,EAAE;IACf,IAAIiB,eAAe,CAACG,OAAO,EAAE;MAC3BH,eAAe,CAACG,OAAO,CAACO,IAAI,CAAC,aAAa,EAAE;QAAE3B,QAAQ,EAAEiD,MAAM,CAACjD,QAAQ;MAAE,CAAC,CAAC;MAC3EiB,eAAe,CAACG,OAAO,CAACG,UAAU,CAAC,CAAC;IACtC;IACA,MAAMC,CAAC,GAAGC,EAAE,CAACxC,UAAU,EAAE;MAAEyC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACvDT,eAAe,CAACG,OAAO,GAAGI,CAAC;IAC3BA,CAAC,CAACG,IAAI,CAAC,YAAY,EAAE;MAAE3B,QAAQ,EAAEiD,MAAM,CAACjD,QAAQ;IAAE,CAAC,CAAC;IACpDwB,CAAC,CAACI,EAAE,CAAC,kBAAkB,EAAEE,GAAG,IAAIzB,qBAAqB,CAAC0B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,GAAG,CAAC,CAAC,CAAC;IAC9EN,CAAC,CAACI,EAAE,CAAC,qBAAqB,EAAEsB,OAAO,IAAI7C,qBAAqB,CAAC0B,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,KAAKkB,OAAO,CAAClB,EAAE,CAAC,CAAC,CAAC;IAC5GR,CAAC,CAACI,EAAE,CAAC,qBAAqB,EAAE,MAAMvB,qBAAqB,CAAC,EAAE,CAAC,CAAC;IAC5D,OAAO,MAAM;MACXmB,CAAC,CAACG,IAAI,CAAC,aAAa,EAAE;QAAE3B,QAAQ,EAAEiD,MAAM,CAACjD,QAAQ;MAAE,CAAC,CAAC;MACrDwB,CAAC,CAACD,UAAU,CAAC,CAAC;IAChB,CAAC;EACH,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;;EAEd;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI6C,aAAa,CAACM,OAAO,EAAEN,aAAa,CAACM,OAAO,CAACC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACzF,CAAC,EAAE,CAACpB,mBAAmB,EAAEE,kBAAkB,EAAEZ,WAAW,EAAE0B,IAAI,CAAC,CAAC;;EAEhE;EACAjD,SAAS,CAAC,MAAM;IACdsC,SAAS,CAAC,EAAE,CAAC;IACbE,UAAU,CAAC,IAAI,CAAC;IAChBE,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC,EAAE,CAACnB,WAAW,EAAEI,UAAU,EAAEI,QAAQ,CAAC,CAAC;;EAEvC;EACA,MAAMmD,WAAW,GAAIC,CAAC,IAAK;IACzB7C,SAAS,CAAEwB,IAAI,IAAKA,IAAI,GAAGqB,CAAC,CAACC,KAAK,CAAC;IACnC1C,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAM2C,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAIC,MAAM;IACV,IAAI/D,WAAW,KAAK,SAAS,EAAE+D,MAAM,GAAGvC,gBAAgB,CAACI,OAAO,CAAC,KAC5D,IAAI5B,WAAW,KAAK,QAAQ,EAAE+D,MAAM,GAAGtC,eAAe,CAACG,OAAO,CAAC,KAC/D,IAAI5B,WAAW,KAAK,SAAS,EAAE+D,MAAM,GAAGhE,gBAAgB,CAAC6B,OAAO;IACrE,IAAI,CAACmC,MAAM,EAAE;;IAEb;IACA,IAAI/C,OAAO,EAAE;MACX,MAAMgD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElD,OAAO,CAAC;MAEhC,IAAImD,SAAS,GAAG,EAAE;MAClB,IAAInE,WAAW,KAAK,SAAS,EAAE;QAC7BmE,SAAS,GAAG,yDAAyD;MACvE,CAAC,MAAM,IAAInE,WAAW,KAAK,SAAS,EAAE;QACpCgE,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE9D,UAAU,CAAC;QACzC+D,SAAS,GAAG,0DAA0D;MACxE,CAAC,MAAM,IAAInE,WAAW,KAAK,QAAQ,EAAE;QACnCgE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE1D,QAAQ,CAAC;QACrC2D,SAAS,GAAG,iDAAiD;MAC/D;MACA,IAAI;QACF,MAAMtB,GAAG,GAAG,MAAMF,KAAK,CAACwB,SAAS,EAAE;UACjCC,MAAM,EAAE,MAAM;UACdC,IAAI,EAAEL;QACR,CAAC,CAAC;QACF,MAAMM,QAAQ,GAAG,MAAMzB,GAAG,CAACC,IAAI,CAAC,CAAC;QACjC,IAAI9C,WAAW,KAAK,SAAS,EAAE;UAC7B+D,MAAM,CAAC5B,IAAI,CAAC,oBAAoB,EAAE;YAChCoC,OAAO,EAAED,QAAQ,CAACE,OAAO;YACzBC,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAI,MAAM;YACjCC,QAAQ,EAAEhF,IAAI,CAAC6C;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIxC,WAAW,KAAK,SAAS,EAAE;UACpC+D,MAAM,CAAC5B,IAAI,CAAC,qBAAqB,EAAE;YACjCoC,OAAO,EAAED,QAAQ,CAACE,OAAO;YACzBC,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAI,MAAM;YACjCtE,UAAU,EAAEqD,MAAM,CAACrD,UAAU,CAAC;YAC9BuE,QAAQ,EAAEhF,IAAI,CAAC6C;UACjB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLuB,MAAM,CAAC5B,IAAI,CAAC,mBAAmB,EAAE;YAC/BoC,OAAO,EAAED,QAAQ,CAACE,OAAO;YACzBC,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAI,MAAM;YACjClE,QAAQ,EAAEiD,MAAM,CAACjD,QAAQ,CAAC;YAC1BmE,QAAQ,EAAEhF,IAAI,CAAC6C;UACjB,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,MAAM;QACNoC,KAAK,CAAC,uBAAuB,CAAC;MAChC;MACA3D,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIM,YAAY,CAACK,OAAO,EAAEL,YAAY,CAACK,OAAO,CAACiD,KAAK,GAAG,EAAE;MACzD;IACF;IACA;IACA,IAAI/D,MAAM,CAACgE,IAAI,CAAC,CAAC,EAAE;MACjB,IAAI9E,WAAW,KAAK,SAAS,EAAE;QAC7B+D,MAAM,CAAC5B,IAAI,CAAC,oBAAoB,EAAE;UAChCoC,OAAO,EAAEzD,MAAM;UACf2D,IAAI,EAAE,MAAM;UACZE,QAAQ,EAAEhF,IAAI,CAAC6C;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIxC,WAAW,KAAK,SAAS,EAAE;QACpC+D,MAAM,CAAC5B,IAAI,CAAC,qBAAqB,EAAE;UACjCoC,OAAO,EAAEzD,MAAM;UACf2D,IAAI,EAAE,MAAM;UACZrE,UAAU,EAAEqD,MAAM,CAACrD,UAAU,CAAC;UAC9BuE,QAAQ,EAAEhF,IAAI,CAAC6C;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLuB,MAAM,CAAC5B,IAAI,CAAC,mBAAmB,EAAE;UAC/BoC,OAAO,EAAEzD,MAAM;UACf2D,IAAI,EAAE,MAAM;UACZjE,QAAQ,EAAEiD,MAAM,CAACjD,QAAQ,CAAC;UAC1BmE,QAAQ,EAAEhF,IAAI,CAAC6C;QACjB,CAAC,CAAC;MACJ;MACAzB,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC;;EAGD;EACA,MAAMgE,eAAe,GAAG,MAAOC,KAAK,IAAK;IACvC,IAAIhF,WAAW,KAAK,SAAS,EAAE;MAC7B,MAAM2C,KAAK,CAAC,gDAAgDqC,KAAK,EAAE,EAAE;QACnEZ,MAAM,EAAE,QAAQ;QAChBa,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CZ,IAAI,EAAEa,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEzF,IAAI,CAAC6C;QAAG,CAAC;MAC1C,CAAC,CAAC;MACF7B,sBAAsB,CAAE4B,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKwC,KAAK,CAAC,CAAC;IACtE,CAAC,MAAM,IAAIhF,WAAW,KAAK,QAAQ,EAAE;MACnC,MAAM2C,KAAK,CAAC,uCAAuCqC,KAAK,EAAE,EAAE;QAC1DZ,MAAM,EAAE,QAAQ;QAChBa,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CZ,IAAI,EAAEa,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEzF,IAAI,CAAC6C;QAAG,CAAC;MAC1C,CAAC,CAAC;MACF3B,qBAAqB,CAAE0B,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKwC,KAAK,CAAC,CAAC;IACrE,CAAC,MAAM,IAAIhF,WAAW,KAAK,SAAS,EAAE;MACpC,MAAM2C,KAAK,CAAC,+CAA+CqC,KAAK,EAAE,EAAE;QAClEZ,MAAM,EAAE,QAAQ;QAChBa,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CZ,IAAI,EAAEa,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEzF,IAAI,CAAC6C;QAAG,CAAC;MAC1C,CAAC,CAAC;MACF1C,sBAAsB,CAAEyC,IAAI,IAAKA,IAAI,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACF,EAAE,KAAKwC,KAAK,CAAC,CAAC;IACtE;EACF,CAAC;;EAGD;EACA,oBACE1F,OAAA,CAAAE,SAAA;IAAA6F,QAAA,gBAEE/F,OAAA;MACEgG,OAAO,EAAEA,CAAA,KAAM3D,OAAO,CAAC,CAACD,IAAI,CAAE;MAC9B6D,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAEpE,IAAI,GAAG,MAAM,GAAG,SAAS;QACrCqE,KAAK,EAAErE,IAAI,GAAG,SAAS,GAAG,MAAM;QAChCsE,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAExE,IAAI,GAAG,mBAAmB,GAAG,MAAM;QAC3CyE,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,uBAAuB;QACnCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,OAAO,EAAE;MACX,CAAE;MACF,cAAY/E,IAAI,GAAG,YAAY,GAAG,WAAY;MAC9CgF,WAAW,EAAG9C,CAAC,IAAK;QAClBA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACO,UAAU,GAAGpE,IAAI,GAAG,SAAS,GAAG,SAAS;MACjE,CAAE;MACFkF,UAAU,EAAGhD,CAAC,IAAK;QACjBA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACO,UAAU,GAAGpE,IAAI,GAAG,MAAM,GAAG,SAAS;MAC9D,CAAE;MACFmF,OAAO,EAAGjD,CAAC,IAAK;QACdA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACU,SAAS,GAAG,kCAAkC;MACtE,CAAE;MACFa,MAAM,EAAGlD,CAAC,IAAK;QACbA,CAAC,CAAC+C,aAAa,CAACpB,KAAK,CAACU,SAAS,GAAG,gCAAgC;MACpE,CAAE;MAAAZ,QAAA,EAED3D,IAAI,GAAG,GAAG,GAAG;IAAI;MAAAqF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,EAER9F,WAAW,iBACV9B,OAAA;MAAKiG,KAAK,EAAE;QACVC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,GAAG;QACXC,KAAK,EAAE,EAAE;QACTM,QAAQ,EAAE,MAAM;QAChBD,KAAK,EAAE,OAAO;QACdD,UAAU,EAAE,iBAAiB;QAC7BqB,OAAO,EAAE,UAAU;QACnBtB,YAAY,EAAE,KAAK;QACnBuB,UAAU,EAAE,QAAQ;QACpBjB,MAAM,EAAE;MACV,CAAE;MAAAd,QAAA,EAAC;IAEH;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,EAGAxF,IAAI,iBACHpC,OAAA,CAACT,KAAK;MACJwI,EAAE,EAAE;QACF7B,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,GAAG;QACXC,KAAK,EAAE,EAAE;QACTC,KAAK,EAAE,GAAG;QACV2B,SAAS,EAAE,MAAM;QACjBzB,YAAY,EAAE,CAAC;QACfI,SAAS,EAAE,EAAE;QACbE,MAAM,EAAE,IAAI;QACZG,OAAO,EAAE,MAAM;QACfiB,aAAa,EAAE,QAAQ;QACvBC,CAAC,EAAE,CAAC;QACJC,QAAQ,EAAE,QAAQ;QAClB3B,UAAU,EAAE;MACd,CAAE;MAAAT,QAAA,gBAGF/F,OAAA,CAACV,GAAG;QACF0H,OAAO,EAAC,MAAM;QACdC,UAAU,EAAC,QAAQ;QACnBmB,OAAO,EAAC,MAAM;QACdC,EAAE,EAAE,CAAE;QACNC,EAAE,EAAE,GAAI;QACRC,EAAE,EAAE,GAAI;QACRC,YAAY,EAAC,mBAAmB;QAAAzC,QAAA,gBAEhC/F,OAAA,CAACJ,MAAM;UACL6I,OAAO,EAAE/H,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG,MAAO;UAC1DsF,OAAO,EAAEA,CAAA,KAAMrF,cAAc,CAAC,SAAS,CAAE;UACzC8F,KAAK,EAAC,OAAO;UACbiC,IAAI,EAAC,OAAO;UACZX,EAAE,EAAE;YACFxB,YAAY,EAAE,CAAC;YACfoC,UAAU,EAAE,GAAG;YACfhC,SAAS,EAAEjG,WAAW,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;YAC5CkI,EAAE,EAAE,CAAC;YACLP,EAAE,EAAE;UACN,CAAE;UAAAtC,QAAA,EACH;QAED;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5H,OAAA,CAACJ,MAAM;UACL6I,OAAO,EAAE/H,WAAW,KAAK,QAAQ,GAAG,WAAW,GAAG,MAAO;UACzDsF,OAAO,EAAEA,CAAA,KAAMrF,cAAc,CAAC,QAAQ,CAAE;UACxC8F,KAAK,EAAC,SAAS;UACfiC,IAAI,EAAC,OAAO;UACZG,QAAQ,EAAE,CAAC3H,QAAS;UACpB6G,EAAE,EAAE;YACFxB,YAAY,EAAE,CAAC;YACfoC,UAAU,EAAE,GAAG;YACfhC,SAAS,EAAEjG,WAAW,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;YAC3CkI,EAAE,EAAE,CAAC;YACLP,EAAE,EAAE;UACN,CAAE;UAAAtC,QAAA,EACH;QAED;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5H,OAAA,CAACJ,MAAM;UACL6I,OAAO,EAAE/H,WAAW,KAAK,SAAS,GAAG,WAAW,GAAG,MAAO;UAC1DsF,OAAO,EAAEA,CAAA,KAAMrF,cAAc,CAAC,SAAS,CAAE;UACzC8F,KAAK,EAAC,WAAW;UACjBiC,IAAI,EAAC,OAAO;UACZX,EAAE,EAAE;YACFxB,YAAY,EAAE,CAAC;YACfoC,UAAU,EAAE,GAAG;YACfhC,SAAS,EAAEjG,WAAW,KAAK,SAAS,GAAG,CAAC,GAAG,CAAC;YAC5CkI,EAAE,EAAE,CAAC;YACLP,EAAE,EAAE;UACN,CAAE;UAAAtC,QAAA,EACH;QAED;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEN,CAAC,EAELhH,SAAS,CAACkI,MAAM,GAAG,CAAC,iBACnB9I,OAAA,CAACV,GAAG;QAAC8I,OAAO,EAAC,SAAS;QAACC,EAAE,EAAE,CAAE;QAACU,EAAE,EAAE,GAAI;QAAAhD,QAAA,eACpC/F,OAAA;UACEuF,KAAK,EAAEzE,UAAW;UAClBkI,QAAQ,EAAE1E,CAAC,IAAIvD,aAAa,CAACoD,MAAM,CAACG,CAAC,CAAC2E,MAAM,CAAC1D,KAAK,CAAC,CAAE;UACrDU,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbwB,OAAO,EAAE,UAAU;YACnBtB,YAAY,EAAE,CAAC;YACfK,MAAM,EAAE,gBAAgB;YACxB+B,UAAU,EAAE;UACd,CAAE;UAAA5C,QAAA,EAEDnF,SAAS,CAACiD,GAAG,CAACnB,CAAC,iBACd1C,OAAA;YAAQuF,KAAK,EAAE7C,CAAC,CAACQ,EAAG;YAAA6C,QAAA,EAAarD,CAAC,CAACsB;UAAI,GAAbtB,CAAC,CAACQ,EAAE;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAlH,WAAW,KAAK,QAAQ,IAAIM,OAAO,CAAC8H,MAAM,GAAG,CAAC,iBAC7C9I,OAAA,CAACV,GAAG;QAAC8I,OAAO,EAAC,SAAS;QAACC,EAAE,EAAE,CAAE;QAACU,EAAE,EAAE,GAAI;QAAAhD,QAAA,eACpC/F,OAAA;UACEuF,KAAK,EAAErE,QAAS;UAChB8H,QAAQ,EAAE1E,CAAC,IAAInD,WAAW,CAACgD,MAAM,CAACG,CAAC,CAAC2E,MAAM,CAAC1D,KAAK,CAAC,CAAE;UACnDU,KAAK,EAAE;YACLI,KAAK,EAAE,MAAM;YACbwB,OAAO,EAAE,UAAU;YACnBtB,YAAY,EAAE,CAAC;YACfK,MAAM,EAAE,gBAAgB;YACxB+B,UAAU,EAAE;UACd,CAAE;UAAA5C,QAAA,EAED/E,OAAO,CAAC6C,GAAG,CAACnB,CAAC,iBACZ1C,OAAA;YAAQuF,KAAK,EAAE7C,CAAC,CAACQ,EAAG;YAAA6C,QAAA,EAAarD,CAAC,CAACwG,KAAK,IAAI,UAAUxG,CAAC,CAACQ,EAAE;UAAE,GAAlCR,CAAC,CAACQ,EAAE;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAuC,CACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,eAGD5H,OAAA,CAACV,GAAG;QACFyI,EAAE,EAAE;UACFG,CAAC,EAAE,CAAC;UACJI,EAAE,EAAE,GAAG;UACPa,IAAI,EAAE,CAAC;UACPC,SAAS,EAAE,MAAM;UACjB5C,UAAU,EAAE,SAAS;UACrBgC,YAAY,EAAE,gBAAgB;UAC9Ba,SAAS,EAAE,GAAG;UACdC,cAAc,EAAE,MAAM;UACtB,sBAAsB,EAAE;YACtBjD,KAAK,EAAE,KAAK;YACZG,UAAU,EAAE,SAAS;YACrBD,YAAY,EAAE;UAChB,CAAC;UACD,4BAA4B,EAAE;YAC5BC,UAAU,EAAE,SAAS;YACrBD,YAAY,EAAE;UAChB;QACF,CAAE;QAAAR,QAAA,eAEF/F,OAAA,CAACP,KAAK;UAAC8J,OAAO,EAAE,CAAE;UAAAxD,QAAA,GACf,CACCrF,WAAW,KAAK,SAAS,GACrBU,mBAAmB,GACnBV,WAAW,KAAK,QAAQ,GACtBY,kBAAkB,GAClBf,mBAAmB,EACzBsD,GAAG,CAAC,CAACb,GAAG,EAAEwG,CAAC;YAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,qBAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,aAAA,EAAAC,aAAA,EAAAC,aAAA;YAAA,oBACXvK,OAAA,CAACV,GAAG;cAEFyI,EAAE,EAAE;gBACFf,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,YAAY;gBACxBuD,GAAG,EAAE,GAAG;gBACRpC,OAAO,EAAE,EAAAqB,WAAA,GAAAzG,GAAG,CAACyH,MAAM,cAAAhB,WAAA,uBAAVA,WAAA,CAAYvG,EAAE,MAAK7C,IAAI,CAAC6C,EAAE,GAAG,SAAS,GAAG,MAAM;gBACxDqD,YAAY,EAAE,CAAC;gBACfI,SAAS,EAAE,EAAA+C,YAAA,GAAA1G,GAAG,CAACyH,MAAM,cAAAf,YAAA,uBAAVA,YAAA,CAAYxG,EAAE,MAAK7C,IAAI,CAAC6C,EAAE,GAAG,mBAAmB,GAAG,qBAAqB;gBACnF0D,MAAM,EAAE,mBAAmB;gBAC3ByB,EAAE,EAAE,GAAG;gBACPU,EAAE,EAAE,CAAC;gBACL2B,EAAE,EAAE,EAAAf,YAAA,GAAA3G,GAAG,CAACyH,MAAM,cAAAd,YAAA,uBAAVA,YAAA,CAAYzG,EAAE,MAAK7C,IAAI,CAAC6C,EAAE,GAAG,CAAC,GAAG,MAAM;gBAC3CyH,EAAE,EAAE,EAAAf,YAAA,GAAA5G,GAAG,CAACyH,MAAM,cAAAb,YAAA,uBAAVA,YAAA,CAAY1G,EAAE,MAAK7C,IAAI,CAAC6C,EAAE,GAAG,MAAM,GAAG,CAAC;gBAC3C0H,QAAQ,EAAE;cACZ,CAAE;cAAA7E,QAAA,GAGD,CAAA8D,YAAA,GAAA7G,GAAG,CAACyH,MAAM,cAAAZ,YAAA,eAAVA,YAAA,CAAYgB,UAAU,gBAEnB7K,OAAA;gBACE8K,GAAG,EACD,CAAAhB,YAAA,GAAA9G,GAAG,CAACyH,MAAM,cAAAX,YAAA,gBAAAC,qBAAA,GAAVD,YAAA,CAAYe,UAAU,cAAAd,qBAAA,eAAtBA,qBAAA,CAAwBgB,UAAU,CAAC,MAAM,CAAC,GACtC/H,GAAG,CAACyH,MAAM,CAACI,UAAU,GACrB,wBAAwB,EAAAb,YAAA,GAAAhH,GAAG,CAACyH,MAAM,cAAAT,YAAA,uBAAVA,YAAA,CAAYa,UAAU,KAAI,2BAA2B,EAClF;gBACDG,GAAG,GAAAf,YAAA,GAAEjH,GAAG,CAACyH,MAAM,cAAAR,YAAA,uBAAVA,YAAA,CAAYjG,IAAK;gBACtBiC,KAAK,EAAE;kBAAEI,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE,KAAK;kBAAE0E,SAAS,EAAE;gBAAE;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,gBAEF5H,OAAA,CAACH,MAAM;gBAACkI,EAAE,EAAE;kBAAE1B,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAE8B,OAAO,EAAE,SAAS;kBAAE8C,EAAE,EAAE;gBAAK,CAAE;gBAAAnF,QAAA,EACjE,EAAAmE,YAAA,GAAAlH,GAAG,CAACyH,MAAM,cAAAP,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAYlG,IAAI,cAAAmG,iBAAA,wBAAAC,kBAAA,GAAhBD,iBAAA,CAAmB,CAAC,CAAC,cAAAC,kBAAA,uBAArBA,kBAAA,CAAuBe,WAAW,CAAC,CAAC,KAAI;cAAG;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CACT,eAEH5H,OAAA,CAACV,GAAG;gBAACyI,EAAE,EAAE;kBAAEoB,IAAI,EAAE;gBAAE,CAAE;gBAAApD,QAAA,gBACnB/F,OAAA,CAACV,GAAG;kBAAC0H,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAAAlB,QAAA,gBACrC/F,OAAA,CAACR,UAAU;oBAACiJ,OAAO,EAAC,WAAW;oBAACE,UAAU,EAAC,MAAM;oBAAClC,KAAK,EAAC,SAAS;oBAACsB,EAAE,EAAE;sBAAErB,QAAQ,EAAE;oBAAG,CAAE;oBAAAX,QAAA,EACpF,EAAAsE,aAAA,GAAArH,GAAG,CAACyH,MAAM,cAAAJ,aAAA,uBAAVA,aAAA,CAAYrG,IAAI,KAAI;kBAAS;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,eACb5H,OAAA,CAACR,UAAU;oBACTuI,EAAE,EAAE;sBACFtB,KAAK,EAAE,MAAM;sBACbC,QAAQ,EAAE,IAAI;sBACdiC,UAAU,EAAE,GAAG;sBACfgC,EAAE,EAAE,CAAC;sBACLO,EAAE,EAAE;oBACN,CAAE;oBAAAnF,QAAA,GAED,CAAAuE,aAAA,GAAAtH,GAAG,CAACyH,MAAM,cAAAH,aAAA,eAAVA,aAAA,CAAYc,IAAI,GAAG,IAAI,GAAGpI,GAAG,CAACyH,MAAM,CAACW,IAAI,GAAG,EAAE,EAC9CpI,GAAG,CAACqI,SAAS,iBACZrL,OAAA;sBAAMiG,KAAK,EAAE;wBAAEqF,UAAU,EAAE;sBAAE,CAAE;sBAAAvF,QAAA,EAC5B,IAAIwF,IAAI,CAACvI,GAAG,CAACqI,SAAS,CAAC,CAACG,kBAAkB,CAAC,EAAE,EAAE;wBAAEC,IAAI,EAAE,SAAS;wBAAEC,MAAM,EAAE;sBAAU,CAAC;oBAAC;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,EACZ,EAAA2C,aAAA,GAAAvH,GAAG,CAACyH,MAAM,cAAAF,aAAA,uBAAVA,aAAA,CAAYrH,EAAE,MAAK7C,IAAI,CAAC6C,EAAE,iBACzBlD,OAAA,CAACL,UAAU;oBACT+I,IAAI,EAAC,OAAO;oBACZ1C,OAAO,EAAEA,CAAA,KAAMP,eAAe,CAACzC,GAAG,CAACE,EAAE,CAAE;oBACvCuD,KAAK,EAAC,OAAO;oBACbsB,EAAE,EAAE;sBAAE4C,EAAE,EAAE;oBAAO,CAAE;oBAAA5E,QAAA,eAEnB/F,OAAA,CAAC2L,UAAU;sBAACjF,QAAQ,EAAC;oBAAO;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAEN5H,OAAA,CAACV,GAAG;kBAAC4L,EAAE,EAAE,GAAI;kBAAAnF,QAAA,GACV/C,GAAG,CAACmC,IAAI,KAAK,MAAM,iBAClBnF,OAAA,CAACR,UAAU;oBAACuI,EAAE,EAAE;sBAAErB,QAAQ,EAAE,EAAE;sBAAED,KAAK,EAAE;oBAAO,CAAE;oBAAAV,QAAA,EAC7C/C,GAAG,CAACiC;kBAAO;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACb,EACA5E,GAAG,CAACmC,IAAI,KAAK,OAAO,iBACnBnF,OAAA;oBAAK8K,GAAG,EAAE9H,GAAG,CAACiC,OAAQ;oBAAC+F,GAAG,EAAC,KAAK;oBAAC/E,KAAK,EAAE;sBAAE2E,QAAQ,EAAE,GAAG;sBAAErE,YAAY,EAAE,CAAC;sBAAE0E,SAAS,EAAE;oBAAE;kBAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC5F,EACA5E,GAAG,CAACmC,IAAI,KAAK,OAAO,iBACnBnF,OAAA;oBAAO4L,QAAQ;oBAACd,GAAG,EAAE9H,GAAG,CAACiC,OAAQ;oBAACgB,KAAK,EAAE;sBAAE2E,QAAQ,EAAE,GAAG;sBAAEK,SAAS,EAAE;oBAAE;kBAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC5E,EACA5E,GAAG,CAACmC,IAAI,KAAK,OAAO,iBACnBnF,OAAA;oBAAO4L,QAAQ;oBAACd,GAAG,EAAE9H,GAAG,CAACiC,OAAQ;oBAACgB,KAAK,EAAE;sBAAE2E,QAAQ,EAAE,GAAG;sBAAErE,YAAY,EAAE,CAAC;sBAAE0E,SAAS,EAAE;oBAAE;kBAAE;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC7F,EACA5E,GAAG,CAACmC,IAAI,KAAK,MAAM,iBAClBnF,OAAA;oBAAG6L,IAAI,EAAE7I,GAAG,CAACiC,OAAQ;oBAACgE,MAAM,EAAC,QAAQ;oBAAC6C,GAAG,EAAC,qBAAqB;oBAAC7F,KAAK,EAAE;sBAAEe,OAAO,EAAE,OAAO;sBAAEiE,SAAS,EAAE,CAAC;sBAAExE,KAAK,EAAE;oBAAU,CAAE;oBAAAV,QAAA,GAAC,eACxH,EAAC/C,GAAG,CAACiC,OAAO,CAAC8G,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;kBAAA;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAxFD5E,GAAG,CAACE,EAAE,IAAIsG,CAAC;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyFb,CAAC;UAAA,CACP,CAAC,eACF5H,OAAA;YAAKiM,GAAG,EAAEjK;UAAc;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN5H,OAAA,CAACV,GAAG;QAACyI,EAAE,EAAE;UACPG,CAAC,EAAE,CAAC;UACJlB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBuD,GAAG,EAAE,CAAC;UACNhE,UAAU,EAAE,MAAM;UAClB0F,SAAS,EAAE;QACb,CAAE;QAAAnG,QAAA,gBACA/F,OAAA,CAACN,SAAS;UACRyM,SAAS;UACT5G,KAAK,EAAE/D,MAAO;UACdkH,IAAI,EAAC,OAAO;UACZ0D,WAAW,EAAC,2BAAmB;UAC/BpD,QAAQ,EAAE1E,CAAC,IAAI7C,SAAS,CAAC6C,CAAC,CAAC2E,MAAM,CAAC1D,KAAK,CAAE;UACzC8G,SAAS,EAAE/H,CAAC,IAAIA,CAAC,CAACgI,GAAG,KAAK,OAAO,IAAI9H,cAAc,CAAC,CAAE;UACtDuD,EAAE,EAAE;YAAEvB,UAAU,EAAE,SAAS;YAAED,YAAY,EAAE;UAAE,CAAE;UAC/CgG,UAAU,EAAE;YAAEtG,KAAK,EAAE;cAAES,QAAQ,EAAE;YAAG;UAAE;QAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACF5H,OAAA,CAACL,UAAU;UAACqG,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC2K,CAAC,IAAI,CAACA,CAAC,CAAE;UAAAzG,QAAA,eAC/C/F,OAAA;YAAMoL,IAAI,EAAC,KAAK;YAAC,cAAW,OAAO;YAAArF,QAAA,EAAC;UAAE;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACb5H,OAAA,CAACL,UAAU;UAAC8M,SAAS,EAAC,OAAO;UAAChG,KAAK,EAAE/E,OAAO,GAAG,SAAS,GAAG,SAAU;UAAAqE,QAAA,gBACnE/F,OAAA,CAACF,qBAAqB;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzB5H,OAAA;YACE0M,MAAM;YACNT,GAAG,EAAEhK,YAAa;YAClBkD,IAAI,EAAC,MAAM;YACXwH,MAAM,EAAC,yCAAyC;YAChD3D,QAAQ,EAAE1E,CAAC,IAAI3C,UAAU,CAAC2C,CAAC,CAAC2E,MAAM,CAAC2D,KAAK,CAAC,CAAC,CAAC;UAAE;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eACb5H,OAAA,CAACJ,MAAM;UAACoG,OAAO,EAAExB,cAAe;UAACiE,OAAO,EAAC,WAAW;UAAChC,KAAK,EAAE/F,WAAW,KAAK,SAAS,GAAG,OAAO,GAAG,SAAU;UAACmI,QAAQ,EAAE,CAACrH,MAAM,CAACgE,IAAI,CAAC,CAAC,IAAI,CAAC9D,OAAQ;UAACqG,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEM,UAAU,EAAE;UAAI,CAAE;UAAA5C,QAAA,EAAC;QAEnL;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EACLhG,SAAS,iBACR5B,OAAA,CAACV,GAAG;QAACyI,EAAE,EAAE;UAAE7B,QAAQ,EAAE,UAAU;UAAEC,MAAM,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAES,MAAM,EAAE;QAAG,CAAE;QAAAd,QAAA,eACnE/F,OAAA,CAAC6M,WAAW;UAACC,YAAY,EAAEzI,WAAY;UAAC0I,eAAe,EAAE;QAAM;UAAAtF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CACN,EACAlG,OAAO,iBACN1B,OAAA,CAACR,UAAU;QAACiH,KAAK,EAAC,SAAS;QAACC,QAAQ,EAAE,EAAG;QAACiE,EAAE,EAAE,CAAE;QAACO,EAAE,EAAE,GAAI;QAAAnF,QAAA,GAAC,iCAC/B,eAAA/F,OAAA;UAAA+F,QAAA,EAASrE,OAAO,CAACsC;QAAI;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR;EAAA,eACD,CAAC;AAGP;AAACtH,EAAA,CAvnBuBF,uBAAuB;AAAA4M,EAAA,GAAvB5M,uBAAuB;AAAA,IAAA4M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}