{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\seancefeedbacklist.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography, Paper, Divider, Dialog, DialogTitle, DialogContent, Stack, Button } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { DataGrid } from '@mui/x-data-grid';\nimport { Feedback as FeedbackIcon } from \"@mui/icons-material\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FeedbackList = () => {\n  _s();\n  var _selectedFeedback$ans;\n  const {\n    t\n  } = useTranslation('seances');\n  const {\n    id: seanceId\n  } = useParams();\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\n  const reloadFeedbacks = () => {\n    if (seanceId) {\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`).then(res => {\n        console.log(\"Feedbacks reçus:\", res.data);\n        // Remove duplicate email filtering for debugging\n        const mapped = res.data.map((fb, idx) => ({\n          ...fb,\n          id: fb.id || fb._id || idx,\n          // Always use idx if no id/_id\n          studentName: fb.nom || '',\n          studentEmail: fb.email || '',\n          content: fb.feedback || '',\n          sessionComments: fb.sessionComments,\n          trainerComments: fb.trainerComments,\n          teamComments: fb.teamComments,\n          suggestions: fb.suggestions,\n          answers: fb.answers || []\n        }));\n        console.log(\"Feedbacks mappés:\", mapped); // Debug output\n        setFeedbacks(mapped);\n      }).catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\n    }\n  };\n  useEffect(() => {\n    reloadFeedbacks();\n  }, [seanceId]);\n  const feedbackColumns = [{\n    field: 'id',\n    headerName: t('id'),\n    width: 70\n  }, {\n    field: 'studentName',\n    headerName: t('studentName'),\n    width: 200\n  }, {\n    field: 'studentEmail',\n    headerName: t('studentEmail'),\n    width: 250\n  }, {\n    field: 'fullFeedback',\n    headerName: t('fullFeedback'),\n    width: 250,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      size: \"small\",\n      onClick: () => {\n        setSelectedFeedback(params.row);\n        setFeedbackDialogOpen(true);\n      },\n      children: t('showMore')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'averageRating',\n    headerName: t('averageRating'),\n    width: 180,\n    renderCell: params => {\n      const answers = params.row.answers || [];\n      const numericAnswers = answers.map(qa => Number(qa.answer)).filter(val => !isNaN(val) && val >= 1 && val <= 5);\n      if (numericAnswers.length === 0) return t('noRating');\n      const avg = numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length;\n      const rounded = Math.round(avg);\n      const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n      const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 22\n          },\n          children: moodEmojis[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginLeft: 4\n          },\n          children: moodLabels[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888',\n            marginLeft: 4\n          },\n          children: [\"(\", avg.toFixed(2), \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 2,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(FeedbackIcon, {\n        fontSize: \"large\",\n        sx: {\n          verticalAlign: 'middle',\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), t('feedbackList')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 500,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: feedbacks,\n          columns: feedbackColumns,\n          pageSize: 7,\n          rowsPerPageOptions: [5, 10, 20],\n          disableSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: feedbackDialogOpen,\n      onClose: () => setFeedbackDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FeedbackIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [t('feedbackFrom'), \" \", /*#__PURE__*/_jsxDEV(\"b\", {\n              children: selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 35\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentEmail\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        sx: {\n          bgcolor: \"#f8fafc\",\n          maxHeight: 500\n        },\n        children: /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"text.secondary\",\n              children: [t('date'), \": \", (selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.createdAt) && new Date(selectedFeedback.createdAt).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this), (selectedFeedback === null || selectedFeedback === void 0 ? void 0 : (_selectedFeedback$ans = selectedFeedback.answers) === null || _selectedFeedback$ans === void 0 ? void 0 : _selectedFeedback$ans.length) > 0 && (() => {\n            // Définition des sections thématiques\n            const sections = [{\n              title: t('sessionSection'),\n              keywords: ['note de la session', 'organisation', 'objectifs', 'durée', 'durée de la séance', 'qualité du contenu', 'commentaires sur la session']\n            }, {\n              title: t('trainerSection'),\n              keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\n            }, {\n              title: t('teamSection'),\n              keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\n            }, {\n              title: t('suggestionsSection'),\n              keywords: ['suggestions', 'amélioration', 'recommanderait']\n            }];\n            // Grouper les réponses par section avec un matching robuste\n            function normalize(str) {\n              return str.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\n              .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\n            }\n            const groupedAnswers = selectedFeedback && selectedFeedback.answers ? sections.map(section => ({\n              ...section,\n              answers: selectedFeedback.answers.filter(qa => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\n            })) : [];\n            // Réponses non classées\n            const otherAnswers = selectedFeedback && selectedFeedback.answers ? selectedFeedback.answers.filter(qa => !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))) : [];\n            // Emoji/label pour toutes les réponses numériques (1-5)\n            const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n            const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n            return /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [groupedAnswers.map((section, idx) => section.answers.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    mb: 1\n                  },\n                  children: section.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  spacing: 2,\n                  children: section.answers.map((qa, qidx) => {\n                    let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                    let value = isNumeric ? Number(qa.answer) : null;\n                    return /*#__PURE__*/_jsxDEV(Paper, {\n                      elevation: 1,\n                      sx: {\n                        p: 2,\n                        bgcolor: \"#fff\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        fontWeight: \"bold\",\n                        gutterBottom: true,\n                        children: qa.question\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 33\n                      }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          fontSize: 32,\n                          children: moodEmojis[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 207,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          fontWeight: \"bold\",\n                          children: moodLabels[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 208,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          color: \"text.secondary\",\n                          children: [\"(\", value, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 209,\n                          columnNumber: 37\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 35\n                      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                        style: {\n                          whiteSpace: 'pre-line'\n                        },\n                        children: qa.answer || t('noAnswer')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 212,\n                        columnNumber: 35\n                      }, this)]\n                    }, qidx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 31\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 25\n                }, this)]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 23\n              }, this)), otherAnswers.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                mb: 2,\n                children: [/*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    mb: 1\n                  },\n                  children: t('otherSection')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                  spacing: 2,\n                  children: otherAnswers.map((qa, qidx) => {\n                    let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                    let value = isNumeric ? Number(qa.answer) : null;\n                    return /*#__PURE__*/_jsxDEV(Paper, {\n                      elevation: 1,\n                      sx: {\n                        p: 2,\n                        bgcolor: \"#fff\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        fontWeight: \"bold\",\n                        gutterBottom: true,\n                        children: qa.question\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 31\n                      }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          fontSize: 32,\n                          children: moodEmojis[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 235,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          fontWeight: \"bold\",\n                          children: moodLabels[value - 1]\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 236,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          color: \"text.secondary\",\n                          children: [\"(\", value, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 237,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 234,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                        style: {\n                          whiteSpace: 'pre-line'\n                        },\n                        children: qa.answer || t('noAnswer')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 33\n                      }, this)]\n                    }, qidx, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 29\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true);\n          })(), /*#__PURE__*/_jsxDEV(Divider, {\n            children: t('averageRating')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), (() => {\n            // Récupère toutes les réponses numériques (1-5)\n            const numericAnswers = selectedFeedback && selectedFeedback.answers ? selectedFeedback.answers.map(qa => Number(qa.answer)).filter(val => !isNaN(val) && val >= 1 && val <= 5) : [];\n            if (numericAnswers.length === 0) {\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: t('noRating')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 24\n              }, this);\n            }\n            const avg = numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length;\n            const rounded = Math.round(avg);\n            const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n            const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n            return /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              mt: 1,\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                fontSize: 32,\n                children: moodEmojis[rounded - 1]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                fontWeight: \"bold\",\n                children: moodLabels[rounded - 1]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                children: [\"(\", avg.toFixed(2), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(FeedbackList, \"pNQGhhrcD8e4HlzoelZrTU8avJQ=\", false, function () {\n  return [useTranslation, useParams];\n});\n_c = FeedbackList;\nexport default FeedbackList;\nvar _c;\n$RefreshReg$(_c, \"FeedbackList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Box", "Typography", "Paper", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "useTranslation", "axios", "DataGrid", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FeedbackList", "_s", "_selectedFeedback$ans", "t", "id", "seanceId", "feedbacks", "setFeedbacks", "selectedFeedback", "setSelectedFeedback", "feedbackDialogOpen", "setFeedbackDialogOpen", "reloadFeedbacks", "get", "then", "res", "console", "log", "data", "mapped", "map", "fb", "idx", "_id", "studentName", "nom", "studentEmail", "email", "content", "feedback", "sessionComments", "trainerComments", "teamComments", "suggestions", "answers", "catch", "err", "error", "feedbackColumns", "field", "headerName", "width", "renderCell", "params", "variant", "size", "onClick", "row", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "numericAnswers", "qa", "Number", "answer", "filter", "val", "isNaN", "length", "avg", "reduce", "a", "b", "rounded", "Math", "round", "moodEmojis", "<PERSON><PERSON><PERSON><PERSON>", "style", "display", "alignItems", "gap", "fontSize", "fontWeight", "marginLeft", "color", "toFixed", "p", "mb", "sx", "verticalAlign", "mr", "height", "rows", "columns", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "direction", "spacing", "dividers", "bgcolor", "maxHeight", "createdAt", "Date", "toLocaleString", "sections", "title", "keywords", "normalize", "str", "toLowerCase", "replace", "groupedAnswers", "section", "some", "keyword", "question", "includes", "otherAnswers", "qidx", "isNumeric", "value", "elevation", "gutterBottom", "whiteSpace", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/seancefeedbacklist.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Divider,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  Stack,\r\n  Button,\r\n} from \"@mui/material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport { Feedback as FeedbackIcon } from \"@mui/icons-material\";\r\n\r\nconst FeedbackList = () => {\r\n  const { t } = useTranslation('seances');\r\n  const { id: seanceId } = useParams();\r\n  const [feedbacks, setFeedbacks] = useState([]);\r\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\r\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\r\n\r\n  const reloadFeedbacks = () => {\r\n    if (seanceId) {\r\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`)\r\n        .then(res => {\r\n          console.log(\"Feedbacks reçus:\", res.data);\r\n          // Remove duplicate email filtering for debugging\r\n          const mapped = res.data.map((fb, idx) => ({\r\n            ...fb,\r\n            id: fb.id || fb._id || idx, // Always use idx if no id/_id\r\n            studentName: fb.nom || '',\r\n            studentEmail: fb.email || '',\r\n            content: fb.feedback || '',\r\n            sessionComments: fb.sessionComments,\r\n            trainerComments: fb.trainerComments,\r\n            teamComments: fb.teamComments,\r\n            suggestions: fb.suggestions,\r\n            answers: fb.answers || [],\r\n          }));\r\n          console.log(\"Feedbacks mappés:\", mapped); // Debug output\r\n          setFeedbacks(mapped);\r\n        })\r\n        .catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    reloadFeedbacks();\r\n  }, [seanceId]);\r\n\r\n  const feedbackColumns = [\r\n    { field: 'id', headerName: t('id'), width: 70 },\r\n    { field: 'studentName', headerName: t('studentName'), width: 200 },\r\n    { field: 'studentEmail', headerName: t('studentEmail'), width: 250 },\r\n    {\r\n      field: 'fullFeedback',\r\n      headerName: t('fullFeedback'),\r\n      width: 250,\r\n      renderCell: (params) => (\r\n        <Button\r\n          variant=\"outlined\"\r\n          size=\"small\"\r\n          onClick={() => {\r\n            setSelectedFeedback(params.row);\r\n            setFeedbackDialogOpen(true);\r\n          }}\r\n        >\r\n          {t('showMore')}\r\n        </Button>\r\n      ),\r\n    },\r\n    {\r\n      field: 'averageRating',\r\n      headerName: t('averageRating'),\r\n      width: 180,\r\n      renderCell: (params) => {\r\n        const answers = params.row.answers || [];\r\n        const numericAnswers = answers\r\n          .map(qa => Number(qa.answer))\r\n          .filter(val => !isNaN(val) && val >= 1 && val <= 5);\r\n        if (numericAnswers.length === 0) return t('noRating');\r\n        const avg = numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length;\r\n        const rounded = Math.round(avg);\r\n        const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n        const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n        return (\r\n          <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>\r\n            <span style={{ fontSize: 22 }}>{moodEmojis[rounded - 1]}</span>\r\n            <span style={{ fontWeight: 'bold', marginLeft: 4 }}>{moodLabels[rounded - 1]}</span>\r\n            <span style={{ color: '#888', marginLeft: 4 }}>({avg.toFixed(2)})</span>\r\n          </span>\r\n        );\r\n      }\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Box p={2}>\r\n      <Typography variant=\"h4\" mb={3}>\r\n        <FeedbackIcon fontSize=\"large\" sx={{ verticalAlign: 'middle', mr: 2 }} />\r\n        {t('feedbackList')}\r\n      </Typography>\r\n\r\n      <Paper sx={{ p: 3 }}>\r\n        <Box sx={{ height: 500, width: '100%' }}>\r\n          <DataGrid\r\n            rows={feedbacks}\r\n            columns={feedbackColumns}\r\n            pageSize={7}\r\n            rowsPerPageOptions={[5, 10, 20]}\r\n            disableSelectionOnClick\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Feedback Dialog */}\r\n      <Dialog open={feedbackDialogOpen} onClose={() => setFeedbackDialogOpen(false)} maxWidth=\"sm\" fullWidth>\r\n        <DialogTitle>\r\n          <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\r\n            <FeedbackIcon color=\"primary\" />\r\n            <Box>\r\n              {t('feedbackFrom')} <b>{selectedFeedback?.studentName}</b>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                {selectedFeedback?.studentEmail}\r\n              </Typography>\r\n            </Box>\r\n          </Stack>\r\n        </DialogTitle>\r\n        <DialogContent dividers sx={{ bgcolor: \"#f8fafc\", maxHeight: 500 }}>\r\n          <Stack spacing={2}>\r\n            <Box>\r\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\r\n                {t('date')}: {selectedFeedback?.createdAt && new Date(selectedFeedback.createdAt).toLocaleString()}\r\n              </Typography>\r\n            </Box>\r\n            {selectedFeedback?.answers?.length > 0 && (() => {\r\n              // Définition des sections thématiques\r\n              const sections = [\r\n                {\r\n                  title: t('sessionSection'),\r\n                  keywords: [\r\n                    'note de la session',\r\n                    'organisation',\r\n                    'objectifs',\r\n                    'durée',\r\n                    'durée de la séance',\r\n                    'qualité du contenu',\r\n                    'commentaires sur la session'\r\n                  ]\r\n                },\r\n                {\r\n                  title: t('trainerSection'),\r\n                  keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\r\n                },\r\n                {\r\n                  title: t('teamSection'),\r\n                  keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\r\n                },\r\n                {\r\n                  title: t('suggestionsSection'),\r\n                  keywords: ['suggestions', 'amélioration', 'recommanderait']\r\n                }\r\n              ];\r\n              // Grouper les réponses par section avec un matching robuste\r\n              function normalize(str) {\r\n                return str\r\n                  .toLowerCase()\r\n                  .normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\r\n                  .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\r\n              }\r\n              const groupedAnswers = selectedFeedback && selectedFeedback.answers ? sections.map(section => ({\r\n                ...section,\r\n                answers: selectedFeedback.answers.filter(qa =>\r\n                  section.keywords.some(keyword =>\r\n                    normalize(qa.question).includes(normalize(keyword))\r\n                  )\r\n                )\r\n              })) : [];\r\n              // Réponses non classées\r\n              const otherAnswers = selectedFeedback && selectedFeedback.answers ? selectedFeedback.answers.filter(qa =>\r\n                !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\r\n              ) : [];\r\n              // Emoji/label pour toutes les réponses numériques (1-5)\r\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n              return (\r\n                <>\r\n                  {groupedAnswers.map((section, idx) =>\r\n                    section.answers.length > 0 && (\r\n                      <Box key={idx} mb={2}>\r\n                        <Divider sx={{ mb: 1 }}>{section.title}</Divider>\r\n                        <Stack spacing={2}>\r\n                          {section.answers.map((qa, qidx) => {\r\n                            let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                            let value = isNumeric ? Number(qa.answer) : null;\r\n                            return (\r\n                              <Paper key={qidx} elevation={1} sx={{ p: 2, bgcolor: \"#fff\" }}>\r\n                                <Typography fontWeight=\"bold\" gutterBottom>\r\n                                  {qa.question}\r\n                                </Typography>\r\n                                {isNumeric ? (\r\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                                    <Typography fontSize={32}>{moodEmojis[value - 1]}</Typography>\r\n                                    <Typography fontWeight=\"bold\">{moodLabels[value - 1]}</Typography>\r\n                                    <Typography color=\"text.secondary\">({value})</Typography>\r\n                                  </Box>\r\n                                ) : (\r\n                                  <Typography style={{ whiteSpace: 'pre-line' }}>{qa.answer || t('noAnswer')}</Typography>\r\n                                )}\r\n                              </Paper>\r\n                            );\r\n                          })}\r\n                        </Stack>\r\n                      </Box>\r\n                    )\r\n                  )}\r\n                  {otherAnswers.length > 0 && (\r\n                    <Box mb={2}>\r\n                      <Divider sx={{ mb: 1 }}>{t('otherSection')}</Divider>\r\n                      <Stack spacing={2}>\r\n                        {otherAnswers.map((qa, qidx) => {\r\n                          let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                          let value = isNumeric ? Number(qa.answer) : null;\r\n                          return (\r\n                            <Paper key={qidx} elevation={1} sx={{ p: 2, bgcolor: \"#fff\" }}>\r\n                              <Typography fontWeight=\"bold\" gutterBottom>\r\n                                {qa.question}\r\n                              </Typography>\r\n                              {isNumeric ? (\r\n                                <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                                  <Typography fontSize={32}>{moodEmojis[value - 1]}</Typography>\r\n                                  <Typography fontWeight=\"bold\">{moodLabels[value - 1]}</Typography>\r\n                                  <Typography color=\"text.secondary\">({value})</Typography>\r\n                                </Box>\r\n                              ) : (\r\n                                <Typography style={{ whiteSpace: 'pre-line' }}>{qa.answer || t('noAnswer')}</Typography>\r\n                              )}\r\n                            </Paper>\r\n                          );\r\n                        })}\r\n                      </Stack>\r\n                    </Box>\r\n                  )}\r\n                </>\r\n              );\r\n            })()}\r\n            {/* Note moyenne de feedback */}\r\n            <Divider>{t('averageRating')}</Divider>\r\n            {(() => {\r\n              // Récupère toutes les réponses numériques (1-5)\r\n              const numericAnswers = selectedFeedback && selectedFeedback.answers ? selectedFeedback.answers\r\n                .map(qa => Number(qa.answer))\r\n                .filter(val => !isNaN(val) && val >= 1 && val <= 5) : [];\r\n              if (numericAnswers.length === 0) {\r\n                return <Typography color=\"text.secondary\">{t('noRating')}</Typography>;\r\n              }\r\n              const avg = numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length;\r\n              const rounded = Math.round(avg);\r\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n              return (\r\n                <Box display=\"flex\" alignItems=\"center\" gap={1} mt={1} mb={2}>\r\n                  <Typography fontSize={32}>{moodEmojis[rounded - 1]}</Typography>\r\n                  <Typography fontWeight=\"bold\">{moodLabels[rounded - 1]}</Typography>\r\n                  <Typography color=\"text.secondary\">({avg.toFixed(2)})</Typography>\r\n                </Box>\r\n              );\r\n            })()}\r\n          </Stack>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default FeedbackList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,QAAQ,IAAIC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACzB,MAAM;IAAEC;EAAE,CAAC,GAAGZ,cAAc,CAAC,SAAS,CAAC;EACvC,MAAM;IAAEa,EAAE,EAAEC;EAAS,CAAC,GAAGxB,SAAS,CAAC,CAAC;EACpC,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMgC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIP,QAAQ,EAAE;MACZb,KAAK,CAACqB,GAAG,CAAC,sDAAsDR,QAAQ,EAAE,CAAC,CACxES,IAAI,CAACC,GAAG,IAAI;QACXC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,GAAG,CAACG,IAAI,CAAC;QACzC;QACA,MAAMC,MAAM,GAAGJ,GAAG,CAACG,IAAI,CAACE,GAAG,CAAC,CAACC,EAAE,EAAEC,GAAG,MAAM;UACxC,GAAGD,EAAE;UACLjB,EAAE,EAAEiB,EAAE,CAACjB,EAAE,IAAIiB,EAAE,CAACE,GAAG,IAAID,GAAG;UAAE;UAC5BE,WAAW,EAAEH,EAAE,CAACI,GAAG,IAAI,EAAE;UACzBC,YAAY,EAAEL,EAAE,CAACM,KAAK,IAAI,EAAE;UAC5BC,OAAO,EAAEP,EAAE,CAACQ,QAAQ,IAAI,EAAE;UAC1BC,eAAe,EAAET,EAAE,CAACS,eAAe;UACnCC,eAAe,EAAEV,EAAE,CAACU,eAAe;UACnCC,YAAY,EAAEX,EAAE,CAACW,YAAY;UAC7BC,WAAW,EAAEZ,EAAE,CAACY,WAAW;UAC3BC,OAAO,EAAEb,EAAE,CAACa,OAAO,IAAI;QACzB,CAAC,CAAC,CAAC;QACHlB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,MAAM,CAAC,CAAC,CAAC;QAC1CZ,YAAY,CAACY,MAAM,CAAC;MACtB,CAAC,CAAC,CACDgB,KAAK,CAACC,GAAG,IAAIpB,OAAO,CAACqB,KAAK,CAAC,iCAAiC,EAAED,GAAG,CAAC,CAAC;IACxE;EACF,CAAC;EAEDzD,SAAS,CAAC,MAAM;IACdiC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;EAEd,MAAMiC,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAErC,CAAC,CAAC,IAAI,CAAC;IAAEsC,KAAK,EAAE;EAAG,CAAC,EAC/C;IAAEF,KAAK,EAAE,aAAa;IAAEC,UAAU,EAAErC,CAAC,CAAC,aAAa,CAAC;IAAEsC,KAAK,EAAE;EAAI,CAAC,EAClE;IAAEF,KAAK,EAAE,cAAc;IAAEC,UAAU,EAAErC,CAAC,CAAC,cAAc,CAAC;IAAEsC,KAAK,EAAE;EAAI,CAAC,EACpE;IACEF,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAErC,CAAC,CAAC,cAAc,CAAC;IAC7BsC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjB9C,OAAA,CAACP,MAAM;MACLsD,OAAO,EAAC,UAAU;MAClBC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAEA,CAAA,KAAM;QACbrC,mBAAmB,CAACkC,MAAM,CAACI,GAAG,CAAC;QAC/BpC,qBAAqB,CAAC,IAAI,CAAC;MAC7B,CAAE;MAAAqC,QAAA,EAED7C,CAAC,CAAC,UAAU;IAAC;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEZ,CAAC,EACD;IACEb,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAErC,CAAC,CAAC,eAAe,CAAC;IAC9BsC,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,IAAK;MACtB,MAAMT,OAAO,GAAGS,MAAM,CAACI,GAAG,CAACb,OAAO,IAAI,EAAE;MACxC,MAAMmB,cAAc,GAAGnB,OAAO,CAC3Bd,GAAG,CAACkC,EAAE,IAAIC,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,CAAC,CAC5BC,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC;MACrD,IAAIL,cAAc,CAACO,MAAM,KAAK,CAAC,EAAE,OAAOzD,CAAC,CAAC,UAAU,CAAC;MACrD,MAAM0D,GAAG,GAAGR,cAAc,CAACS,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGX,cAAc,CAACO,MAAM;MAC7E,MAAMK,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC;MAC/B,MAAMO,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACjD,MAAMC,UAAU,GAAG,CAAClE,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/G,oBACEN,OAAA;QAAMyE,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAC7DnD,OAAA;UAAMyE,KAAK,EAAE;YAAEI,QAAQ,EAAE;UAAG,CAAE;UAAA1B,QAAA,EAAEoB,UAAU,CAACH,OAAO,GAAG,CAAC;QAAC;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/DvD,OAAA;UAAMyE,KAAK,EAAE;YAAEK,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAE,CAAE;UAAA5B,QAAA,EAAEqB,UAAU,CAACJ,OAAO,GAAG,CAAC;QAAC;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpFvD,OAAA;UAAMyE,KAAK,EAAE;YAAEO,KAAK,EAAE,MAAM;YAAED,UAAU,EAAE;UAAE,CAAE;UAAA5B,QAAA,GAAC,GAAC,EAACa,GAAG,CAACiB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEX;EACF,CAAC,CACF;EAED,oBACEvD,OAAA,CAACf,GAAG;IAACiG,CAAC,EAAE,CAAE;IAAA/B,QAAA,gBACRnD,OAAA,CAACd,UAAU;MAAC6D,OAAO,EAAC,IAAI;MAACoC,EAAE,EAAE,CAAE;MAAAhC,QAAA,gBAC7BnD,OAAA,CAACF,YAAY;QAAC+E,QAAQ,EAAC,OAAO;QAACO,EAAE,EAAE;UAAEC,aAAa,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxEjD,CAAC,CAAC,cAAc,CAAC;IAAA;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEbvD,OAAA,CAACb,KAAK;MAACiG,EAAE,EAAE;QAAEF,CAAC,EAAE;MAAE,CAAE;MAAA/B,QAAA,eAClBnD,OAAA,CAACf,GAAG;QAACmG,EAAE,EAAE;UAAEG,MAAM,EAAE,GAAG;UAAE3C,KAAK,EAAE;QAAO,CAAE;QAAAO,QAAA,eACtCnD,OAAA,CAACJ,QAAQ;UACP4F,IAAI,EAAE/E,SAAU;UAChBgF,OAAO,EAAEhD,eAAgB;UACzBiD,QAAQ,EAAE,CAAE;UACZC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;UAChCC,uBAAuB;QAAA;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRvD,OAAA,CAACX,MAAM;MAACwG,IAAI,EAAEhF,kBAAmB;MAACiF,OAAO,EAAEA,CAAA,KAAMhF,qBAAqB,CAAC,KAAK,CAAE;MAACiF,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA7C,QAAA,gBACpGnD,OAAA,CAACV,WAAW;QAAA6D,QAAA,eACVnD,OAAA,CAACR,KAAK;UAACyG,SAAS,EAAC,KAAK;UAACtB,UAAU,EAAC,QAAQ;UAACuB,OAAO,EAAE,CAAE;UAAA/C,QAAA,gBACpDnD,OAAA,CAACF,YAAY;YAACkF,KAAK,EAAC;UAAS;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCvD,OAAA,CAACf,GAAG;YAAAkE,QAAA,GACD7C,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC,eAAAN,OAAA;cAAAmD,QAAA,EAAIxC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgB;YAAW;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1DvD,OAAA,CAACd,UAAU;cAAC6D,OAAO,EAAC,OAAO;cAACiC,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EAC/CxC,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkB;YAAY;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACdvD,OAAA,CAACT,aAAa;QAAC4G,QAAQ;QAACf,EAAE,EAAE;UAAEgB,OAAO,EAAE,SAAS;UAAEC,SAAS,EAAE;QAAI,CAAE;QAAAlD,QAAA,eACjEnD,OAAA,CAACR,KAAK;UAAC0G,OAAO,EAAE,CAAE;UAAA/C,QAAA,gBAChBnD,OAAA,CAACf,GAAG;YAAAkE,QAAA,eACFnD,OAAA,CAACd,UAAU;cAAC6D,OAAO,EAAC,WAAW;cAACiC,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,GACnD7C,CAAC,CAAC,MAAM,CAAC,EAAC,IAAE,EAAC,CAAAK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2F,SAAS,KAAI,IAAIC,IAAI,CAAC5F,gBAAgB,CAAC2F,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC;YAAA;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EACL,CAAA5C,gBAAgB,aAAhBA,gBAAgB,wBAAAN,qBAAA,GAAhBM,gBAAgB,CAAE0B,OAAO,cAAAhC,qBAAA,uBAAzBA,qBAAA,CAA2B0D,MAAM,IAAG,CAAC,IAAI,CAAC,MAAM;YAC/C;YACA,MAAM0C,QAAQ,GAAG,CACf;cACEC,KAAK,EAAEpG,CAAC,CAAC,gBAAgB,CAAC;cAC1BqG,QAAQ,EAAE,CACR,oBAAoB,EACpB,cAAc,EACd,WAAW,EACX,OAAO,EACP,oBAAoB,EACpB,oBAAoB,EACpB,6BAA6B;YAEjC,CAAC,EACD;cACED,KAAK,EAAEpG,CAAC,CAAC,gBAAgB,CAAC;cAC1BqG,QAAQ,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAE,+BAA+B;YACxH,CAAC,EACD;cACED,KAAK,EAAEpG,CAAC,CAAC,aAAa,CAAC;cACvBqG,QAAQ,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,4BAA4B;YACjH,CAAC,EACD;cACED,KAAK,EAAEpG,CAAC,CAAC,oBAAoB,CAAC;cAC9BqG,QAAQ,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB;YAC5D,CAAC,CACF;YACD;YACA,SAASC,SAASA,CAACC,GAAG,EAAE;cACtB,OAAOA,GAAG,CACPC,WAAW,CAAC,CAAC,CACbF,SAAS,CAAC,KAAK,CAAC,CAACG,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;cAAA,CACjDA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;YAChC;YACA,MAAMC,cAAc,GAAGrG,gBAAgB,IAAIA,gBAAgB,CAAC0B,OAAO,GAAGoE,QAAQ,CAAClF,GAAG,CAAC0F,OAAO,KAAK;cAC7F,GAAGA,OAAO;cACV5E,OAAO,EAAE1B,gBAAgB,CAAC0B,OAAO,CAACuB,MAAM,CAACH,EAAE,IACzCwD,OAAO,CAACN,QAAQ,CAACO,IAAI,CAACC,OAAO,IAC3BP,SAAS,CAACnD,EAAE,CAAC2D,QAAQ,CAAC,CAACC,QAAQ,CAACT,SAAS,CAACO,OAAO,CAAC,CACpD,CACF;YACF,CAAC,CAAC,CAAC,GAAG,EAAE;YACR;YACA,MAAMG,YAAY,GAAG3G,gBAAgB,IAAIA,gBAAgB,CAAC0B,OAAO,GAAG1B,gBAAgB,CAAC0B,OAAO,CAACuB,MAAM,CAACH,EAAE,IACpG,CAACgD,QAAQ,CAACS,IAAI,CAACD,OAAO,IAAIA,OAAO,CAACN,QAAQ,CAACO,IAAI,CAACC,OAAO,IAAIP,SAAS,CAACnD,EAAE,CAAC2D,QAAQ,CAAC,CAACC,QAAQ,CAACT,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC,CACjH,CAAC,GAAG,EAAE;YACN;YACA,MAAM5C,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACjD,MAAMC,UAAU,GAAG,CAAClE,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;YAC/G,oBACEN,OAAA,CAAAE,SAAA;cAAAiD,QAAA,GACG6D,cAAc,CAACzF,GAAG,CAAC,CAAC0F,OAAO,EAAExF,GAAG,KAC/BwF,OAAO,CAAC5E,OAAO,CAAC0B,MAAM,GAAG,CAAC,iBACxB/D,OAAA,CAACf,GAAG;gBAAWkG,EAAE,EAAE,CAAE;gBAAAhC,QAAA,gBACnBnD,OAAA,CAACZ,OAAO;kBAACgG,EAAE,EAAE;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAhC,QAAA,EAAE8D,OAAO,CAACP;gBAAK;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACjDvD,OAAA,CAACR,KAAK;kBAAC0G,OAAO,EAAE,CAAE;kBAAA/C,QAAA,EACf8D,OAAO,CAAC5E,OAAO,CAACd,GAAG,CAAC,CAACkC,EAAE,EAAE8D,IAAI,KAAK;oBACjC,IAAIC,SAAS,GAAG,CAAC1D,KAAK,CAACJ,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,CAAC,IAAID,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,IAAI,CAAC,IAAID,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,IAAI,CAAC;oBAC7F,IAAI8D,KAAK,GAAGD,SAAS,GAAG9D,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,GAAG,IAAI;oBAChD,oBACE3D,OAAA,CAACb,KAAK;sBAAYuI,SAAS,EAAE,CAAE;sBAACtC,EAAE,EAAE;wBAAEF,CAAC,EAAE,CAAC;wBAAEkB,OAAO,EAAE;sBAAO,CAAE;sBAAAjD,QAAA,gBAC5DnD,OAAA,CAACd,UAAU;wBAAC4F,UAAU,EAAC,MAAM;wBAAC6C,YAAY;wBAAAxE,QAAA,EACvCM,EAAE,CAAC2D;sBAAQ;wBAAAhE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,EACZiE,SAAS,gBACRxH,OAAA,CAACf,GAAG;wBAACyF,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAzB,QAAA,gBAC7CnD,OAAA,CAACd,UAAU;0BAAC2F,QAAQ,EAAE,EAAG;0BAAA1B,QAAA,EAAEoB,UAAU,CAACkD,KAAK,GAAG,CAAC;wBAAC;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAC9DvD,OAAA,CAACd,UAAU;0BAAC4F,UAAU,EAAC,MAAM;0BAAA3B,QAAA,EAAEqB,UAAU,CAACiD,KAAK,GAAG,CAAC;wBAAC;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAClEvD,OAAA,CAACd,UAAU;0BAAC8F,KAAK,EAAC,gBAAgB;0BAAA7B,QAAA,GAAC,GAAC,EAACsE,KAAK,EAAC,GAAC;wBAAA;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,gBAENvD,OAAA,CAACd,UAAU;wBAACuF,KAAK,EAAE;0BAAEmD,UAAU,EAAE;wBAAW,CAAE;wBAAAzE,QAAA,EAAEM,EAAE,CAACE,MAAM,IAAIrD,CAAC,CAAC,UAAU;sBAAC;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CACxF;oBAAA,GAZSgE,IAAI;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAaT,CAAC;kBAEZ,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA,GAvBA9B,GAAG;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBR,CAET,CAAC,EACA+D,YAAY,CAACvD,MAAM,GAAG,CAAC,iBACtB/D,OAAA,CAACf,GAAG;gBAACkG,EAAE,EAAE,CAAE;gBAAAhC,QAAA,gBACTnD,OAAA,CAACZ,OAAO;kBAACgG,EAAE,EAAE;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAhC,QAAA,EAAE7C,CAAC,CAAC,cAAc;gBAAC;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACrDvD,OAAA,CAACR,KAAK;kBAAC0G,OAAO,EAAE,CAAE;kBAAA/C,QAAA,EACfmE,YAAY,CAAC/F,GAAG,CAAC,CAACkC,EAAE,EAAE8D,IAAI,KAAK;oBAC9B,IAAIC,SAAS,GAAG,CAAC1D,KAAK,CAACJ,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,CAAC,IAAID,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,IAAI,CAAC,IAAID,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,IAAI,CAAC;oBAC7F,IAAI8D,KAAK,GAAGD,SAAS,GAAG9D,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,GAAG,IAAI;oBAChD,oBACE3D,OAAA,CAACb,KAAK;sBAAYuI,SAAS,EAAE,CAAE;sBAACtC,EAAE,EAAE;wBAAEF,CAAC,EAAE,CAAC;wBAAEkB,OAAO,EAAE;sBAAO,CAAE;sBAAAjD,QAAA,gBAC5DnD,OAAA,CAACd,UAAU;wBAAC4F,UAAU,EAAC,MAAM;wBAAC6C,YAAY;wBAAAxE,QAAA,EACvCM,EAAE,CAAC2D;sBAAQ;wBAAAhE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,EACZiE,SAAS,gBACRxH,OAAA,CAACf,GAAG;wBAACyF,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,CAAE;wBAAAzB,QAAA,gBAC7CnD,OAAA,CAACd,UAAU;0BAAC2F,QAAQ,EAAE,EAAG;0BAAA1B,QAAA,EAAEoB,UAAU,CAACkD,KAAK,GAAG,CAAC;wBAAC;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAC9DvD,OAAA,CAACd,UAAU;0BAAC4F,UAAU,EAAC,MAAM;0BAAA3B,QAAA,EAAEqB,UAAU,CAACiD,KAAK,GAAG,CAAC;wBAAC;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAa,CAAC,eAClEvD,OAAA,CAACd,UAAU;0BAAC8F,KAAK,EAAC,gBAAgB;0BAAA7B,QAAA,GAAC,GAAC,EAACsE,KAAK,EAAC,GAAC;wBAAA;0BAAArE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,gBAENvD,OAAA,CAACd,UAAU;wBAACuF,KAAK,EAAE;0BAAEmD,UAAU,EAAE;wBAAW,CAAE;wBAAAzE,QAAA,EAAEM,EAAE,CAACE,MAAM,IAAIrD,CAAC,CAAC,UAAU;sBAAC;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CACxF;oBAAA,GAZSgE,IAAI;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAaT,CAAC;kBAEZ,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACN;YAAA,eACD,CAAC;UAEP,CAAC,EAAE,CAAC,eAEJvD,OAAA,CAACZ,OAAO;YAAA+D,QAAA,EAAE7C,CAAC,CAAC,eAAe;UAAC;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,EACtC,CAAC,MAAM;YACN;YACA,MAAMC,cAAc,GAAG7C,gBAAgB,IAAIA,gBAAgB,CAAC0B,OAAO,GAAG1B,gBAAgB,CAAC0B,OAAO,CAC3Fd,GAAG,CAACkC,EAAE,IAAIC,MAAM,CAACD,EAAE,CAACE,MAAM,CAAC,CAAC,CAC5BC,MAAM,CAACC,GAAG,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE;YAC1D,IAAIL,cAAc,CAACO,MAAM,KAAK,CAAC,EAAE;cAC/B,oBAAO/D,OAAA,CAACd,UAAU;gBAAC8F,KAAK,EAAC,gBAAgB;gBAAA7B,QAAA,EAAE7C,CAAC,CAAC,UAAU;cAAC;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YACxE;YACA,MAAMS,GAAG,GAAGR,cAAc,CAACS,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGX,cAAc,CAACO,MAAM;YAC7E,MAAMK,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACN,GAAG,CAAC;YAC/B,MAAMO,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;YACjD,MAAMC,UAAU,GAAG,CAAClE,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;YAC/G,oBACEN,OAAA,CAACf,GAAG;cAACyF,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAACiD,EAAE,EAAE,CAAE;cAAC1C,EAAE,EAAE,CAAE;cAAAhC,QAAA,gBAC3DnD,OAAA,CAACd,UAAU;gBAAC2F,QAAQ,EAAE,EAAG;gBAAA1B,QAAA,EAAEoB,UAAU,CAACH,OAAO,GAAG,CAAC;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChEvD,OAAA,CAACd,UAAU;gBAAC4F,UAAU,EAAC,MAAM;gBAAA3B,QAAA,EAAEqB,UAAU,CAACJ,OAAO,GAAG,CAAC;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACpEvD,OAAA,CAACd,UAAU;gBAAC8F,KAAK,EAAC,gBAAgB;gBAAA7B,QAAA,GAAC,GAAC,EAACa,GAAG,CAACiB,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAEV,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACnD,EAAA,CAnQID,YAAY;EAAA,QACFT,cAAc,EACHV,SAAS;AAAA;AAAA8I,EAAA,GAF9B3H,YAAY;AAqQlB,eAAeA,YAAY;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}