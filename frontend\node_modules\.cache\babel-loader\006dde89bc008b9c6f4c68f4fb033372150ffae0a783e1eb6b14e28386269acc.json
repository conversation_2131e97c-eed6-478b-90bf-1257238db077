{"ast": null, "code": "import { isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nexport default function getBoundingClientRect(element, includeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n  if (isHTMLElement(element) && includeScale) {\n    var offsetHeight = element.offsetHeight;\n    var offsetWidth = element.offsetWidth; // Do not attempt to divide by 0, otherwise we get `Infinity` as scale\n    // Fallback to 1 in case both values are `0`\n\n    if (offsetWidth > 0) {\n      scaleX = round(rect.width) / offsetWidth || 1;\n    }\n    if (offsetHeight > 0) {\n      scaleY = round(rect.height) / offsetHeight || 1;\n    }\n  }\n  return {\n    width: rect.width / scaleX,\n    height: rect.height / scaleY,\n    top: rect.top / scaleY,\n    right: rect.right / scaleX,\n    bottom: rect.bottom / scaleY,\n    left: rect.left / scaleX,\n    x: rect.left / scaleX,\n    y: rect.top / scaleY\n  };\n}", "map": {"version": 3, "names": ["isHTMLElement", "round", "getBoundingClientRect", "element", "includeScale", "rect", "scaleX", "scaleY", "offsetHeight", "offsetWidth", "width", "height", "top", "right", "bottom", "left", "x", "y"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js"], "sourcesContent": ["import { isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nexport default function getBoundingClientRect(element, includeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (isHTMLElement(element) && includeScale) {\n    var offsetHeight = element.offsetHeight;\n    var offsetWidth = element.offsetWidth; // Do not attempt to divide by 0, otherwise we get `Infinity` as scale\n    // Fallback to 1 in case both values are `0`\n\n    if (offsetWidth > 0) {\n      scaleX = round(rect.width) / offsetWidth || 1;\n    }\n\n    if (offsetHeight > 0) {\n      scaleY = round(rect.height) / offsetHeight || 1;\n    }\n  }\n\n  return {\n    width: rect.width / scaleX,\n    height: rect.height / scaleY,\n    top: rect.top / scaleY,\n    right: rect.right / scaleX,\n    bottom: rect.bottom / scaleY,\n    left: rect.left / scaleX,\n    x: rect.left / scaleX,\n    y: rect.top / scaleY\n  };\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,KAAK,QAAQ,kBAAkB;AACxC,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,YAAY,EAAE;EACnE,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EAEA,IAAIC,IAAI,GAAGF,OAAO,CAACD,qBAAqB,CAAC,CAAC;EAC1C,IAAII,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EAEd,IAAIP,aAAa,CAACG,OAAO,CAAC,IAAIC,YAAY,EAAE;IAC1C,IAAII,YAAY,GAAGL,OAAO,CAACK,YAAY;IACvC,IAAIC,WAAW,GAAGN,OAAO,CAACM,WAAW,CAAC,CAAC;IACvC;;IAEA,IAAIA,WAAW,GAAG,CAAC,EAAE;MACnBH,MAAM,GAAGL,KAAK,CAACI,IAAI,CAACK,KAAK,CAAC,GAAGD,WAAW,IAAI,CAAC;IAC/C;IAEA,IAAID,YAAY,GAAG,CAAC,EAAE;MACpBD,MAAM,GAAGN,KAAK,CAACI,IAAI,CAACM,MAAM,CAAC,GAAGH,YAAY,IAAI,CAAC;IACjD;EACF;EAEA,OAAO;IACLE,KAAK,EAAEL,IAAI,CAACK,KAAK,GAAGJ,MAAM;IAC1BK,MAAM,EAAEN,IAAI,CAACM,MAAM,GAAGJ,MAAM;IAC5BK,GAAG,EAAEP,IAAI,CAACO,GAAG,GAAGL,MAAM;IACtBM,KAAK,EAAER,IAAI,CAACQ,KAAK,GAAGP,MAAM;IAC1BQ,MAAM,EAAET,IAAI,CAACS,MAAM,GAAGP,MAAM;IAC5BQ,IAAI,EAAEV,IAAI,CAACU,IAAI,GAAGT,MAAM;IACxBU,CAAC,EAAEX,IAAI,CAACU,IAAI,GAAGT,MAAM;IACrBW,CAAC,EAAEZ,IAAI,CAACO,GAAG,GAAGL;EAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}