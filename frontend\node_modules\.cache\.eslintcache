[{"C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js": "4", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js": "5", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js": "6", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js": "7", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js": "8", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js": "9", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js": "10", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js": "11", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js": "12", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js": "13", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js": "14", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js": "15", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js": "16", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js": "17", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js": "18", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js": "19", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js": "20", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js": "21", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js": "22", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js": "23", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js": "24", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js": "25", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js": "26", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js": "27", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js": "28", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js": "29", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js": "30", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js": "31", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js": "32", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js": "33", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js": "34", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js": "35", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js": "36", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js": "37", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js": "38", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js": "39", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js": "40", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js": "41", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js": "42", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js": "43", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js": "44", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js": "45", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js": "46", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js": "47", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js": "48", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js": "49", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js": "50", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js": "51", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js": "52", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js": "53", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js": "54", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js": "55", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js": "56", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js": "57", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js": "58", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js": "59", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js": "60", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js": "61", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js": "62", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js": "63", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js": "64", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js": "65", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js": "66", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js": "67", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js": "68", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js": "69", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js": "70", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js": "71", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js": "72", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js": "73", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js": "74", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\TestFeedback.js": "75", "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\TestAverageRating.js": "76"}, {"size": 610, "mtime": 1747831852695, "results": "77", "hashOfConfig": "78"}, {"size": 11723, "mtime": 1754087055072, "results": "79", "hashOfConfig": "78"}, {"size": 375, "mtime": 1745438071501, "results": "80", "hashOfConfig": "78"}, {"size": 656, "mtime": 1752859285013, "results": "81", "hashOfConfig": "78"}, {"size": 5622, "mtime": 1751990678109, "results": "82", "hashOfConfig": "78"}, {"size": 3723, "mtime": 1750871433201, "results": "83", "hashOfConfig": "78"}, {"size": 455, "mtime": 1750865324399, "results": "84", "hashOfConfig": "78"}, {"size": 6151, "mtime": 1750873153170, "results": "85", "hashOfConfig": "78"}, {"size": 1687, "mtime": 1753356431070, "results": "86", "hashOfConfig": "78"}, {"size": 694, "mtime": 1747594139572, "results": "87", "hashOfConfig": "78"}, {"size": 25936, "mtime": 1752342963093, "results": "88", "hashOfConfig": "78"}, {"size": 420, "mtime": 1747844523996, "results": "89", "hashOfConfig": "78"}, {"size": 15067, "mtime": 1753136360471, "results": "90", "hashOfConfig": "78"}, {"size": 2240, "mtime": 1750873583618, "results": "91", "hashOfConfig": "78"}, {"size": 491, "mtime": 1747844524003, "results": "92", "hashOfConfig": "78"}, {"size": 850, "mtime": 1750872193880, "results": "93", "hashOfConfig": "78"}, {"size": 4575, "mtime": 1750883025232, "results": "94", "hashOfConfig": "78"}, {"size": 3424, "mtime": 1753700299624, "results": "95", "hashOfConfig": "78"}, {"size": 422, "mtime": 1747844523996, "results": "96", "hashOfConfig": "78"}, {"size": 478, "mtime": 1751986578114, "results": "97", "hashOfConfig": "78"}, {"size": 55698, "mtime": 1751985929377, "results": "98", "hashOfConfig": "78"}, {"size": 1026, "mtime": 1750873436634, "results": "99", "hashOfConfig": "78"}, {"size": 7665, "mtime": 1753034959934, "results": "100", "hashOfConfig": "78"}, {"size": 15134, "mtime": 1752581112327, "results": "101", "hashOfConfig": "78"}, {"size": 4159, "mtime": 1750777638880, "results": "102", "hashOfConfig": "78"}, {"size": 19519, "mtime": 1753294211583, "results": "103", "hashOfConfig": "78"}, {"size": 153, "mtime": 1745582315206, "results": "104", "hashOfConfig": "78"}, {"size": 284, "mtime": 1750858318977, "results": "105", "hashOfConfig": "78"}, {"size": 2909, "mtime": 1750887085865, "results": "106", "hashOfConfig": "78"}, {"size": 11933, "mtime": 1752069953530, "results": "107", "hashOfConfig": "78"}, {"size": 1693, "mtime": 1750850985128, "results": "108", "hashOfConfig": "78"}, {"size": 25111, "mtime": 1751985930792, "results": "109", "hashOfConfig": "78"}, {"size": 11596, "mtime": 1750887959421, "results": "110", "hashOfConfig": "78"}, {"size": 6477, "mtime": 1750872851488, "results": "111", "hashOfConfig": "78"}, {"size": 12740, "mtime": 1749741251393, "results": "112", "hashOfConfig": "78"}, {"size": 5969, "mtime": 1753704624495, "results": "113", "hashOfConfig": "78"}, {"size": 22810, "mtime": 1753375441494, "results": "114", "hashOfConfig": "78"}, {"size": 8204, "mtime": 1750890990379, "results": "115", "hashOfConfig": "78"}, {"size": 5733, "mtime": 1752069593152, "results": "116", "hashOfConfig": "78"}, {"size": 3301, "mtime": 1752068170287, "results": "117", "hashOfConfig": "78"}, {"size": 11954, "mtime": 1752341829288, "results": "118", "hashOfConfig": "78"}, {"size": 22778, "mtime": 1753356431075, "results": "119", "hashOfConfig": "78"}, {"size": 43555, "mtime": 1754153077590, "results": "120", "hashOfConfig": "78"}, {"size": 22730, "mtime": 1753356431232, "results": "121", "hashOfConfig": "78"}, {"size": 10045, "mtime": 1753136361210, "results": "122", "hashOfConfig": "78"}, {"size": 9460, "mtime": 1753136361257, "results": "123", "hashOfConfig": "78"}, {"size": 14064, "mtime": 1753356431325, "results": "124", "hashOfConfig": "78"}, {"size": 665, "mtime": 1753136360410, "results": "125", "hashOfConfig": "78"}, {"size": 56, "mtime": 1750504542636, "results": "126", "hashOfConfig": "78"}, {"size": 751, "mtime": 1748635026074, "results": "127", "hashOfConfig": "78"}, {"size": 2964, "mtime": 1752068241528, "results": "128", "hashOfConfig": "78"}, {"size": 620, "mtime": 1747594139559, "results": "129", "hashOfConfig": "78"}, {"size": 1393, "mtime": 1750866339097, "results": "130", "hashOfConfig": "78"}, {"size": 3083, "mtime": 1751985930466, "results": "131", "hashOfConfig": "78"}, {"size": 4753, "mtime": 1751985930121, "results": "132", "hashOfConfig": "78"}, {"size": 10232, "mtime": 1753035018285, "results": "133", "hashOfConfig": "78"}, {"size": 4696, "mtime": 1751985929811, "results": "134", "hashOfConfig": "78"}, {"size": 25558, "mtime": 1753904297260, "results": "135", "hashOfConfig": "78"}, {"size": 7155, "mtime": 1751298764731, "results": "136", "hashOfConfig": "78"}, {"size": 5299, "mtime": 1750884972392, "results": "137", "hashOfConfig": "78"}, {"size": 897, "mtime": 1747061904678, "results": "138", "hashOfConfig": "78"}, {"size": 23129, "mtime": 1753356169008, "results": "139", "hashOfConfig": "78"}, {"size": 5376, "mtime": 1748517649309, "results": "140", "hashOfConfig": "78"}, {"size": 5485, "mtime": 1753034961627, "results": "141", "hashOfConfig": "78"}, {"size": 890, "mtime": 1749741250195, "results": "142", "hashOfConfig": "78"}, {"size": 4625, "mtime": 1753034977418, "results": "143", "hashOfConfig": "78"}, {"size": 2175, "mtime": 1753370053964, "results": "144", "hashOfConfig": "78"}, {"size": 12989, "mtime": 1753365132448, "results": "145", "hashOfConfig": "78"}, {"size": 24795, "mtime": 1754154172500, "results": "146", "hashOfConfig": "78"}, {"size": 23365, "mtime": 1753369624191, "results": "147", "hashOfConfig": "78"}, {"size": 10857, "mtime": 1753354385496, "results": "148", "hashOfConfig": "78"}, {"size": 56267, "mtime": 1754152978143, "results": "149", "hashOfConfig": "78"}, {"size": 1223, "mtime": 1751656822978, "results": "150", "hashOfConfig": "78"}, {"size": 2878, "mtime": 1751659811949, "results": "151", "hashOfConfig": "78"}, {"size": 1667, "mtime": 1754071617823, "results": "152", "hashOfConfig": "78"}, {"size": 6475, "mtime": 1754087187151, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16w30kw", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\App.js", ["382", "383"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\i18n\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ForgetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\NotFoundPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetPasswordPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ResetSuccessPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\FeedbackPage.js", ["384", "385", "386"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\CoursesPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProfilePage.js", ["387"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentLandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ProgramsPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ModulePage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\StudentProgramPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SeanceFormateurPage.js", ["388"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\ContenusPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\WhiteboardPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\EditProfilePage.js", ["389"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\BuildProgramOverviewPage.js", ["390"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\VerifyAccountPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\SessionDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Main.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\apps\\Auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\UsersPages.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddModuleView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddUserView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\UserList.js", ["391", "392", "393"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddQuizForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\PlayQuizPage.js", ["394", "395"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditProgramView.js", ["396"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SeanceFormateurList.js", ["397"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AnimerSeanceView.js", ["398", "399", "400"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\BuildProgramView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFormateurView.js", ["401"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ModuleList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EditQuizForm.js", ["402", "403"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AdminDashboard.js", ["404", "405"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionFeedbackList.js", ["406", "407", "408", "409", "410", "411", "412"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CreateurDashboard.js", ["413", "414"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtablissementDashboard.js", ["415"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\EtudiantDashboard.js", ["416"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\FormateurDashboard.js", ["417"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\JitsiRoom.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\CourseList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ToastSuccess.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddCourseView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ProgramList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\ContenusList.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Whiteboard.js", ["418", "419"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddContenusView.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\SessionList.js", ["420", "421", "422", "423", "424", "425", "426"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\LanguageSelectorWithFlags.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionView.js", ["427"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScrollToTopButton.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Session2ChatPopup.js", ["428", "429", "430"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\toastError.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\feedbackService.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\ScoreReveal.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\constants\\sideBarData.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\FeedbackFormateur.js", ["431"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\seancefeedbacklist.js", ["432", "433", "434", "435"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSeanceFeedback.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\Chatbot.js", ["436"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\pages\\users\\views\\AddSessionFeedback.js", ["437", "438", "439"], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\components\\Chatbot\\QuickSuggestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\services\\languageService.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\TestFeedback.js", [], [], "C:\\Users\\<USER>\\Desktop\\mka-lms-2025\\frontend\\src\\TestAverageRating.js", [], [], {"ruleId": "440", "severity": 1, "message": "441", "line": 4, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 4, "endColumn": 24}, {"ruleId": "440", "severity": 1, "message": "444", "line": 56, "column": 8, "nodeType": "442", "messageId": "443", "endLine": 56, "endColumn": 21}, {"ruleId": "440", "severity": 1, "message": "445", "line": 19, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 19, "endColumn": 12}, {"ruleId": "440", "severity": 1, "message": "446", "line": 30, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 30, "endColumn": 10}, {"ruleId": "447", "severity": 1, "message": "448", "line": 202, "column": 5, "nodeType": "449", "messageId": "450", "endLine": 224, "endColumn": 6}, {"ruleId": "451", "severity": 1, "message": "452", "line": 170, "column": 6, "nodeType": "453", "endLine": 170, "endColumn": 20, "suggestions": "454"}, {"ruleId": "451", "severity": 1, "message": "455", "line": 42, "column": 6, "nodeType": "453", "endLine": 42, "endColumn": 17, "suggestions": "456"}, {"ruleId": "440", "severity": 1, "message": "457", "line": 171, "column": 19, "nodeType": "442", "messageId": "443", "endLine": 171, "endColumn": 23}, {"ruleId": "451", "severity": 1, "message": "458", "line": 69, "column": 6, "nodeType": "453", "endLine": 69, "endColumn": 17, "suggestions": "459"}, {"ruleId": "440", "severity": 1, "message": "460", "line": 37, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 37, "endColumn": 10}, {"ruleId": "451", "severity": 1, "message": "461", "line": 83, "column": 6, "nodeType": "453", "endLine": 83, "endColumn": 8, "suggestions": "462"}, {"ruleId": "440", "severity": 1, "message": "463", "line": 241, "column": 9, "nodeType": "442", "messageId": "443", "endLine": 241, "endColumn": 23}, {"ruleId": "451", "severity": 1, "message": "464", "line": 56, "column": 6, "nodeType": "453", "endLine": 56, "endColumn": 23, "suggestions": "465"}, {"ruleId": "440", "severity": 1, "message": "466", "line": 68, "column": 9, "nodeType": "442", "messageId": "443", "endLine": 68, "endColumn": 14}, {"ruleId": "451", "severity": 1, "message": "467", "line": 77, "column": 6, "nodeType": "453", "endLine": 77, "endColumn": 17, "suggestions": "468"}, {"ruleId": "440", "severity": 1, "message": "469", "line": 7, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 7, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "470", "line": 47, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 47, "endColumn": 17}, {"ruleId": "440", "severity": 1, "message": "471", "line": 56, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 56, "endColumn": 29}, {"ruleId": "440", "severity": 1, "message": "472", "line": 56, "column": 31, "nodeType": "442", "messageId": "443", "endLine": 56, "endColumn": 53}, {"ruleId": "440", "severity": 1, "message": "473", "line": 18, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 18, "endColumn": 13}, {"ruleId": "440", "severity": 1, "message": "446", "line": 14, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 14, "endColumn": 10}, {"ruleId": "440", "severity": 1, "message": "474", "line": 15, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 15, "endColumn": 10}, {"ruleId": "440", "severity": 1, "message": "475", "line": 22, "column": 8, "nodeType": "442", "messageId": "443", "endLine": 22, "endColumn": 21}, {"ruleId": "440", "severity": 1, "message": "476", "line": 23, "column": 8, "nodeType": "442", "messageId": "443", "endLine": 23, "endColumn": 19}, {"ruleId": "440", "severity": 1, "message": "477", "line": 1, "column": 17, "nodeType": "442", "messageId": "443", "endLine": 1, "endColumn": 26}, {"ruleId": "440", "severity": 1, "message": "446", "line": 14, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 14, "endColumn": 10}, {"ruleId": "451", "severity": 1, "message": "478", "line": 46, "column": 6, "nodeType": "453", "endLine": 46, "endColumn": 17, "suggestions": "479"}, {"ruleId": "480", "severity": 1, "message": "481", "line": 478, "column": 31, "nodeType": "482", "messageId": "483", "endLine": 478, "endColumn": 33}, {"ruleId": "480", "severity": 1, "message": "481", "line": 478, "column": 74, "nodeType": "482", "messageId": "483", "endLine": 478, "endColumn": 76}, {"ruleId": "480", "severity": 1, "message": "481", "line": 579, "column": 31, "nodeType": "482", "messageId": "483", "endLine": 579, "endColumn": 33}, {"ruleId": "480", "severity": 1, "message": "481", "line": 579, "column": 60, "nodeType": "482", "messageId": "483", "endLine": 579, "endColumn": 62}, {"ruleId": "440", "severity": 1, "message": "484", "line": 10, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 10, "endColumn": 9}, {"ruleId": "440", "severity": 1, "message": "485", "line": 75, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 75, "endColumn": 15}, {"ruleId": "440", "severity": 1, "message": "486", "line": 31, "column": 20, "nodeType": "442", "messageId": "443", "endLine": 31, "endColumn": 31}, {"ruleId": "440", "severity": 1, "message": "487", "line": 18, "column": 8, "nodeType": "442", "messageId": "443", "endLine": 18, "endColumn": 22}, {"ruleId": "440", "severity": 1, "message": "488", "line": 16, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 16, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "489", "line": 12, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 12, "endColumn": 9}, {"ruleId": "440", "severity": 1, "message": "490", "line": 19, "column": 8, "nodeType": "442", "messageId": "443", "endLine": 19, "endColumn": 21}, {"ruleId": "440", "severity": 1, "message": "491", "line": 30, "column": 8, "nodeType": "442", "messageId": "443", "endLine": 30, "endColumn": 27}, {"ruleId": "440", "severity": 1, "message": "492", "line": 43, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 43, "endColumn": 33}, {"ruleId": "440", "severity": 1, "message": "493", "line": 43, "column": 35, "nodeType": "442", "messageId": "443", "endLine": 43, "endColumn": 61}, {"ruleId": "440", "severity": 1, "message": "494", "line": 44, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 44, "endColumn": 40}, {"ruleId": "440", "severity": 1, "message": "495", "line": 44, "column": 42, "nodeType": "442", "messageId": "443", "endLine": 44, "endColumn": 75}, {"ruleId": "451", "severity": 1, "message": "496", "line": 71, "column": 6, "nodeType": "453", "endLine": 71, "endColumn": 8, "suggestions": "497"}, {"ruleId": "440", "severity": 1, "message": "498", "line": 162, "column": 9, "nodeType": "442", "messageId": "443", "endLine": 162, "endColumn": 30}, {"ruleId": "451", "severity": 1, "message": "452", "line": 35, "column": 6, "nodeType": "453", "endLine": 35, "endColumn": 8, "suggestions": "499"}, {"ruleId": "440", "severity": 1, "message": "500", "line": 9, "column": 10, "nodeType": "442", "messageId": "443", "endLine": 9, "endColumn": 14}, {"ruleId": "440", "severity": 1, "message": "501", "line": 9, "column": 16, "nodeType": "442", "messageId": "443", "endLine": 9, "endColumn": 19}, {"ruleId": "440", "severity": 1, "message": "502", "line": 40, "column": 23, "nodeType": "442", "messageId": "443", "endLine": 40, "endColumn": 37}, {"ruleId": "451", "severity": 1, "message": "503", "line": 76, "column": 6, "nodeType": "453", "endLine": 76, "endColumn": 29, "suggestions": "504"}, {"ruleId": "440", "severity": 1, "message": "446", "line": 7, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 7, "endColumn": 10}, {"ruleId": "440", "severity": 1, "message": "469", "line": 11, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 11, "endColumn": 8}, {"ruleId": "451", "severity": 1, "message": "505", "line": 58, "column": 6, "nodeType": "453", "endLine": 58, "endColumn": 16, "suggestions": "506"}, {"ruleId": "440", "severity": 1, "message": "507", "line": 387, "column": 21, "nodeType": "442", "messageId": "443", "endLine": 387, "endColumn": 31}, {"ruleId": "451", "severity": 1, "message": "508", "line": 35, "column": 6, "nodeType": "453", "endLine": 35, "endColumn": 20, "suggestions": "509"}, {"ruleId": "440", "severity": 1, "message": "446", "line": 30, "column": 3, "nodeType": "442", "messageId": "443", "endLine": 30, "endColumn": 10}, {"ruleId": "440", "severity": 1, "message": "510", "line": 110, "column": 9, "nodeType": "442", "messageId": "443", "endLine": 110, "endColumn": 23}, {"ruleId": "447", "severity": 1, "message": "448", "line": 220, "column": 5, "nodeType": "449", "messageId": "450", "endLine": 247, "endColumn": 6}, "no-unused-vars", "'useTranslation' is defined but never used.", "Identifier", "unusedVar", "'SessionDetail' is defined but never used.", "'FormGroup' is defined but never used.", "'Divider' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", "ArrayExpression", ["511"], "React Hook useEffect has missing dependencies: 'fetchSeances' and 'fetchSessionDetails'. Either include them or remove the dependency array.", ["512"], "'gray' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 't'. Either include it or remove the dependency array.", ["513"], "'Refresh' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["514"], "'getAvatarColor' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["515"], "'total' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'contenusByCourse' and 'coursesByModule'. Either include them or remove the dependency array.", ["516"], "'Stack' is defined but never used.", "'prevTab' is assigned a value but never used.", "'showFeedbackSidebar' is assigned a value but never used.", "'setShowFeedbackSidebar' is assigned a value but never used.", "'Eye' is defined but never used.", "'Tooltip' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'ArchiveIcon' is defined but never used.", "'useEffect' is defined but never used.", "React Hook React.useEffect has a missing dependency: 'reloadFeedbacks'. Either include it or remove the dependency array.", ["517"], "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'Avatar' is defined but never used.", "'error' is assigned a value but never used.", "'setStudents' is assigned a value but never used.", "'TrendingUpIcon' is defined but never used.", "'Paper' is defined but never used.", "'Button' is defined but never used.", "'ColorLensIcon' is defined but never used.", "'SessionFeedbackList' is defined but never used.", "'openSessionFeedbackList' is assigned a value but never used.", "'setOpenSessionFeedbackList' is assigned a value but never used.", "'selectedSessionForFeedbackList' is assigned a value but never used.", "'setSelectedSessionForFeedbackList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["518"], "'handleDownloadPreview' is assigned a value but never used.", ["519"], "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'setShowTooltip' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStudents'. Either include it or remove the dependency array.", ["520"], "React Hook useEffect has a missing dependency: 'reloadFeedbacks'. Either include it or remove the dependency array.", ["521"], "'moodEmojis' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'messages.length' and 'welcomeMessages'. Either include them or remove the dependency array.", ["522"], "'getRatingLabel' is assigned a value but never used.", {"desc": "523", "fix": "524"}, {"desc": "525", "fix": "526"}, {"desc": "527", "fix": "528"}, {"desc": "529", "fix": "530"}, {"desc": "531", "fix": "532"}, {"desc": "533", "fix": "534"}, {"desc": "535", "fix": "536"}, {"desc": "537", "fix": "538"}, {"desc": "539", "fix": "540"}, {"desc": "541", "fix": "542"}, {"desc": "543", "fix": "544"}, {"desc": "545", "fix": "546"}, "Update the dependencies array to be: [id, navigate, t]", {"range": "547", "text": "548"}, "Update the dependencies array to be: [fetchSeances, fetchSessionDetails, sessionId]", {"range": "549", "text": "550"}, "Update the dependencies array to be: [programId, t]", {"range": "551", "text": "552"}, "Update the dependencies array to be: [fetchUsers]", {"range": "553", "text": "554"}, "Update the dependencies array to be: [timeLeft, score, handleSubmit]", {"range": "555", "text": "556"}, "Update the dependencies array to be: [contenusByCourse, coursesByModule, programId]", {"range": "557", "text": "558"}, "Update the dependencies array to be: [reloadFeedbacks, sessionId]", {"range": "559", "text": "560"}, "Update the dependencies array to be: [fetchSessions]", {"range": "561", "text": "562"}, "Update the dependencies array to be: [t]", {"range": "563", "text": "564"}, "Update the dependencies array to be: [formateurId, loadStudents, seanceId]", {"range": "565", "text": "566"}, "Update the dependencies array to be: [reloadFeedbacks, seanceId]", {"range": "567", "text": "568"}, "Update the dependencies array to be: [messages.length, userLanguage, welcomeMessages]", {"range": "569", "text": "570"}, [5898, 5912], "[id, navigate, t]", [1508, 1519], "[fetchSeances, fetchSessionDetails, sessionId]", [2192, 2203], "[programId, t]", [1913, 1915], "[fetchUsers]", [1411, 1428], "[timeLeft, score, handleSubmit]", [3070, 3081], "[contenusByCourse, coursesByModule, programId]", [1272, 1283], "[reloadFeedbacks, sessionId]", [2428, 2430], "[fetchSessions]", [1041, 1043], "[t]", [2278, 2301], "[formateurId, loadStudents, seanceId]", [1901, 1911], "[reloadFeedbacks, seanceId]", [1685, 1699], "[messages.length, userLanguage, welcomeMessages]"]