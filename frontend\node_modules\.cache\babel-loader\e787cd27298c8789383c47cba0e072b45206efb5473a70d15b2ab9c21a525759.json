{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\seancefeedbacklist.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography, Paper, Divider, Dialog, DialogTitle, DialogContent, Stack, Button, Card, CardContent, CardHeader, Grid, IconButton } from \"@mui/material\";\nimport { Close } from \"@mui/icons-material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { DataGrid } from '@mui/x-data-grid';\nimport { Feedback as FeedbackIcon } from \"@mui/icons-material\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SeanceFeedbackList = () => {\n  _s();\n  const {\n    t\n  } = useTranslation('seances');\n  const {\n    id: seanceId\n  } = useParams();\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\n  const reloadFeedbacks = () => {\n    if (seanceId) {\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`).then(res => {\n        console.log(\"Feedbacks reçus:\", res.data);\n        setFeedbacks(res.data);\n        // After setting feedbacks, send cleanup request to backend\n        const displayedIds = res.data.map(fb => fb.id).filter(id => id !== undefined);\n        axios.delete('http://localhost:8000/feedback-formateur/cleanup', {\n          data: {\n            formateurId: null,\n            // Set formateurId if available in context\n            seanceId: Number(seanceId),\n            keepIds: displayedIds\n          }\n        }).then(() => {\n          console.log('Cleanup request sent successfully');\n        }).catch(err => {\n          console.error('Error sending cleanup request:', err);\n        });\n      }).catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\n    }\n  };\n  useEffect(() => {\n    reloadFeedbacks();\n  }, [seanceId]);\n\n  // Fonction pour transformer les données de feedback en format answers avec emojis\n  const createAnswersFromFeedback = feedback => {\n    if (!feedback) return [];\n    const answers = [];\n    if (feedback.sessionRating) answers.push({\n      question: 'Note de la session',\n      answer: feedback.sessionRating\n    });\n    if (feedback.contentQuality) answers.push({\n      question: 'Qualité du contenu',\n      answer: feedback.contentQuality\n    });\n    if (feedback.sessionOrganization) answers.push({\n      question: 'Organisation de la session',\n      answer: feedback.sessionOrganization\n    });\n    if (feedback.objectivesAchieved) answers.push({\n      question: 'Objectifs atteints',\n      answer: feedback.objectivesAchieved\n    });\n    if (feedback.sessionDuration) answers.push({\n      question: 'Durée de la séance',\n      answer: feedback.sessionDuration\n    });\n    if (feedback.trainerRating) answers.push({\n      question: 'Note du formateur',\n      answer: feedback.trainerRating\n    });\n    if (feedback.trainerClarity) answers.push({\n      question: 'Clarté du formateur',\n      answer: feedback.trainerClarity\n    });\n    if (feedback.trainerAvailability) answers.push({\n      question: 'Disponibilité du formateur',\n      answer: feedback.trainerAvailability\n    });\n    if (feedback.trainerPedagogy) answers.push({\n      question: 'Pédagogie du formateur',\n      answer: feedback.trainerPedagogy\n    });\n    if (feedback.trainerInteraction) answers.push({\n      question: 'Interaction du formateur',\n      answer: feedback.trainerInteraction\n    });\n    if (feedback.teamRating) answers.push({\n      question: 'Note de l\\'équipe',\n      answer: feedback.teamRating\n    });\n    if (feedback.teamCollaboration) answers.push({\n      question: 'Collaboration de l\\'équipe',\n      answer: feedback.teamCollaboration\n    });\n    if (feedback.teamParticipation) answers.push({\n      question: 'Participation de l\\'équipe',\n      answer: feedback.teamParticipation\n    });\n    if (feedback.teamCommunication) answers.push({\n      question: 'Communication de l\\'équipe',\n      answer: feedback.teamCommunication\n    });\n    if (feedback.sessionComments) answers.push({\n      question: 'Commentaires sur la session',\n      answer: feedback.sessionComments\n    });\n    if (feedback.trainerComments) answers.push({\n      question: 'Commentaires sur le formateur',\n      answer: feedback.trainerComments\n    });\n    if (feedback.teamComments) answers.push({\n      question: 'Commentaires sur l\\'équipe',\n      answer: feedback.teamComments\n    });\n    if (feedback.suggestions) answers.push({\n      question: 'Suggestions d\\'amélioration',\n      answer: feedback.suggestions\n    });\n    if (feedback.wouldRecommend) answers.push({\n      question: 'Recommanderiez-vous cette formation ?',\n      answer: feedback.wouldRecommend\n    });\n    return answers;\n  };\n  const feedbackColumns = [{\n    field: 'id',\n    headerName: t('id'),\n    width: 70\n  }, {\n    field: 'studentName',\n    headerName: t('studentName'),\n    width: 200\n  }, {\n    field: 'studentEmail',\n    headerName: t('studentEmail'),\n    width: 250\n  }, {\n    field: 'fullFeedback',\n    headerName: t('fullFeedback'),\n    width: 250,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      size: \"small\",\n      onClick: async () => {\n        try {\n          // Récupérer les détails complets du feedback\n          const response = await axios.get(`http://localhost:8000/feedback/seance/details/${seanceId}/${params.row.userId || params.row.id}`);\n          setSelectedFeedback(response.data);\n          setFeedbackDialogOpen(true);\n        } catch (error) {\n          console.error('Erreur lors de la récupération des détails:', error);\n          // Fallback: utiliser les données de base\n          setSelectedFeedback(params.row);\n          setFeedbackDialogOpen(true);\n        }\n      },\n      children: t('showMore')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'averageRating',\n    headerName: t('averageRating'),\n    width: 180,\n    renderCell: params => {\n      const avg = params.row.averageRating;\n      if (avg === null || avg === undefined) return t('noRating');\n      const rounded = Math.round(avg);\n      const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n      const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 22\n          },\n          children: moodEmojis[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginLeft: 4\n          },\n          children: moodLabels[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888',\n            marginLeft: 4\n          },\n          children: [\"(\", avg.toFixed(2), \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 2,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(FeedbackIcon, {\n        fontSize: \"large\",\n        sx: {\n          verticalAlign: 'middle',\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), t('feedbackList')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 500,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: feedbacks,\n          columns: feedbackColumns,\n          pageSize: 7,\n          rowsPerPageOptions: [5, 10, 20],\n          disableSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: feedbackDialogOpen,\n      onClose: () => setFeedbackDialogOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      sx: {\n        '& .MuiDialog-paper': {\n          maxHeight: \"90vh\",\n          overflow: \"auto\",\n          borderRadius: 3\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          color: \"white\",\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          pr: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            fontWeight: \"bold\",\n            children: [\"\\uD83D\\uDCCB \", t('feedbackFrom'), \" \", selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9,\n              mt: 0.5\n            },\n            children: selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentEmail\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setFeedbackDialogOpen(false),\n          sx: {\n            color: \"white\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          p: 3\n        },\n        children: selectedFeedback ? (() => {\n          const answers = createAnswersFromFeedback(selectedFeedback);\n          if (answers.length === 0) {\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: t('noFeedbackData')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 19\n            }, this);\n          }\n\n          // Calculer la note moyenne\n          const numericAnswers = answers.map(qa => Number(qa.answer)).filter(val => !isNaN(val) && val >= 1 && val <= 5);\n          const averageRating = (selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.averageRating) || (numericAnswers.length > 0 ? numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length : 0);\n          const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n          const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [averageRating > 0 && /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3,\n                bgcolor: 'primary.main',\n                color: 'white'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"\\uD83D\\uDCCA \\xC9valuation Moyenne\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h2\",\n                  fontWeight: \"bold\",\n                  children: [averageRating.toFixed(1), \"/5\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: [moodEmojis[Math.round(averageRating) - 1], \" \", moodLabels[Math.round(averageRating) - 1]]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 21\n            }, this), (() => {\n              // Définition des sections thématiques avec emojis et couleurs\n              const sections = [{\n                title: t('sessionSection'),\n                emoji: '📚',\n                color: 'primary.light',\n                keywords: ['note de la session', 'organisation', 'objectifs', 'durée', 'durée de la séance', 'qualité du contenu', 'commentaires sur la session']\n              }, {\n                title: t('trainerSection'),\n                emoji: '👨‍🏫',\n                color: 'success.light',\n                keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\n              }, {\n                title: t('teamSection'),\n                emoji: '👥',\n                color: 'info.light',\n                keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\n              }, {\n                title: t('suggestionsSection'),\n                emoji: '💡',\n                color: 'warning.light',\n                keywords: ['suggestions', 'amélioration', 'recommanderait']\n              }];\n\n              // Grouper les réponses par section avec un matching robuste\n              function normalize(str) {\n                return str.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\n                .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\n              }\n              const groupedAnswers = answers.length > 0 ? sections.map(section => ({\n                ...section,\n                answers: answers.filter(qa => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\n              })) : [];\n\n              // Réponses non classées\n              const otherAnswers = answers.length > 0 ? answers.filter(qa => !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))) : [];\n              return /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [groupedAnswers.map((section, idx) => section.answers.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n                    sx: {\n                      bgcolor: section.color,\n                      color: 'white'\n                    },\n                    title: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: '1.2rem'\n                        },\n                        children: section.emoji\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: section.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 310,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 305,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: /*#__PURE__*/_jsxDEV(Grid, {\n                      container: true,\n                      spacing: 2,\n                      children: section.answers.map((qa, qidx) => {\n                        let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                        let value = isNumeric ? Number(qa.answer) : null;\n                        return /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 12,\n                          sm: isNumeric ? 6 : 12,\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              p: 2,\n                              bgcolor: 'grey.50',\n                              borderRadius: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              fontWeight: \"600\",\n                              gutterBottom: true,\n                              children: qa.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 322,\n                              columnNumber: 37\n                            }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: 1\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                                sx: {\n                                  fontSize: '1.5rem'\n                                },\n                                children: moodEmojis[value - 1]\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 327,\n                                columnNumber: 41\n                              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"body2\",\n                                  fontWeight: \"600\",\n                                  children: moodLabels[value - 1]\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 331,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  color: \"text.secondary\",\n                                  children: [\"(\", value, \"/5)\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 334,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 330,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 326,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              style: {\n                                whiteSpace: 'pre-line'\n                              },\n                              children: qa.answer || t('noAnswer')\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 340,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 321,\n                            columnNumber: 35\n                          }, this)\n                        }, qidx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 320,\n                          columnNumber: 33\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 23\n                }, this)), otherAnswers.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n                    sx: {\n                      bgcolor: 'grey.600',\n                      color: 'white'\n                    },\n                    title: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: '1.2rem'\n                        },\n                        children: \"\\uD83D\\uDCDD\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 359,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: t('otherSection')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 360,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 355,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: /*#__PURE__*/_jsxDEV(Grid, {\n                      container: true,\n                      spacing: 2,\n                      children: otherAnswers.map((qa, qidx) => {\n                        let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                        let value = isNumeric ? Number(qa.answer) : null;\n                        return /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 12,\n                          sm: isNumeric ? 6 : 12,\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              p: 2,\n                              bgcolor: 'grey.50',\n                              borderRadius: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              fontWeight: \"600\",\n                              gutterBottom: true,\n                              children: qa.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 372,\n                              columnNumber: 35\n                            }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: 1\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                                sx: {\n                                  fontSize: '1.5rem'\n                                },\n                                children: moodEmojis[value - 1]\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 377,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"body2\",\n                                  fontWeight: \"600\",\n                                  children: moodLabels[value - 1]\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 381,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  color: \"text.secondary\",\n                                  children: [\"(\", value, \"/5)\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 384,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 380,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 376,\n                              columnNumber: 37\n                            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              style: {\n                                whiteSpace: 'pre-line'\n                              },\n                              children: qa.answer || t('noAnswer')\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 390,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 371,\n                            columnNumber: 33\n                          }, this)\n                        }, qidx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 365,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true);\n            })()]\n          }, void 0, true);\n        })() : /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          sx: {\n            textAlign: 'center',\n            py: 3\n          },\n          children: \"Aucune donn\\xE9e de feedback disponible\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(SeanceFeedbackList, \"pNQGhhrcD8e4HlzoelZrTU8avJQ=\", false, function () {\n  return [useTranslation, useParams];\n});\n_c = SeanceFeedbackList;\nexport default SeanceFeedbackList;\nvar _c;\n$RefreshReg$(_c, \"SeanceFeedbackList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "Box", "Typography", "Paper", "Divider", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "IconButton", "Close", "useTranslation", "axios", "DataGrid", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SeanceFeedbackList", "_s", "t", "id", "seanceId", "feedbacks", "setFeedbacks", "selectedFeedback", "setSelectedFeedback", "feedbackDialogOpen", "setFeedbackDialogOpen", "reloadFeedbacks", "get", "then", "res", "console", "log", "data", "displayedIds", "map", "fb", "filter", "undefined", "delete", "formateurId", "Number", "keepIds", "catch", "err", "error", "createAnswersFromFeedback", "feedback", "answers", "sessionRating", "push", "question", "answer", "contentQuality", "sessionOrganization", "objectivesAchieved", "sessionDuration", "trainerRating", "trainerClarity", "trainerAvailability", "trainerPedagogy", "trainerInteraction", "teamRating", "teamCollaboration", "teamParticipation", "teamCommunication", "sessionComments", "trainerComments", "teamComments", "suggestions", "wouldRecommend", "feedbackColumns", "field", "headerName", "width", "renderCell", "params", "variant", "size", "onClick", "response", "row", "userId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "avg", "averageRating", "rounded", "Math", "round", "moodEmojis", "<PERSON><PERSON><PERSON><PERSON>", "style", "display", "alignItems", "gap", "fontSize", "fontWeight", "marginLeft", "color", "toFixed", "p", "mb", "sx", "verticalAlign", "mr", "height", "rows", "columns", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "maxHeight", "overflow", "borderRadius", "background", "justifyContent", "pr", "component", "studentName", "opacity", "mt", "studentEmail", "length", "textAlign", "py", "numericAnswers", "qa", "val", "isNaN", "reduce", "a", "b", "bgcolor", "gutterBottom", "sections", "title", "emoji", "keywords", "normalize", "str", "toLowerCase", "replace", "groupedAnswers", "section", "some", "keyword", "includes", "otherAnswers", "idx", "container", "spacing", "qidx", "isNumeric", "value", "item", "xs", "sm", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/seancefeedbacklist.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Divider,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  Stack,\r\n  <PERSON>ton,\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  Grid,\r\n  IconButton,\r\n} from \"@mui/material\";\r\nimport { Close } from \"@mui/icons-material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport { Feedback as FeedbackIcon } from \"@mui/icons-material\";\r\n\r\nconst SeanceFeedbackList = () => {\r\n  const { t } = useTranslation('seances');\r\n  const { id: seanceId } = useParams();\r\n  const [feedbacks, setFeedbacks] = useState([]);\r\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\r\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\r\n\r\n  const reloadFeedbacks = () => {\r\n    if (seanceId) {\r\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`)\r\n        .then(res => {\r\n          console.log(\"Feedbacks reçus:\", res.data);\r\n          setFeedbacks(res.data);\r\n          // After setting feedbacks, send cleanup request to backend\r\n          const displayedIds = res.data.map(fb => fb.id).filter(id => id !== undefined);\r\n          axios.delete('http://localhost:8000/feedback-formateur/cleanup', {\r\n            data: {\r\n              formateurId: null, // Set formateurId if available in context\r\n              seanceId: Number(seanceId),\r\n              keepIds: displayedIds,\r\n            }\r\n          }).then(() => {\r\n            console.log('Cleanup request sent successfully');\r\n          }).catch(err => {\r\n            console.error('Error sending cleanup request:', err);\r\n          });\r\n        })\r\n        .catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    reloadFeedbacks();\r\n  }, [seanceId]);\r\n\r\n  // Fonction pour transformer les données de feedback en format answers avec emojis\r\n  const createAnswersFromFeedback = (feedback) => {\r\n    if (!feedback) return [];\r\n\r\n    const answers = [];\r\n\r\n    if (feedback.sessionRating) answers.push({ question: 'Note de la session', answer: feedback.sessionRating });\r\n    if (feedback.contentQuality) answers.push({ question: 'Qualité du contenu', answer: feedback.contentQuality });\r\n    if (feedback.sessionOrganization) answers.push({ question: 'Organisation de la session', answer: feedback.sessionOrganization });\r\n    if (feedback.objectivesAchieved) answers.push({ question: 'Objectifs atteints', answer: feedback.objectivesAchieved });\r\n    if (feedback.sessionDuration) answers.push({ question: 'Durée de la séance', answer: feedback.sessionDuration });\r\n\r\n    if (feedback.trainerRating) answers.push({ question: 'Note du formateur', answer: feedback.trainerRating });\r\n    if (feedback.trainerClarity) answers.push({ question: 'Clarté du formateur', answer: feedback.trainerClarity });\r\n    if (feedback.trainerAvailability) answers.push({ question: 'Disponibilité du formateur', answer: feedback.trainerAvailability });\r\n    if (feedback.trainerPedagogy) answers.push({ question: 'Pédagogie du formateur', answer: feedback.trainerPedagogy });\r\n    if (feedback.trainerInteraction) answers.push({ question: 'Interaction du formateur', answer: feedback.trainerInteraction });\r\n\r\n    if (feedback.teamRating) answers.push({ question: 'Note de l\\'équipe', answer: feedback.teamRating });\r\n    if (feedback.teamCollaboration) answers.push({ question: 'Collaboration de l\\'équipe', answer: feedback.teamCollaboration });\r\n    if (feedback.teamParticipation) answers.push({ question: 'Participation de l\\'équipe', answer: feedback.teamParticipation });\r\n    if (feedback.teamCommunication) answers.push({ question: 'Communication de l\\'équipe', answer: feedback.teamCommunication });\r\n\r\n    if (feedback.sessionComments) answers.push({ question: 'Commentaires sur la session', answer: feedback.sessionComments });\r\n    if (feedback.trainerComments) answers.push({ question: 'Commentaires sur le formateur', answer: feedback.trainerComments });\r\n    if (feedback.teamComments) answers.push({ question: 'Commentaires sur l\\'équipe', answer: feedback.teamComments });\r\n    if (feedback.suggestions) answers.push({ question: 'Suggestions d\\'amélioration', answer: feedback.suggestions });\r\n    if (feedback.wouldRecommend) answers.push({ question: 'Recommanderiez-vous cette formation ?', answer: feedback.wouldRecommend });\r\n\r\n    return answers;\r\n  };\r\n\r\n  const feedbackColumns = [\r\n    { field: 'id', headerName: t('id'), width: 70 },\r\n    { field: 'studentName', headerName: t('studentName'), width: 200 },\r\n    { field: 'studentEmail', headerName: t('studentEmail'), width: 250 },\r\n    {\r\n      field: 'fullFeedback',\r\n      headerName: t('fullFeedback'),\r\n      width: 250,\r\n      renderCell: (params) => (\r\n        <Button\r\n          variant=\"outlined\"\r\n          size=\"small\"\r\n          onClick={async () => {\r\n            try {\r\n              // Récupérer les détails complets du feedback\r\n              const response = await axios.get(`http://localhost:8000/feedback/seance/details/${seanceId}/${params.row.userId || params.row.id}`);\r\n              setSelectedFeedback(response.data);\r\n              setFeedbackDialogOpen(true);\r\n            } catch (error) {\r\n              console.error('Erreur lors de la récupération des détails:', error);\r\n              // Fallback: utiliser les données de base\r\n              setSelectedFeedback(params.row);\r\n              setFeedbackDialogOpen(true);\r\n            }\r\n          }}\r\n        >\r\n          {t('showMore')}\r\n        </Button>\r\n      ),\r\n    },\r\n    {\r\n      field: 'averageRating',\r\n      headerName: t('averageRating'),\r\n      width: 180,\r\n      renderCell: (params) => {\r\n        const avg = params.row.averageRating;\r\n        if (avg === null || avg === undefined) return t('noRating');\r\n        const rounded = Math.round(avg);\r\n        const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n        const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n        return (\r\n          <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>\r\n            <span style={{ fontSize: 22 }}>{moodEmojis[rounded - 1]}</span>\r\n            <span style={{ fontWeight: 'bold', marginLeft: 4 }}>{moodLabels[rounded - 1]}</span>\r\n            <span style={{ color: '#888', marginLeft: 4 }}>({avg.toFixed(2)})</span>\r\n          </span>\r\n        );\r\n      }\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Box p={2}>\r\n      <Typography variant=\"h4\" mb={3}>\r\n        <FeedbackIcon fontSize=\"large\" sx={{ verticalAlign: 'middle', mr: 2 }} />\r\n        {t('feedbackList')}\r\n      </Typography>\r\n\r\n      <Paper sx={{ p: 3 }}>\r\n        <Box sx={{ height: 500, width: '100%' }}>\r\n          <DataGrid\r\n            rows={feedbacks}\r\n            columns={feedbackColumns}\r\n            pageSize={7}\r\n            rowsPerPageOptions={[5, 10, 20]}\r\n            disableSelectionOnClick\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Feedback Dialog */}\r\n      <Dialog\r\n        open={feedbackDialogOpen}\r\n        onClose={() => setFeedbackDialogOpen(false)}\r\n        maxWidth=\"md\"\r\n        fullWidth\r\n        sx={{\r\n          '& .MuiDialog-paper': {\r\n            maxHeight: \"90vh\",\r\n            overflow: \"auto\",\r\n            borderRadius: 3,\r\n          }\r\n        }}\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\r\n            color: \"white\",\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n            pr: 1,\r\n          }}\r\n        >\r\n          <Box>\r\n            <Typography variant=\"h5\" component=\"h1\" fontWeight=\"bold\">\r\n              📋 {t('feedbackFrom')} {selectedFeedback?.studentName}\r\n            </Typography>\r\n            <Typography variant=\"body2\" sx={{ opacity: 0.9, mt: 0.5 }}>\r\n              {selectedFeedback?.studentEmail}\r\n            </Typography>\r\n          </Box>\r\n          <IconButton onClick={() => setFeedbackDialogOpen(false)} sx={{ color: \"white\" }}>\r\n            <Close />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent sx={{ p: 3 }}>\r\n            {selectedFeedback ? (() => {\r\n              const answers = createAnswersFromFeedback(selectedFeedback);\r\n\r\n              if (answers.length === 0) {\r\n                return (\r\n                  <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                    {t('noFeedbackData')}\r\n                  </Typography>\r\n                );\r\n              }\r\n\r\n              // Calculer la note moyenne\r\n              const numericAnswers = answers\r\n                .map(qa => Number(qa.answer))\r\n                .filter(val => !isNaN(val) && val >= 1 && val <= 5);\r\n              const averageRating = selectedFeedback?.averageRating ||\r\n                (numericAnswers.length > 0 ? numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length : 0);\r\n\r\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n\r\n              return (\r\n                <>\r\n                  {/* Évaluation moyenne */}\r\n                  {averageRating > 0 && (\r\n                    <Card sx={{ mb: 3, bgcolor: 'primary.main', color: 'white' }}>\r\n                      <CardContent sx={{ textAlign: 'center' }}>\r\n                        <Typography variant=\"h6\" gutterBottom>\r\n                          📊 Évaluation Moyenne\r\n                        </Typography>\r\n                        <Typography variant=\"h2\" fontWeight=\"bold\">\r\n                          {averageRating.toFixed(1)}/5\r\n                        </Typography>\r\n                        <Typography variant=\"subtitle1\" sx={{ opacity: 0.9 }}>\r\n                          {moodEmojis[Math.round(averageRating) - 1]} {moodLabels[Math.round(averageRating) - 1]}\r\n                        </Typography>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n\r\n\r\n                  {(() => {\r\n                    // Définition des sections thématiques avec emojis et couleurs\r\n                    const sections = [\r\n                      {\r\n                        title: t('sessionSection'),\r\n                        emoji: '📚',\r\n                        color: 'primary.light',\r\n                        keywords: [\r\n                          'note de la session',\r\n                          'organisation',\r\n                          'objectifs',\r\n                          'durée',\r\n                          'durée de la séance',\r\n                          'qualité du contenu',\r\n                          'commentaires sur la session'\r\n                        ]\r\n                      },\r\n                      {\r\n                        title: t('trainerSection'),\r\n                        emoji: '👨‍🏫',\r\n                        color: 'success.light',\r\n                        keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\r\n                      },\r\n                      {\r\n                        title: t('teamSection'),\r\n                        emoji: '👥',\r\n                        color: 'info.light',\r\n                        keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\r\n                      },\r\n                      {\r\n                        title: t('suggestionsSection'),\r\n                        emoji: '💡',\r\n                        color: 'warning.light',\r\n                        keywords: ['suggestions', 'amélioration', 'recommanderait']\r\n                      }\r\n                    ];\r\n\r\n                    // Grouper les réponses par section avec un matching robuste\r\n                    function normalize(str) {\r\n                      return str\r\n                        .toLowerCase()\r\n                        .normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\r\n                        .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\r\n                    }\r\n\r\n                    const groupedAnswers = answers.length > 0 ? sections.map(section => ({\r\n                      ...section,\r\n                      answers: answers.filter(qa =>\r\n                        section.keywords.some(keyword =>\r\n                          normalize(qa.question).includes(normalize(keyword))\r\n                        )\r\n                      )\r\n                    })) : [];\r\n\r\n                    // Réponses non classées\r\n                    const otherAnswers = answers.length > 0 ? answers.filter(qa =>\r\n                      !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\r\n                    ) : [];\r\n\r\n                    return (\r\n                <>\r\n                  {groupedAnswers.map((section, idx) =>\r\n                    section.answers.length > 0 && (\r\n                      <Card key={idx} sx={{ mb: 3 }}>\r\n                        <CardHeader\r\n                          sx={{ bgcolor: section.color, color: 'white' }}\r\n                          title={\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                              <Typography sx={{ fontSize: '1.2rem' }}>{section.emoji}</Typography>\r\n                              <Typography variant=\"h6\">{section.title}</Typography>\r\n                            </Box>\r\n                          }\r\n                        />\r\n                        <CardContent>\r\n                          <Grid container spacing={2}>\r\n                            {section.answers.map((qa, qidx) => {\r\n                              let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                              let value = isNumeric ? Number(qa.answer) : null;\r\n                              return (\r\n                                <Grid item xs={12} sm={isNumeric ? 6 : 12} key={qidx}>\r\n                                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\r\n                                    <Typography variant=\"body2\" fontWeight=\"600\" gutterBottom>\r\n                                      {qa.question}\r\n                                    </Typography>\r\n                                    {isNumeric ? (\r\n                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                        <Typography sx={{ fontSize: '1.5rem' }}>\r\n                                          {moodEmojis[value - 1]}\r\n                                        </Typography>\r\n                                        <Box>\r\n                                          <Typography variant=\"body2\" fontWeight=\"600\">\r\n                                            {moodLabels[value - 1]}\r\n                                          </Typography>\r\n                                          <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                            ({value}/5)\r\n                                          </Typography>\r\n                                        </Box>\r\n                                      </Box>\r\n                                    ) : (\r\n                                      <Typography variant=\"body2\" style={{ whiteSpace: 'pre-line' }}>\r\n                                        {qa.answer || t('noAnswer')}\r\n                                      </Typography>\r\n                                    )}\r\n                                  </Box>\r\n                                </Grid>\r\n                              );\r\n                            })}\r\n                          </Grid>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )\r\n                  )}\r\n                  {otherAnswers.length > 0 && (\r\n                    <Card sx={{ mb: 3 }}>\r\n                      <CardHeader\r\n                        sx={{ bgcolor: 'grey.600', color: 'white' }}\r\n                        title={\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                            <Typography sx={{ fontSize: '1.2rem' }}>📝</Typography>\r\n                            <Typography variant=\"h6\">{t('otherSection')}</Typography>\r\n                          </Box>\r\n                        }\r\n                      />\r\n                      <CardContent>\r\n                        <Grid container spacing={2}>\r\n                          {otherAnswers.map((qa, qidx) => {\r\n                            let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                            let value = isNumeric ? Number(qa.answer) : null;\r\n                            return (\r\n                              <Grid item xs={12} sm={isNumeric ? 6 : 12} key={qidx}>\r\n                                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\r\n                                  <Typography variant=\"body2\" fontWeight=\"600\" gutterBottom>\r\n                                    {qa.question}\r\n                                  </Typography>\r\n                                  {isNumeric ? (\r\n                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                      <Typography sx={{ fontSize: '1.5rem' }}>\r\n                                        {moodEmojis[value - 1]}\r\n                                      </Typography>\r\n                                      <Box>\r\n                                        <Typography variant=\"body2\" fontWeight=\"600\">\r\n                                          {moodLabels[value - 1]}\r\n                                        </Typography>\r\n                                        <Typography variant=\"caption\" color=\"text.secondary\">\r\n                                          ({value}/5)\r\n                                        </Typography>\r\n                                      </Box>\r\n                                    </Box>\r\n                                  ) : (\r\n                                    <Typography variant=\"body2\" style={{ whiteSpace: 'pre-line' }}>\r\n                                      {qa.answer || t('noAnswer')}\r\n                                    </Typography>\r\n                                  )}\r\n                                </Box>\r\n                              </Grid>\r\n                            );\r\n                          })}\r\n                        </Grid>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n                    </>\r\n                    );\r\n                  })()}\r\n                </>\r\n              );\r\n            })() : (\r\n              <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                Aucune donnée de feedback disponible\r\n              </Typography>\r\n            )}\r\n        </DialogContent>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default SeanceFeedbackList;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,UAAU,QACL,eAAe;AACtB,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,QAAQ,IAAIC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC;EAAE,CAAC,GAAGX,cAAc,CAAC,SAAS,CAAC;EACvC,MAAM;IAAEY,EAAE,EAAEC;EAAS,CAAC,GAAG7B,SAAS,CAAC,CAAC;EACpC,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMqC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIP,QAAQ,EAAE;MACZZ,KAAK,CAACoB,GAAG,CAAC,sDAAsDR,QAAQ,EAAE,CAAC,CACxES,IAAI,CAACC,GAAG,IAAI;QACXC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,GAAG,CAACG,IAAI,CAAC;QACzCX,YAAY,CAACQ,GAAG,CAACG,IAAI,CAAC;QACtB;QACA,MAAMC,YAAY,GAAGJ,GAAG,CAACG,IAAI,CAACE,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACjB,EAAE,CAAC,CAACkB,MAAM,CAAClB,EAAE,IAAIA,EAAE,KAAKmB,SAAS,CAAC;QAC7E9B,KAAK,CAAC+B,MAAM,CAAC,kDAAkD,EAAE;UAC/DN,IAAI,EAAE;YACJO,WAAW,EAAE,IAAI;YAAE;YACnBpB,QAAQ,EAAEqB,MAAM,CAACrB,QAAQ,CAAC;YAC1BsB,OAAO,EAAER;UACX;QACF,CAAC,CAAC,CAACL,IAAI,CAAC,MAAM;UACZE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAClD,CAAC,CAAC,CAACW,KAAK,CAACC,GAAG,IAAI;UACdb,OAAO,CAACc,KAAK,CAAC,gCAAgC,EAAED,GAAG,CAAC;QACtD,CAAC,CAAC;MACJ,CAAC,CAAC,CACDD,KAAK,CAACC,GAAG,IAAIb,OAAO,CAACc,KAAK,CAAC,iCAAiC,EAAED,GAAG,CAAC,CAAC;IACxE;EACF,CAAC;EAEDvD,SAAS,CAAC,MAAM;IACdsC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAM0B,yBAAyB,GAAIC,QAAQ,IAAK;IAC9C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,MAAMC,OAAO,GAAG,EAAE;IAElB,IAAID,QAAQ,CAACE,aAAa,EAAED,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACE;IAAc,CAAC,CAAC;IAC5G,IAAIF,QAAQ,CAACM,cAAc,EAAEL,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACM;IAAe,CAAC,CAAC;IAC9G,IAAIN,QAAQ,CAACO,mBAAmB,EAAEN,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACO;IAAoB,CAAC,CAAC;IAChI,IAAIP,QAAQ,CAACQ,kBAAkB,EAAEP,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACQ;IAAmB,CAAC,CAAC;IACtH,IAAIR,QAAQ,CAACS,eAAe,EAAER,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,oBAAoB;MAAEC,MAAM,EAAEL,QAAQ,CAACS;IAAgB,CAAC,CAAC;IAEhH,IAAIT,QAAQ,CAACU,aAAa,EAAET,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEC,MAAM,EAAEL,QAAQ,CAACU;IAAc,CAAC,CAAC;IAC3G,IAAIV,QAAQ,CAACW,cAAc,EAAEV,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAEC,MAAM,EAAEL,QAAQ,CAACW;IAAe,CAAC,CAAC;IAC/G,IAAIX,QAAQ,CAACY,mBAAmB,EAAEX,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACY;IAAoB,CAAC,CAAC;IAChI,IAAIZ,QAAQ,CAACa,eAAe,EAAEZ,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,wBAAwB;MAAEC,MAAM,EAAEL,QAAQ,CAACa;IAAgB,CAAC,CAAC;IACpH,IAAIb,QAAQ,CAACc,kBAAkB,EAAEb,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,0BAA0B;MAAEC,MAAM,EAAEL,QAAQ,CAACc;IAAmB,CAAC,CAAC;IAE5H,IAAId,QAAQ,CAACe,UAAU,EAAEd,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,mBAAmB;MAAEC,MAAM,EAAEL,QAAQ,CAACe;IAAW,CAAC,CAAC;IACrG,IAAIf,QAAQ,CAACgB,iBAAiB,EAAEf,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACgB;IAAkB,CAAC,CAAC;IAC5H,IAAIhB,QAAQ,CAACiB,iBAAiB,EAAEhB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACiB;IAAkB,CAAC,CAAC;IAC5H,IAAIjB,QAAQ,CAACkB,iBAAiB,EAAEjB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACkB;IAAkB,CAAC,CAAC;IAE5H,IAAIlB,QAAQ,CAACmB,eAAe,EAAElB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEC,MAAM,EAAEL,QAAQ,CAACmB;IAAgB,CAAC,CAAC;IACzH,IAAInB,QAAQ,CAACoB,eAAe,EAAEnB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,+BAA+B;MAAEC,MAAM,EAAEL,QAAQ,CAACoB;IAAgB,CAAC,CAAC;IAC3H,IAAIpB,QAAQ,CAACqB,YAAY,EAAEpB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEC,MAAM,EAAEL,QAAQ,CAACqB;IAAa,CAAC,CAAC;IAClH,IAAIrB,QAAQ,CAACsB,WAAW,EAAErB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,6BAA6B;MAAEC,MAAM,EAAEL,QAAQ,CAACsB;IAAY,CAAC,CAAC;IACjH,IAAItB,QAAQ,CAACuB,cAAc,EAAEtB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,uCAAuC;MAAEC,MAAM,EAAEL,QAAQ,CAACuB;IAAe,CAAC,CAAC;IAEjI,OAAOtB,OAAO;EAChB,CAAC;EAED,MAAMuB,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAEvD,CAAC,CAAC,IAAI,CAAC;IAAEwD,KAAK,EAAE;EAAG,CAAC,EAC/C;IAAEF,KAAK,EAAE,aAAa;IAAEC,UAAU,EAAEvD,CAAC,CAAC,aAAa,CAAC;IAAEwD,KAAK,EAAE;EAAI,CAAC,EAClE;IAAEF,KAAK,EAAE,cAAc;IAAEC,UAAU,EAAEvD,CAAC,CAAC,cAAc,CAAC;IAAEwD,KAAK,EAAE;EAAI,CAAC,EACpE;IACEF,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAEvD,CAAC,CAAC,cAAc,CAAC;IAC7BwD,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjB/D,OAAA,CAACb,MAAM;MACL6E,OAAO,EAAC,UAAU;MAClBC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAE,MAAAA,CAAA,KAAY;QACnB,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,MAAMxE,KAAK,CAACoB,GAAG,CAAC,iDAAiDR,QAAQ,IAAIwD,MAAM,CAACK,GAAG,CAACC,MAAM,IAAIN,MAAM,CAACK,GAAG,CAAC9D,EAAE,EAAE,CAAC;UACnIK,mBAAmB,CAACwD,QAAQ,CAAC/C,IAAI,CAAC;UAClCP,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdd,OAAO,CAACc,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE;UACArB,mBAAmB,CAACoD,MAAM,CAACK,GAAG,CAAC;UAC/BvD,qBAAqB,CAAC,IAAI,CAAC;QAC7B;MACF,CAAE;MAAAyD,QAAA,EAEDjE,CAAC,CAAC,UAAU;IAAC;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEZ,CAAC,EACD;IACEf,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAEvD,CAAC,CAAC,eAAe,CAAC;IAC9BwD,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,IAAK;MACtB,MAAMY,GAAG,GAAGZ,MAAM,CAACK,GAAG,CAACQ,aAAa;MACpC,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKlD,SAAS,EAAE,OAAOpB,CAAC,CAAC,UAAU,CAAC;MAC3D,MAAMwE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC;MAC/B,MAAMK,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACjD,MAAMC,UAAU,GAAG,CAAC5E,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/G,oBACEL,OAAA;QAAMkF,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAf,QAAA,gBAC7DtE,OAAA;UAAMkF,KAAK,EAAE;YAAEI,QAAQ,EAAE;UAAG,CAAE;UAAAhB,QAAA,EAAEU,UAAU,CAACH,OAAO,GAAG,CAAC;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/D1E,OAAA;UAAMkF,KAAK,EAAE;YAAEK,UAAU,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAE,CAAE;UAAAlB,QAAA,EAAEW,UAAU,CAACJ,OAAO,GAAG,CAAC;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpF1E,OAAA;UAAMkF,KAAK,EAAE;YAAEO,KAAK,EAAE,MAAM;YAAED,UAAU,EAAE;UAAE,CAAE;UAAAlB,QAAA,GAAC,GAAC,EAACK,GAAG,CAACe,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEX;EACF,CAAC,CACF;EAED,oBACE1E,OAAA,CAACrB,GAAG;IAACgH,CAAC,EAAE,CAAE;IAAArB,QAAA,gBACRtE,OAAA,CAACpB,UAAU;MAACoF,OAAO,EAAC,IAAI;MAAC4B,EAAE,EAAE,CAAE;MAAAtB,QAAA,gBAC7BtE,OAAA,CAACF,YAAY;QAACwF,QAAQ,EAAC,OAAO;QAACO,EAAE,EAAE;UAAEC,aAAa,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxErE,CAAC,CAAC,cAAc,CAAC;IAAA;MAAAkE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEb1E,OAAA,CAACnB,KAAK;MAACgH,EAAE,EAAE;QAAEF,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,eAClBtE,OAAA,CAACrB,GAAG;QAACkH,EAAE,EAAE;UAAEG,MAAM,EAAE,GAAG;UAAEnC,KAAK,EAAE;QAAO,CAAE;QAAAS,QAAA,eACtCtE,OAAA,CAACJ,QAAQ;UACPqG,IAAI,EAAEzF,SAAU;UAChB0F,OAAO,EAAExC,eAAgB;UACzByC,QAAQ,EAAE,CAAE;UACZC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;UAChCC,uBAAuB;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR1E,OAAA,CAACjB,MAAM;MACLuH,IAAI,EAAE1F,kBAAmB;MACzB2F,OAAO,EAAEA,CAAA,KAAM1F,qBAAqB,CAAC,KAAK,CAAE;MAC5C2F,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTZ,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBa,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChBC,YAAY,EAAE;QAChB;MACF,CAAE;MAAAtC,QAAA,gBAEFtE,OAAA,CAAChB,WAAW;QACV6G,EAAE,EAAE;UACFgB,UAAU,EAAE,mDAAmD;UAC/DpB,KAAK,EAAE,OAAO;UACdN,OAAO,EAAE,MAAM;UACf2B,cAAc,EAAE,eAAe;UAC/B1B,UAAU,EAAE,QAAQ;UACpB2B,EAAE,EAAE;QACN,CAAE;QAAAzC,QAAA,gBAEFtE,OAAA,CAACrB,GAAG;UAAA2F,QAAA,gBACFtE,OAAA,CAACpB,UAAU;YAACoF,OAAO,EAAC,IAAI;YAACgD,SAAS,EAAC,IAAI;YAACzB,UAAU,EAAC,MAAM;YAAAjB,QAAA,GAAC,eACrD,EAACjE,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC,EAACK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuG,WAAW;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACb1E,OAAA,CAACpB,UAAU;YAACoF,OAAO,EAAC,OAAO;YAAC6B,EAAE,EAAE;cAAEqB,OAAO,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAI,CAAE;YAAA7C,QAAA,EACvD5D,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0G;UAAY;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN1E,OAAA,CAACR,UAAU;UAAC0E,OAAO,EAAEA,CAAA,KAAMrD,qBAAqB,CAAC,KAAK,CAAE;UAACgF,EAAE,EAAE;YAAEJ,KAAK,EAAE;UAAQ,CAAE;UAAAnB,QAAA,eAC9EtE,OAAA,CAACP,KAAK;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd1E,OAAA,CAACf,aAAa;QAAC4G,EAAE,EAAE;UAAEF,CAAC,EAAE;QAAE,CAAE;QAAArB,QAAA,EACvB5D,gBAAgB,GAAG,CAAC,MAAM;UACzB,MAAMyB,OAAO,GAAGF,yBAAyB,CAACvB,gBAAgB,CAAC;UAE3D,IAAIyB,OAAO,CAACkF,MAAM,KAAK,CAAC,EAAE;YACxB,oBACErH,OAAA,CAACpB,UAAU;cAAC6G,KAAK,EAAC,gBAAgB;cAACI,EAAE,EAAE;gBAAEyB,SAAS,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjD,QAAA,EACnEjE,CAAC,CAAC,gBAAgB;YAAC;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAEjB;;UAEA;UACA,MAAM8C,cAAc,GAAGrF,OAAO,CAC3Bb,GAAG,CAACmG,EAAE,IAAI7F,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,CAAC,CAC5Bf,MAAM,CAACkG,GAAG,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC;UACrD,MAAM9C,aAAa,GAAG,CAAAlE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkE,aAAa,MAClD4C,cAAc,CAACH,MAAM,GAAG,CAAC,GAAGG,cAAc,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGN,cAAc,CAACH,MAAM,GAAG,CAAC,CAAC;UAErG,MAAMrC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACjD,MAAMC,UAAU,GAAG,CAAC5E,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;UAE/G,oBACEL,OAAA,CAAAE,SAAA;YAAAoE,QAAA,GAEGM,aAAa,GAAG,CAAC,iBAChB5E,OAAA,CAACZ,IAAI;cAACyG,EAAE,EAAE;gBAAED,EAAE,EAAE,CAAC;gBAAEmC,OAAO,EAAE,cAAc;gBAAEtC,KAAK,EAAE;cAAQ,CAAE;cAAAnB,QAAA,eAC3DtE,OAAA,CAACX,WAAW;gBAACwG,EAAE,EAAE;kBAAEyB,SAAS,EAAE;gBAAS,CAAE;gBAAAhD,QAAA,gBACvCtE,OAAA,CAACpB,UAAU;kBAACoF,OAAO,EAAC,IAAI;kBAACgE,YAAY;kBAAA1D,QAAA,EAAC;gBAEtC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1E,OAAA,CAACpB,UAAU;kBAACoF,OAAO,EAAC,IAAI;kBAACuB,UAAU,EAAC,MAAM;kBAAAjB,QAAA,GACvCM,aAAa,CAACc,OAAO,CAAC,CAAC,CAAC,EAAC,IAC5B;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1E,OAAA,CAACpB,UAAU;kBAACoF,OAAO,EAAC,WAAW;kBAAC6B,EAAE,EAAE;oBAAEqB,OAAO,EAAE;kBAAI,CAAE;kBAAA5C,QAAA,GAClDU,UAAU,CAACF,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC,GAAG,CAAC,CAAC,EAAC,GAAC,EAACK,UAAU,CAACH,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC,GAAG,CAAC,CAAC;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP,EAIA,CAAC,MAAM;cACN;cACA,MAAMuD,QAAQ,GAAG,CACf;gBACEC,KAAK,EAAE7H,CAAC,CAAC,gBAAgB,CAAC;gBAC1B8H,KAAK,EAAE,IAAI;gBACX1C,KAAK,EAAE,eAAe;gBACtB2C,QAAQ,EAAE,CACR,oBAAoB,EACpB,cAAc,EACd,WAAW,EACX,OAAO,EACP,oBAAoB,EACpB,oBAAoB,EACpB,6BAA6B;cAEjC,CAAC,EACD;gBACEF,KAAK,EAAE7H,CAAC,CAAC,gBAAgB,CAAC;gBAC1B8H,KAAK,EAAE,OAAO;gBACd1C,KAAK,EAAE,eAAe;gBACtB2C,QAAQ,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAE,+BAA+B;cACxH,CAAC,EACD;gBACEF,KAAK,EAAE7H,CAAC,CAAC,aAAa,CAAC;gBACvB8H,KAAK,EAAE,IAAI;gBACX1C,KAAK,EAAE,YAAY;gBACnB2C,QAAQ,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,4BAA4B;cACjH,CAAC,EACD;gBACEF,KAAK,EAAE7H,CAAC,CAAC,oBAAoB,CAAC;gBAC9B8H,KAAK,EAAE,IAAI;gBACX1C,KAAK,EAAE,eAAe;gBACtB2C,QAAQ,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB;cAC5D,CAAC,CACF;;cAED;cACA,SAASC,SAASA,CAACC,GAAG,EAAE;gBACtB,OAAOA,GAAG,CACPC,WAAW,CAAC,CAAC,CACbF,SAAS,CAAC,KAAK,CAAC,CAACG,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;gBAAA,CACjDA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;cAChC;cAEA,MAAMC,cAAc,GAAGtG,OAAO,CAACkF,MAAM,GAAG,CAAC,GAAGY,QAAQ,CAAC3G,GAAG,CAACoH,OAAO,KAAK;gBACnE,GAAGA,OAAO;gBACVvG,OAAO,EAAEA,OAAO,CAACX,MAAM,CAACiG,EAAE,IACxBiB,OAAO,CAACN,QAAQ,CAACO,IAAI,CAACC,OAAO,IAC3BP,SAAS,CAACZ,EAAE,CAACnF,QAAQ,CAAC,CAACuG,QAAQ,CAACR,SAAS,CAACO,OAAO,CAAC,CACpD,CACF;cACF,CAAC,CAAC,CAAC,GAAG,EAAE;;cAER;cACA,MAAME,YAAY,GAAG3G,OAAO,CAACkF,MAAM,GAAG,CAAC,GAAGlF,OAAO,CAACX,MAAM,CAACiG,EAAE,IACzD,CAACQ,QAAQ,CAACU,IAAI,CAACD,OAAO,IAAIA,OAAO,CAACN,QAAQ,CAACO,IAAI,CAACC,OAAO,IAAIP,SAAS,CAACZ,EAAE,CAACnF,QAAQ,CAAC,CAACuG,QAAQ,CAACR,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC,CACjH,CAAC,GAAG,EAAE;cAEN,oBACJ5I,OAAA,CAAAE,SAAA;gBAAAoE,QAAA,GACGmE,cAAc,CAACnH,GAAG,CAAC,CAACoH,OAAO,EAAEK,GAAG,KAC/BL,OAAO,CAACvG,OAAO,CAACkF,MAAM,GAAG,CAAC,iBACxBrH,OAAA,CAACZ,IAAI;kBAAWyG,EAAE,EAAE;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,gBAC5BtE,OAAA,CAACV,UAAU;oBACTuG,EAAE,EAAE;sBAAEkC,OAAO,EAAEW,OAAO,CAACjD,KAAK;sBAAEA,KAAK,EAAE;oBAAQ,CAAE;oBAC/CyC,KAAK,eACHlI,OAAA,CAACrB,GAAG;sBAACkH,EAAE,EAAE;wBAAEV,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAf,QAAA,gBACzDtE,OAAA,CAACpB,UAAU;wBAACiH,EAAE,EAAE;0BAAEP,QAAQ,EAAE;wBAAS,CAAE;wBAAAhB,QAAA,EAAEoE,OAAO,CAACP;sBAAK;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eACpE1E,OAAA,CAACpB,UAAU;wBAACoF,OAAO,EAAC,IAAI;wBAAAM,QAAA,EAAEoE,OAAO,CAACR;sBAAK;wBAAA3D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACF1E,OAAA,CAACX,WAAW;oBAAAiF,QAAA,eACVtE,OAAA,CAACT,IAAI;sBAACyJ,SAAS;sBAACC,OAAO,EAAE,CAAE;sBAAA3E,QAAA,EACxBoE,OAAO,CAACvG,OAAO,CAACb,GAAG,CAAC,CAACmG,EAAE,EAAEyB,IAAI,KAAK;wBACjC,IAAIC,SAAS,GAAG,CAACxB,KAAK,CAAC/F,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,CAAC,IAAIX,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,IAAI,CAAC,IAAIX,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,IAAI,CAAC;wBAC7F,IAAI6G,KAAK,GAAGD,SAAS,GAAGvH,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,GAAG,IAAI;wBAChD,oBACEvC,OAAA,CAACT,IAAI;0BAAC8J,IAAI;0BAACC,EAAE,EAAE,EAAG;0BAACC,EAAE,EAAEJ,SAAS,GAAG,CAAC,GAAG,EAAG;0BAAA7E,QAAA,eACxCtE,OAAA,CAACrB,GAAG;4BAACkH,EAAE,EAAE;8BAAEF,CAAC,EAAE,CAAC;8BAAEoC,OAAO,EAAE,SAAS;8BAAEnB,YAAY,EAAE;4BAAE,CAAE;4BAAAtC,QAAA,gBACrDtE,OAAA,CAACpB,UAAU;8BAACoF,OAAO,EAAC,OAAO;8BAACuB,UAAU,EAAC,KAAK;8BAACyC,YAAY;8BAAA1D,QAAA,EACtDmD,EAAE,CAACnF;4BAAQ;8BAAAiC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,EACZyE,SAAS,gBACRnJ,OAAA,CAACrB,GAAG;8BAACkH,EAAE,EAAE;gCAAEV,OAAO,EAAE,MAAM;gCAAEC,UAAU,EAAE,QAAQ;gCAAEC,GAAG,EAAE;8BAAE,CAAE;8BAAAf,QAAA,gBACzDtE,OAAA,CAACpB,UAAU;gCAACiH,EAAE,EAAE;kCAAEP,QAAQ,EAAE;gCAAS,CAAE;gCAAAhB,QAAA,EACpCU,UAAU,CAACoE,KAAK,GAAG,CAAC;8BAAC;gCAAA7E,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ,CAAC,eACb1E,OAAA,CAACrB,GAAG;gCAAA2F,QAAA,gBACFtE,OAAA,CAACpB,UAAU;kCAACoF,OAAO,EAAC,OAAO;kCAACuB,UAAU,EAAC,KAAK;kCAAAjB,QAAA,EACzCW,UAAU,CAACmE,KAAK,GAAG,CAAC;gCAAC;kCAAA7E,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,eACb1E,OAAA,CAACpB,UAAU;kCAACoF,OAAO,EAAC,SAAS;kCAACyB,KAAK,EAAC,gBAAgB;kCAAAnB,QAAA,GAAC,GAClD,EAAC8E,KAAK,EAAC,KACV;gCAAA;kCAAA7E,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAEN1E,OAAA,CAACpB,UAAU;8BAACoF,OAAO,EAAC,OAAO;8BAACkB,KAAK,EAAE;gCAAEsE,UAAU,EAAE;8BAAW,CAAE;8BAAAlF,QAAA,EAC3DmD,EAAE,CAAClF,MAAM,IAAIlC,CAAC,CAAC,UAAU;4BAAC;8BAAAkE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC,GAxBwCwE,IAAI;0BAAA3E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAyB9C,CAAC;sBAEX,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA,GA7CLqE,GAAG;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8CR,CAEV,CAAC,EACAoE,YAAY,CAACzB,MAAM,GAAG,CAAC,iBACtBrH,OAAA,CAACZ,IAAI;kBAACyG,EAAE,EAAE;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,gBAClBtE,OAAA,CAACV,UAAU;oBACTuG,EAAE,EAAE;sBAAEkC,OAAO,EAAE,UAAU;sBAAEtC,KAAK,EAAE;oBAAQ,CAAE;oBAC5CyC,KAAK,eACHlI,OAAA,CAACrB,GAAG;sBAACkH,EAAE,EAAE;wBAAEV,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAAf,QAAA,gBACzDtE,OAAA,CAACpB,UAAU;wBAACiH,EAAE,EAAE;0BAAEP,QAAQ,EAAE;wBAAS,CAAE;wBAAAhB,QAAA,EAAC;sBAAE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACvD1E,OAAA,CAACpB,UAAU;wBAACoF,OAAO,EAAC,IAAI;wBAAAM,QAAA,EAAEjE,CAAC,CAAC,cAAc;sBAAC;wBAAAkE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACF1E,OAAA,CAACX,WAAW;oBAAAiF,QAAA,eACVtE,OAAA,CAACT,IAAI;sBAACyJ,SAAS;sBAACC,OAAO,EAAE,CAAE;sBAAA3E,QAAA,EACxBwE,YAAY,CAACxH,GAAG,CAAC,CAACmG,EAAE,EAAEyB,IAAI,KAAK;wBAC9B,IAAIC,SAAS,GAAG,CAACxB,KAAK,CAAC/F,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,CAAC,IAAIX,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,IAAI,CAAC,IAAIX,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,IAAI,CAAC;wBAC7F,IAAI6G,KAAK,GAAGD,SAAS,GAAGvH,MAAM,CAAC6F,EAAE,CAAClF,MAAM,CAAC,GAAG,IAAI;wBAChD,oBACEvC,OAAA,CAACT,IAAI;0BAAC8J,IAAI;0BAACC,EAAE,EAAE,EAAG;0BAACC,EAAE,EAAEJ,SAAS,GAAG,CAAC,GAAG,EAAG;0BAAA7E,QAAA,eACxCtE,OAAA,CAACrB,GAAG;4BAACkH,EAAE,EAAE;8BAAEF,CAAC,EAAE,CAAC;8BAAEoC,OAAO,EAAE,SAAS;8BAAEnB,YAAY,EAAE;4BAAE,CAAE;4BAAAtC,QAAA,gBACrDtE,OAAA,CAACpB,UAAU;8BAACoF,OAAO,EAAC,OAAO;8BAACuB,UAAU,EAAC,KAAK;8BAACyC,YAAY;8BAAA1D,QAAA,EACtDmD,EAAE,CAACnF;4BAAQ;8BAAAiC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,EACZyE,SAAS,gBACRnJ,OAAA,CAACrB,GAAG;8BAACkH,EAAE,EAAE;gCAAEV,OAAO,EAAE,MAAM;gCAAEC,UAAU,EAAE,QAAQ;gCAAEC,GAAG,EAAE;8BAAE,CAAE;8BAAAf,QAAA,gBACzDtE,OAAA,CAACpB,UAAU;gCAACiH,EAAE,EAAE;kCAAEP,QAAQ,EAAE;gCAAS,CAAE;gCAAAhB,QAAA,EACpCU,UAAU,CAACoE,KAAK,GAAG,CAAC;8BAAC;gCAAA7E,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ,CAAC,eACb1E,OAAA,CAACrB,GAAG;gCAAA2F,QAAA,gBACFtE,OAAA,CAACpB,UAAU;kCAACoF,OAAO,EAAC,OAAO;kCAACuB,UAAU,EAAC,KAAK;kCAAAjB,QAAA,EACzCW,UAAU,CAACmE,KAAK,GAAG,CAAC;gCAAC;kCAAA7E,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACZ,CAAC,eACb1E,OAAA,CAACpB,UAAU;kCAACoF,OAAO,EAAC,SAAS;kCAACyB,KAAK,EAAC,gBAAgB;kCAAAnB,QAAA,GAAC,GAClD,EAAC8E,KAAK,EAAC,KACV;gCAAA;kCAAA7E,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAEN1E,OAAA,CAACpB,UAAU;8BAACoF,OAAO,EAAC,OAAO;8BAACkB,KAAK,EAAE;gCAAEsE,UAAU,EAAE;8BAAW,CAAE;8BAAAlF,QAAA,EAC3DmD,EAAE,CAAClF,MAAM,IAAIlC,CAAC,CAAC,UAAU;4BAAC;8BAAAkE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC,GAxBwCwE,IAAI;0BAAA3E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAyB9C,CAAC;sBAEX,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACP;cAAA,eACG,CAAC;YAEL,CAAC,EAAE,CAAC;UAAA,eACJ,CAAC;QAEP,CAAC,EAAE,CAAC,gBACF1E,OAAA,CAACpB,UAAU;UAAC6G,KAAK,EAAC,gBAAgB;UAACI,EAAE,EAAE;YAAEyB,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,EAAC;QAEvE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtE,EAAA,CAvYID,kBAAkB;EAAA,QACRT,cAAc,EACHhB,SAAS;AAAA;AAAA+K,EAAA,GAF9BtJ,kBAAkB;AAyYxB,eAAeA,kBAAkB;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}