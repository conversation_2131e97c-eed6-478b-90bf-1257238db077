{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\SessionList.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Typography, Paper, Box, Divider, Stack, Button, Avatar, IconButton, TextField, Chip, Dialog, DialogTitle, DialogContent, MenuItem, Select, FormControl, InputLabel } from \"@mui/material\";\nimport DeleteIcon from \"@mui/icons-material/Delete\";\nimport PersonAddAlt1Icon from \"@mui/icons-material/PersonAddAlt1\";\nimport RocketLaunchIcon from \"@mui/icons-material/RocketLaunch\";\nimport { Close, Facebook, Twitter, LinkedIn, ContentCopy, Feedback } from \"@mui/icons-material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { toast } from \"react-toastify\";\nimport { useNavigate } from \"react-router-dom\";\nimport AddSessionFeedback from './AddSessionFeedback';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SessionList = () => {\n  _s();\n  var _shareModal$session, _shareModal$session2, _shareModal$session3, _shareModal$session3$, _shareModal$session4, _shareModal$session4$, _shareModal$session5, _shareModal$session5$, _shareModal$session6, _shareModal$session6$;\n  const [showAddUserId, setShowAddUserId] = useState(null);\n  const [userEmail, setUserEmail] = useState(\"\");\n  const [addLoading, setAddLoading] = useState(false);\n  const [sessions, setSessions] = useState([]);\n  const [assignedUsersMap, setAssignedUsersMap] = useState({});\n  const [sidebarOpen, setSidebarOpen] = useState({});\n  const [shareModal, setShareModal] = useState({\n    open: false,\n    session: null\n  });\n  const [shareText, setShareText] = useState('');\n  const [openFeedbackDialog, setOpenFeedbackDialog] = useState(false);\n  const [selectedSession, setSelectedSession] = useState(null);\n  const {\n    t\n  } = useTranslation();\n  const navigate = useNavigate();\n  const fetchSessions = async () => {\n    try {\n      const res = await axios.get(\"http://localhost:8000/session2\");\n      setSessions(res.data);\n      const usersMap = {};\n      await Promise.all(res.data.map(async session => {\n        try {\n          const resp = await axios.get(`http://localhost:8000/session2/${session.id}/users`);\n          usersMap[session.id] = resp.data || [];\n        } catch {\n          usersMap[session.id] = [];\n        }\n      }));\n      setAssignedUsersMap(usersMap);\n    } catch {\n      toast.error(t(\"sessions.loadError\"));\n    }\n  };\n  useEffect(() => {\n    fetchSessions();\n  }, [fetchSessions]);\n  const handleDelete = async id => {\n    try {\n      await axios.delete(`http://localhost:8000/session2/${id}`);\n      toast.success(t(\"sessions.deleteSuccess\"));\n      fetchSessions();\n    } catch {\n      toast.error(t(\"sessions.deleteError\"));\n    }\n  };\n  const handleStatusChange = async (sessionId, newStatus) => {\n    try {\n      await axios.patch(`http://localhost:8000/session2/${sessionId}/status`, {\n        status: newStatus\n      });\n      toast.success(t(\"sessions.statusUpdated\"));\n      fetchSessions();\n    } catch {\n      toast.error(t(\"sessions.statusUpdateError\"));\n    }\n  };\n  const handleRemoveUser = async (sessionId, userId) => {\n    try {\n      await axios.delete(`http://localhost:8000/session2/${sessionId}/remove-user/${userId}`);\n      toast.success(t(\"sessions.userRemoved\"));\n      await fetchSessions();\n    } catch (e) {\n      var _e$response, _e$response$data;\n      toast.error(((_e$response = e.response) === null || _e$response === void 0 ? void 0 : (_e$response$data = _e$response.data) === null || _e$response$data === void 0 ? void 0 : _e$response$data.message) || t(\"sessions.removeUserError\"));\n    }\n  };\n  const handleAddUser = async sessionId => {\n    if (!userEmail) {\n      toast.error(t(\"sessions.enterEmail\"));\n      return;\n    }\n    setAddLoading(true);\n    try {\n      await axios.post(`http://localhost:8000/session2/${sessionId}/add-user`, {\n        email: userEmail\n      });\n      toast.success(t(\"sessions.userAdded\"));\n      setShowAddUserId(null);\n      setUserEmail(\"\");\n      await fetchSessions();\n    } catch (e) {\n      var _e$response2, _e$response2$data;\n      toast.error(((_e$response2 = e.response) === null || _e$response2 === void 0 ? void 0 : (_e$response2$data = _e$response2.data) === null || _e$response2$data === void 0 ? void 0 : _e$response2$data.message) || t(\"sessions.addUserError\"));\n    } finally {\n      setAddLoading(false);\n    }\n  };\n  const handleToggleSidebar = sessionId => {\n    setSidebarOpen(prev => ({\n      ...prev,\n      [sessionId]: !prev[sessionId]\n    }));\n  };\n  const handleShare = session => {\n    var _session$program, _session$startDate, _session$endDate, _session$session2Modu;\n    const text = `🌟 ${t(\"sessions.newSessionAvailable\")} 🌟\\n\\n🎯 ${session.name}\\n\\n📚 ${t(\"sessions.program\")}: ${((_session$program = session.program) === null || _session$program === void 0 ? void 0 : _session$program.name) || t(\"sessions.program\")}\\n📅 ${t(\"sessions.period\")}: ${(_session$startDate = session.startDate) === null || _session$startDate === void 0 ? void 0 : _session$startDate.slice(0, 10)} ➜ ${(_session$endDate = session.endDate) === null || _session$endDate === void 0 ? void 0 : _session$endDate.slice(0, 10)}\\n\\n${((_session$session2Modu = session.session2Modules) === null || _session$session2Modu === void 0 ? void 0 : _session$session2Modu.length) > 0 ? `🎓 ${t(\"sessions.includedModules\")}:\\n` + session.session2Modules.map(mod => {\n      var _mod$module;\n      return `✅ ${(_mod$module = mod.module) === null || _mod$module === void 0 ? void 0 : _mod$module.name}`;\n    }).join('\\n') + '\\n\\n' : ''}🚀 ${t(\"sessions.uniqueOpportunity\")}\\n\\n💡 ${t(\"sessions.registerNow\")}\\n\\n#Formation #Éducation #DéveloppementProfessionnel #Apprentissage #Compétences #LMS #Success`;\n    setShareText(text);\n    setShareModal({\n      open: true,\n      session\n    });\n  };\n  const handleSocialShare = platform => {\n    const encodedText = encodeURIComponent(shareText);\n    const urls = {\n      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,\n      twitter: `https://twitter.com/intent/tweet?text=${encodedText}`,\n      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`\n    };\n    window.open(urls[platform], '_blank', 'width=600,height=400');\n  };\n  const handleCopyText = async () => {\n    try {\n      await navigator.clipboard.writeText(shareText);\n      toast.success(t('sessions.textCopied'));\n    } catch (err) {\n      toast.error(t('sessions.copyError'));\n    }\n  };\n  const openFeedbackForm = session => {\n    setSelectedSession(session);\n    setOpenFeedbackDialog(true);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 3,\n    sx: {\n      p: 4,\n      borderRadius: 4,\n      backgroundColor: \"#fefefe\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      fontWeight: \"bold\",\n      gutterBottom: true,\n      children: [\"\\uD83D\\uDCCB \", t('sessions.sessionList')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), sessions.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      mt: 2,\n      color: \"text.secondary\",\n      children: t(\"sessions.noSessions\")\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 9\n    }, this) : sessions.map(session => {\n      var _session$program2, _session$startDate2, _session$endDate2, _session$session2Modu2;\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 1,\n        sx: {\n          mt: 4,\n          p: 3,\n          borderRadius: 3,\n          backgroundColor: \"#ffffff\",\n          border: \"1px solid #e0e0e0\",\n          display: \"flex\",\n          flexDirection: \"row\",\n          justifyContent: \"space-between\",\n          alignItems: \"flex-start\",\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          flex: 1,\n          children: [session.imageUrl && /*#__PURE__*/_jsxDEV(Box, {\n            mb: 2,\n            display: \"flex\",\n            justifyContent: \"center\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: session.imageUrl,\n              alt: \"Session\",\n              style: {\n                maxWidth: \"100%\",\n                maxHeight: 180,\n                borderRadius: 16,\n                objectFit: \"cover\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n            mb: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              color: \"primary\",\n              children: [\"\\uD83E\\uDDFE \", session.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 2,\n              alignItems: \"center\",\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: session.status,\n                color: session.status === \"ACTIVE\" ? \"success\" : session.status === \"INACTIVE\" ? \"default\" : session.status === \"COMPLETED\" ? \"primary\" : \"secondary\",\n                sx: {\n                  fontWeight: 700,\n                  textTransform: \"capitalize\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                size: \"small\",\n                sx: {\n                  minWidth: 120\n                },\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: `status-label-${session.id}`,\n                  children: t(\"sessions.status\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: `status-label-${session.id}`,\n                  value: session.status,\n                  label: t(\"sessions.status\"),\n                  onChange: e => handleStatusChange(session.id, e.target.value),\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ACTIVE\",\n                    children: t(\"sessions.active\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"INACTIVE\",\n                    children: t(\"sessions.inactive\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"COMPLETED\",\n                    children: t(\"sessions.completed\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"ARCHIVED\",\n                    children: t(\"sessions.archived\")\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), !sidebarOpen[session.id] && /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            alignItems: \"center\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"error\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleDelete(session.id),\n              children: t(\"sessions.delete\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(RocketLaunchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 32\n              }, this),\n              onClick: () => navigate(`/sessions/${session.id}/seances`),\n              children: t(\"sessions.join\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(PersonAddAlt1Icon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleToggleSidebar(session.id),\n              children: t(\"sessions.addUser\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              size: \"small\",\n              onClick: () => handleShare(session),\n              children: [\"\\uD83D\\uDCE4 \", t(\"sessions.share\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              size: \"small\",\n              onClick: () => navigate(`/sessions/${session.id}/feedbacklist`),\n              children: [\"\\uD83D\\uDCCB \", t(\"sessions.sessionFeedbackList\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"info\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(Feedback, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 32\n              }, this),\n              onClick: () => openFeedbackForm(session),\n              children: [\"\\uD83D\\uDCDD \", t(\"sessions.feedback\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 17\n          }, this), showAddUserId === session.id && /*#__PURE__*/_jsxDEV(Box, {\n            mt: 2,\n            mb: 2,\n            display: \"flex\",\n            gap: 1,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              type: \"email\",\n              placeholder: t(\"sessions.userEmailPlaceholder\"),\n              value: userEmail,\n              onChange: e => setUserEmail(e.target.value),\n              size: \"small\",\n              sx: {\n                minWidth: 220\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"small\",\n              color: \"secondary\",\n              onClick: () => handleAddUser(session.id),\n              disabled: addLoading,\n              children: addLoading ? t(\"sessions.adding\") : t(\"sessions.add\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              size: \"small\",\n              onClick: () => {\n                setShowAddUserId(null);\n                setUserEmail(\"\");\n              },\n              children: t(\"sessions.cancel\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            mb: 0.5,\n            children: [\"\\uD83D\\uDCDA \", t(\"sessions.program\"), \" : \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: ((_session$program2 = session.program) === null || _session$program2 === void 0 ? void 0 : _session$program2.name) || t(\"sessions.unknown\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"\\uD83D\\uDCC5 \", t(\"sessions.period\"), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: (_session$startDate2 = session.startDate) === null || _session$startDate2 === void 0 ? void 0 : _session$startDate2.slice(0, 10)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 43\n            }, this), \" \", t(\"sessions.to\"), \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: (_session$endDate2 = session.endDate) === null || _session$endDate2 === void 0 ? void 0 : _session$endDate2.slice(0, 10)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this), ((_session$session2Modu2 = session.session2Modules) === null || _session$session2Modu2 === void 0 ? void 0 : _session$session2Modu2.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              fontWeight: \"bold\",\n              children: [\"\\uD83E\\uDDF1 \", t(\"sessions.modulesContent\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this), session.session2Modules.map(mod => {\n              var _mod$module2, _mod$courses;\n              return /*#__PURE__*/_jsxDEV(Box, {\n                mt: 1,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: \"bold\",\n                  color: \"secondary.main\",\n                  children: [\"\\uD83D\\uDCE6 \", (_mod$module2 = mod.module) === null || _mod$module2 === void 0 ? void 0 : _mod$module2.name]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 23\n                }, this), (_mod$courses = mod.courses) === null || _mod$courses === void 0 ? void 0 : _mod$courses.map(c => {\n                  var _c$course, _c$contenus;\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    ml: 2,\n                    mt: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      color: \"text.primary\",\n                      children: [\"\\uD83D\\uDCD8 \", (_c$course = c.course) === null || _c$course === void 0 ? void 0 : _c$course.title]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 358,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      spacing: 1,\n                      mt: 1,\n                      flexWrap: \"wrap\",\n                      children: (_c$contenus = c.contenus) === null || _c$contenus === void 0 ? void 0 : _c$contenus.map(ct => {\n                        var _ct$contenu2;\n                        return /*#__PURE__*/_jsxDEV(Button, {\n                          variant: \"outlined\",\n                          color: \"info\",\n                          size: \"small\",\n                          sx: {\n                            mr: 1,\n                            mb: 1,\n                            borderRadius: 2\n                          },\n                          onClick: () => {\n                            var _ct$contenu;\n                            return ((_ct$contenu = ct.contenu) === null || _ct$contenu === void 0 ? void 0 : _ct$contenu.fileUrl) && window.open(ct.contenu.fileUrl, \"_blank\");\n                          },\n                          children: [\"\\uD83D\\uDCC4 \", (_ct$contenu2 = ct.contenu) === null || _ct$contenu2 === void 0 ? void 0 : _ct$contenu2.title]\n                        }, ct.id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 27\n                    }, this)]\n                  }, c.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 25\n                  }, this);\n                })]\n              }, mod.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this);\n            })]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this), sidebarOpen[session.id] && /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 4,\n          sx: {\n            minWidth: 350,\n            maxWidth: 400,\n            bgcolor: \"#f8fbff\",\n            borderRadius: 4,\n            p: 3,\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"stretch\",\n            boxShadow: \"0 12px 36px 0 rgba(25, 118, 210, 0.09)\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            mb: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              fontWeight: 700,\n              color: \"primary\",\n              fontSize: 18,\n              children: t(\"sessions.members\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"text\",\n              color: \"primary\",\n              onClick: () => handleToggleSidebar(session.id),\n              sx: {\n                fontWeight: 600,\n                fontSize: 14,\n                textTransform: \"none\"\n              },\n              children: t(\"sessions.hide\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            mb: 3,\n            bgcolor: \"#eaf0f9\",\n            borderRadius: 2,\n            p: 1.5,\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              type: \"email\",\n              placeholder: t(\"sessions.addByEmail\"),\n              value: showAddUserId === session.id ? userEmail : \"\",\n              onFocus: () => setShowAddUserId(session.id),\n              onChange: e => setUserEmail(e.target.value),\n              variant: \"outlined\",\n              sx: {\n                flex: 1,\n                bgcolor: \"#fff\",\n                borderRadius: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              color: \"primary\",\n              disabled: addLoading,\n              onClick: () => handleAddUser(session.id),\n              sx: {\n                bgcolor: \"#1976d2\",\n                color: \"#fff\",\n                \"&:hover\": {\n                  bgcolor: \"#1565c0\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(PersonAddAlt1Icon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 2,\n            children: (assignedUsersMap[session.id] || []).length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              fontSize: 14,\n              children: t(\"sessions.noUsers\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 21\n            }, this) : assignedUsersMap[session.id].map(user => /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              p: 2,\n              bgcolor: \"#fff\",\n              borderRadius: 2,\n              sx: {\n                boxShadow: \"0 2px 8px rgba(25,118,210,.04)\",\n                cursor: \"pointer\",\n                transition: \"background .15s\",\n                \"&:hover\": {\n                  background: \"#f0f6ff\"\n                }\n              },\n              onClick: () => navigate(`/ProfilePage/${user.id}`),\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: user.profilePic || undefined,\n                sx: {\n                  width: 38,\n                  height: 38,\n                  fontWeight: 700,\n                  fontSize: 16,\n                  bgcolor: user.profilePic ? \"transparent\" : \"#B5C7D3\"\n                },\n                children: !user.profilePic && user.name ? user.name[0].toUpperCase() : null\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  fontWeight: 600,\n                  fontSize: 14,\n                  color: \"#222\",\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  fontSize: 12,\n                  color: \"#999\",\n                  children: user.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                color: \"error\",\n                onClick: e => {\n                  e.stopPropagation();\n                  handleRemoveUser(session.id, user.id);\n                },\n                children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 25\n              }, this)]\n            }, user.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 15\n        }, this)]\n      }, session.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this);\n    }), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: shareModal.open,\n      onClose: () => setShareModal({\n        open: false,\n        session: null\n      }),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"\\uD83D\\uDCE4 \", t(\"sessions.shareSession\"), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setShareModal({\n            open: false,\n            session: null\n          }),\n          sx: {\n            position: 'absolute',\n            right: 8,\n            top: 8\n          },\n          children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          id: \"session-preview\",\n          sx: {\n            borderRadius: 4,\n            overflow: \"hidden\",\n            bgcolor: \"#ffffff\",\n            border: \"2px solid #1976d2\",\n            boxShadow: 3,\n            mb: 3,\n            maxWidth: 800,\n            mx: \"auto\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              background: \"linear-gradient(90deg, #1976d2, #42a5f5)\",\n              color: \"#fff\",\n              p: 3,\n              textAlign: \"center\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: \"bold\",\n              children: [\"\\uD83C\\uDF93 \", (_shareModal$session = shareModal.session) === null || _shareModal$session === void 0 ? void 0 : _shareModal$session.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              children: [\"\\uD83D\\uDE80 \", t(\"sessions.newOpportunity\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), ((_shareModal$session2 = shareModal.session) === null || _shareModal$session2 === void 0 ? void 0 : _shareModal$session2.imageUrl) && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: \"flex\",\n              justifyContent: \"center\",\n              backgroundColor: \"#e3f2fd\",\n              p: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: shareModal.session.imageUrl,\n              alt: \"Session\",\n              style: {\n                maxWidth: \"100%\",\n                maxHeight: 240,\n                borderRadius: 12,\n                objectFit: \"cover\",\n                boxShadow: \"0 4px 16px rgba(0,0,0,0.15)\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: 16,\n              mb: 1,\n              children: [\"\\uD83D\\uDCDA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: t(\"sessions.program\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 20\n              }, this), \" : \", (_shareModal$session3 = shareModal.session) === null || _shareModal$session3 === void 0 ? void 0 : (_shareModal$session3$ = _shareModal$session3.program) === null || _shareModal$session3$ === void 0 ? void 0 : _shareModal$session3$.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: 16,\n              mb: 2,\n              children: [\"\\uD83D\\uDCC5 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: t(\"sessions.period\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 20\n              }, this), \" :\", \" \", (_shareModal$session4 = shareModal.session) === null || _shareModal$session4 === void 0 ? void 0 : (_shareModal$session4$ = _shareModal$session4.startDate) === null || _shareModal$session4$ === void 0 ? void 0 : _shareModal$session4$.slice(0, 10), \" \\u279C \", (_shareModal$session5 = shareModal.session) === null || _shareModal$session5 === void 0 ? void 0 : (_shareModal$session5$ = _shareModal$session5.endDate) === null || _shareModal$session5$ === void 0 ? void 0 : _shareModal$session5$.slice(0, 10)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), ((_shareModal$session6 = shareModal.session) === null || _shareModal$session6 === void 0 ? void 0 : (_shareModal$session6$ = _shareModal$session6.session2Modules) === null || _shareModal$session6$ === void 0 ? void 0 : _shareModal$session6$.length) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                fontWeight: \"bold\",\n                fontSize: 16,\n                mb: 1,\n                children: [\"\\uD83E\\uDDF1 \", t(\"sessions.modulesContent\")]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  paddingLeft: 20\n                },\n                children: shareModal.session.session2Modules.map(mod => {\n                  var _mod$module3;\n                  return /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      fontSize: 14,\n                      children: [\"\\u2705 \", (_mod$module3 = mod.module) === null || _mod$module3 === void 0 ? void 0 : _mod$module3.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 25\n                    }, this)\n                  }, mod.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Typography, {\n              fontSize: 14,\n              mt: 3,\n              color: \"text.secondary\",\n              children: \"#Formation #\\xC9ducation #LMS #Apprentissage #Succ\\xE8s\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          multiline: true,\n          rows: 8,\n          fullWidth: true,\n          value: shareText,\n          onChange: e => setShareText(e.target.value),\n          variant: \"outlined\",\n          label: `📝 ${t(\"sessions.customizePost\")}`,\n          sx: {\n            mb: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          spacing: 2,\n          flexWrap: \"wrap\",\n          gap: 1,\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(Facebook, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 52\n            }, this),\n            onClick: () => handleSocialShare('facebook'),\n            sx: {\n              bgcolor: '#1877f2'\n            },\n            children: \"Facebook\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(Twitter, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 52\n            }, this),\n            onClick: () => handleSocialShare('twitter'),\n            sx: {\n              bgcolor: '#1da1f2'\n            },\n            children: \"Twitter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(LinkedIn, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 52\n            }, this),\n            onClick: () => handleSocialShare('linkedin'),\n            sx: {\n              bgcolor: '#0077b5'\n            },\n            children: \"LinkedIn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(ContentCopy, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 51\n            }, this),\n            onClick: handleCopyText,\n            children: [\"\\uD83D\\uDCCB \", t(\"sessions.copyText\")]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AddSessionFeedback, {\n      open: openFeedbackDialog,\n      onClose: () => setOpenFeedbackDialog(false),\n      session: selectedSession\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(SessionList, \"IvV6yENrlKDYIn3y5iUNE6f428U=\", false, function () {\n  return [useTranslation, useNavigate];\n});\n_c = SessionList;\nexport default SessionList;\nvar _c;\n$RefreshReg$(_c, \"SessionList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Typography", "Paper", "Box", "Divider", "<PERSON><PERSON>", "<PERSON><PERSON>", "Avatar", "IconButton", "TextField", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MenuItem", "Select", "FormControl", "InputLabel", "DeleteIcon", "PersonAddAlt1Icon", "RocketLaunchIcon", "Close", "Facebook", "Twitter", "LinkedIn", "ContentCopy", "<PERSON><PERSON><PERSON>", "useTranslation", "axios", "toast", "useNavigate", "AddSessionFeedback", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SessionList", "_s", "_shareModal$session", "_shareModal$session2", "_shareModal$session3", "_shareModal$session3$", "_shareModal$session4", "_shareModal$session4$", "_shareModal$session5", "_shareModal$session5$", "_shareModal$session6", "_shareModal$session6$", "showAddUserId", "setShowAddUserId", "userEmail", "setUserEmail", "addLoading", "setAddLoading", "sessions", "setSessions", "assignedUsersMap", "setAssignedUsersMap", "sidebarOpen", "setSidebarOpen", "shareModal", "setShareModal", "open", "session", "shareText", "setShareText", "openFeedbackDialog", "setOpenFeedbackDialog", "selectedSession", "setSelectedSession", "t", "navigate", "fetchSessions", "res", "get", "data", "usersMap", "Promise", "all", "map", "resp", "id", "error", "handleDelete", "delete", "success", "handleStatusChange", "sessionId", "newStatus", "patch", "status", "handleRemoveUser", "userId", "e", "_e$response", "_e$response$data", "response", "message", "handleAddUser", "post", "email", "_e$response2", "_e$response2$data", "handleToggleSidebar", "prev", "handleShare", "_session$program", "_session$startDate", "_session$endDate", "_session$session2Modu", "text", "name", "program", "startDate", "slice", "endDate", "session2Modules", "length", "mod", "_mod$module", "module", "join", "handleSocialShare", "platform", "encodedText", "encodeURIComponent", "urls", "facebook", "window", "location", "href", "twitter", "linkedin", "handleCopyText", "navigator", "clipboard", "writeText", "err", "openFeedbackForm", "elevation", "sx", "p", "borderRadius", "backgroundColor", "children", "variant", "fontWeight", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mt", "color", "_session$program2", "_session$startDate2", "_session$endDate2", "_session$session2Modu2", "border", "display", "flexDirection", "justifyContent", "alignItems", "gap", "flex", "imageUrl", "mb", "src", "alt", "style", "max<PERSON><PERSON><PERSON>", "maxHeight", "objectFit", "direction", "spacing", "label", "textTransform", "size", "min<PERSON><PERSON><PERSON>", "labelId", "value", "onChange", "target", "startIcon", "onClick", "type", "placeholder", "disabled", "my", "_mod$module2", "_mod$courses", "courses", "c", "_c$course", "_c$contenus", "ml", "course", "title", "flexWrap", "contenus", "ct", "_ct$contenu2", "mr", "_ct$contenu", "contenu", "fileUrl", "bgcolor", "boxShadow", "fontSize", "onFocus", "user", "cursor", "transition", "background", "profilePic", "undefined", "width", "height", "toUpperCase", "role", "stopPropagation", "onClose", "fullWidth", "position", "right", "top", "overflow", "mx", "textAlign", "paddingLeft", "_mod$module3", "multiline", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/SessionList.js"], "sourcesContent": [" import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Typo<PERSON>,\r\n  Paper,\r\n  Box,\r\n  Divider,\r\n  Stack,\r\n  Button,\r\n  Avatar,\r\n  IconButton,\r\n  TextField,\r\n  Chip,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  MenuItem,\r\n  Select,\r\n  FormControl,\r\n  InputLabel,\r\n} from \"@mui/material\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport PersonAddAlt1Icon from \"@mui/icons-material/PersonAddAlt1\";\r\nimport RocketLaunchIcon from \"@mui/icons-material/RocketLaunch\";\r\nimport { Close, Facebook, Twitter, LinkedIn, ContentCopy, Feedback } from \"@mui/icons-material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport AddSessionFeedback from './AddSessionFeedback';\r\n\r\nconst SessionList = () => {\r\n  const [showAddUserId, setShowAddUserId] = useState(null);\r\n  const [userEmail, setUserEmail] = useState(\"\");\r\n  const [addLoading, setAddLoading] = useState(false);\r\n  const [sessions, setSessions] = useState([]);\r\n  const [assignedUsersMap, setAssignedUsersMap] = useState({});\r\n  const [sidebarOpen, setSidebarOpen] = useState({});\r\n  const [shareModal, setShareModal] = useState({ open: false, session: null });\r\n  const [shareText, setShareText] = useState('');\r\n  const [openFeedbackDialog, setOpenFeedbackDialog] = useState(false);\r\n  const [selectedSession, setSelectedSession] = useState(null);\r\n  const { t } = useTranslation();\r\n  const navigate = useNavigate();\r\n\r\n  const fetchSessions = async () => {\r\n    try {\r\n      const res = await axios.get(\"http://localhost:8000/session2\");\r\n      setSessions(res.data);\r\n      const usersMap = {};\r\n      await Promise.all(\r\n        res.data.map(async (session) => {\r\n          try {\r\n            const resp = await axios.get(`http://localhost:8000/session2/${session.id}/users`);\r\n            usersMap[session.id] = resp.data || [];\r\n          } catch {\r\n            usersMap[session.id] = [];\r\n          }\r\n        })\r\n      );\r\n      setAssignedUsersMap(usersMap);\r\n    } catch {\r\n      toast.error(t(\"sessions.loadError\"));\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchSessions();\r\n  }, [fetchSessions]);\r\n\r\n  const handleDelete = async (id) => {\r\n    try {\r\n      await axios.delete(`http://localhost:8000/session2/${id}`);\r\n      toast.success(t(\"sessions.deleteSuccess\"));\r\n      fetchSessions();\r\n    } catch {\r\n      toast.error(t(\"sessions.deleteError\"));\r\n    }\r\n  };\r\n\r\n  const handleStatusChange = async (sessionId, newStatus) => {\r\n    try {\r\n      await axios.patch(`http://localhost:8000/session2/${sessionId}/status`, {\r\n        status: newStatus,\r\n      });\r\n      toast.success(t(\"sessions.statusUpdated\"));\r\n      fetchSessions();\r\n    } catch {\r\n      toast.error(t(\"sessions.statusUpdateError\"));\r\n    }\r\n  };\r\n\r\n  const handleRemoveUser = async (sessionId, userId) => {\r\n    try {\r\n      await axios.delete(`http://localhost:8000/session2/${sessionId}/remove-user/${userId}`);\r\n      toast.success(t(\"sessions.userRemoved\"));\r\n      await fetchSessions();\r\n    } catch (e) {\r\n      toast.error(\r\n        e.response?.data?.message || t(\"sessions.removeUserError\")\r\n      );\r\n    }\r\n  };\r\n\r\n  const handleAddUser = async (sessionId) => {\r\n    if (!userEmail) {\r\n      toast.error(t(\"sessions.enterEmail\"));\r\n      return;\r\n    }\r\n    setAddLoading(true);\r\n    try {\r\n      await axios.post(`http://localhost:8000/session2/${sessionId}/add-user`, {\r\n        email: userEmail,\r\n      });\r\n      toast.success(t(\"sessions.userAdded\"));\r\n      setShowAddUserId(null);\r\n      setUserEmail(\"\");\r\n      await fetchSessions();\r\n    } catch (e) {\r\n      toast.error(\r\n        e.response?.data?.message || t(\"sessions.addUserError\")\r\n      );\r\n    } finally {\r\n      setAddLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleToggleSidebar = (sessionId) => {\r\n    setSidebarOpen(prev => ({\r\n      ...prev,\r\n      [sessionId]: !prev[sessionId]\r\n    }));\r\n  };\r\n\r\n  const handleShare = (session) => {\r\n    const text = `🌟 ${t(\"sessions.newSessionAvailable\")} 🌟\\n\\n🎯 ${session.name}\\n\\n📚 ${t(\"sessions.program\")}: ${session.program?.name || t(\"sessions.program\")}\\n📅 ${t(\"sessions.period\")}: ${session.startDate?.slice(0, 10)} ➜ ${session.endDate?.slice(0, 10)}\\n\\n${session.session2Modules?.length > 0 ? `🎓 ${t(\"sessions.includedModules\")}:\\n` + session.session2Modules.map(mod => `✅ ${mod.module?.name}`).join('\\n') + '\\n\\n' : ''}🚀 ${t(\"sessions.uniqueOpportunity\")}\\n\\n💡 ${t(\"sessions.registerNow\")}\\n\\n#Formation #Éducation #DéveloppementProfessionnel #Apprentissage #Compétences #LMS #Success`;\r\n    setShareText(text);\r\n    setShareModal({ open: true, session });\r\n  };\r\n\r\n  const handleSocialShare = (platform) => {\r\n    const encodedText = encodeURIComponent(shareText);\r\n    const urls = {\r\n      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,\r\n      twitter: `https://twitter.com/intent/tweet?text=${encodedText}`,\r\n      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`\r\n    };\r\n    window.open(urls[platform], '_blank', 'width=600,height=400');\r\n  };\r\n\r\n  const handleCopyText = async () => {\r\n    try {\r\n      await navigator.clipboard.writeText(shareText);\r\n      toast.success(t('sessions.textCopied'));\r\n    } catch (err) {\r\n      toast.error(t('sessions.copyError'));\r\n    }\r\n  };\r\n\r\n  const openFeedbackForm = (session) => {\r\n    setSelectedSession(session);\r\n    setOpenFeedbackDialog(true);\r\n  };\r\n\r\n  return (\r\n    <Paper elevation={3} sx={{ p: 4, borderRadius: 4, backgroundColor: \"#fefefe\" }}>\r\n      <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom>\r\n        📋 {t('sessions.sessionList')}\r\n      </Typography>\r\n\r\n      {sessions.length === 0 ? (\r\n        <Typography mt={2} color=\"text.secondary\">\r\n          {t(\"sessions.noSessions\")}\r\n        </Typography>\r\n      ) : (\r\n        sessions.map((session) => (\r\n          <Paper\r\n            key={session.id}\r\n            elevation={1}\r\n            sx={{\r\n              mt: 4,\r\n              p: 3,\r\n              borderRadius: 3,\r\n              backgroundColor: \"#ffffff\",\r\n              border: \"1px solid #e0e0e0\",\r\n              display: \"flex\",\r\n              flexDirection: \"row\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"flex-start\",\r\n              gap: 3\r\n            }}\r\n          >\r\n            {/* Main Content */}\r\n            <Box flex={1}>\r\n              {session.imageUrl && (\r\n                <Box mb={2} display=\"flex\" justifyContent=\"center\">\r\n                  <img\r\n                    src={session.imageUrl}\r\n                    alt=\"Session\"\r\n                    style={{\r\n                      maxWidth: \"100%\",\r\n                      maxHeight: 180,\r\n                      borderRadius: 16,\r\n                      objectFit: \"cover\",\r\n                    }}\r\n                  />\r\n                </Box>\r\n              )}\r\n\r\n              <Stack\r\n                direction=\"row\"\r\n                justifyContent=\"space-between\"\r\n                alignItems=\"center\"\r\n                mb={1}\r\n              >\r\n                <Typography variant=\"h6\" fontWeight=\"bold\" color=\"primary\">\r\n                  🧾 {session.name}\r\n                </Typography>\r\n                <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n                  <Chip\r\n                    label={session.status}\r\n                    color={\r\n                      session.status === \"ACTIVE\"\r\n                        ? \"success\"\r\n                        : session.status === \"INACTIVE\"\r\n                        ? \"default\"\r\n                        : session.status === \"COMPLETED\"\r\n                        ? \"primary\"\r\n                        : \"secondary\"\r\n                    }\r\n                    sx={{ fontWeight: 700, textTransform: \"capitalize\" }}\r\n                  />\r\n                  <FormControl size=\"small\" sx={{ minWidth: 120 }}>\r\n                    <InputLabel id={`status-label-${session.id}`}>{t(\"sessions.status\")}</InputLabel>\r\n                    <Select\r\n                      labelId={`status-label-${session.id}`}\r\n                      value={session.status}\r\n                      label={t(\"sessions.status\")}\r\n                      onChange={e => handleStatusChange(session.id, e.target.value)}\r\n                    >\r\n                      <MenuItem value=\"ACTIVE\">{t(\"sessions.active\")}</MenuItem>\r\n                      <MenuItem value=\"INACTIVE\">{t(\"sessions.inactive\")}</MenuItem>\r\n                      <MenuItem value=\"COMPLETED\">{t(\"sessions.completed\")}</MenuItem>\r\n                      <MenuItem value=\"ARCHIVED\">{t(\"sessions.archived\")}</MenuItem>\r\n                    </Select>\r\n                  </FormControl>\r\n                </Stack>\r\n              </Stack>\r\n\r\n              {!sidebarOpen[session.id] && (\r\n                <Stack direction=\"row\" spacing={1} alignItems=\"center\" mb={2}>\r\n                  <Button\r\n                    variant=\"outlined\"\r\n                    color=\"error\"\r\n                    size=\"small\"\r\n                    startIcon={<DeleteIcon />}\r\n                    onClick={() => handleDelete(session.id)}\r\n                  >\r\n                    {t(\"sessions.delete\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"small\"\r\n                    startIcon={<RocketLaunchIcon />}\r\n                    onClick={() => navigate(`/sessions/${session.id}/seances`)}\r\n                  >\r\n                    {t(\"sessions.join\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"small\"\r\n                    startIcon={<PersonAddAlt1Icon />}\r\n                    onClick={() => handleToggleSidebar(session.id)}\r\n                  >\r\n                    {t(\"sessions.addUser\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"outlined\"\r\n                    color=\"secondary\"\r\n                    size=\"small\"\r\n                    onClick={() => handleShare(session)}\r\n                  >\r\n                    📤 {t(\"sessions.share\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"primary\"\r\n                    size=\"small\"\r\n                    onClick={() => navigate(`/sessions/${session.id}/feedbacklist`)}\r\n                  >\r\n                    📋 {t(\"sessions.sessionFeedbackList\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"info\"\r\n                    size=\"small\"\r\n                    startIcon={<Feedback />}\r\n                    onClick={() => openFeedbackForm(session)}\r\n                  >\r\n                    📝 {t(\"sessions.feedback\")}\r\n                  </Button>\r\n                </Stack>\r\n              )}\r\n\r\n              {/* Add User Section */}\r\n              {showAddUserId === session.id && (\r\n                <Box mt={2} mb={2} display=\"flex\" gap={1} alignItems=\"center\">\r\n                  <TextField\r\n                    type=\"email\"\r\n                    placeholder={t(\"sessions.userEmailPlaceholder\")}\r\n                    value={userEmail}\r\n                    onChange={e => setUserEmail(e.target.value)}\r\n                    size=\"small\"\r\n                    sx={{ minWidth: 220 }}\r\n                  />\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    size=\"small\"\r\n                    color=\"secondary\"\r\n                    onClick={() => handleAddUser(session.id)}\r\n                    disabled={addLoading}\r\n                  >\r\n                    {addLoading ? t(\"sessions.adding\") : t(\"sessions.add\")}\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"text\"\r\n                    size=\"small\"\r\n                    onClick={() => { setShowAddUserId(null); setUserEmail(\"\"); }}\r\n                  >\r\n                    {t(\"sessions.cancel\")}\r\n                  </Button>\r\n                </Box>\r\n              )}\r\n\r\n              <Typography variant=\"body2\" mb={0.5}>\r\n                📚 {t(\"sessions.program\")} : <strong>{session.program?.name || t(\"sessions.unknown\")}</strong>\r\n              </Typography>\r\n              <Typography variant=\"body2\">\r\n                📅 {t(\"sessions.period\")} <strong>{session.startDate?.slice(0, 10)}</strong> {t(\"sessions.to\")}{\" \"}\r\n                <strong>{session.endDate?.slice(0, 10)}</strong>\r\n              </Typography>\r\n\r\n              {/* Modules and Contents */}\r\n              {session.session2Modules?.length > 0 && (\r\n                <>\r\n                  <Divider sx={{ my: 2 }} />\r\n                  <Typography variant=\"subtitle1\" fontWeight=\"bold\">\r\n                    🧱 {t(\"sessions.modulesContent\")}\r\n                  </Typography>\r\n                  {session.session2Modules.map((mod) => (\r\n                    <Box key={mod.id} mt={1}>\r\n                      <Typography fontWeight=\"bold\" color=\"secondary.main\">\r\n                        📦 {mod.module?.name}\r\n                      </Typography>\r\n                      {mod.courses?.map((c) => (\r\n                        <Box key={c.id} ml={2} mt={1}>\r\n                          <Typography variant=\"body2\" fontWeight=\"bold\" color=\"text.primary\">\r\n                            📘 {c.course?.title}\r\n                          </Typography>\r\n                          <Stack direction=\"row\" spacing={1} mt={1} flexWrap=\"wrap\">\r\n                            {c.contenus?.map((ct) => (\r\n                              <Button\r\n                                key={ct.id}\r\n                                variant=\"outlined\"\r\n                                color=\"info\"\r\n                                size=\"small\"\r\n                                sx={{ mr: 1, mb: 1, borderRadius: 2 }}\r\n                                onClick={() =>\r\n                                  ct.contenu?.fileUrl &&\r\n                                  window.open(ct.contenu.fileUrl, \"_blank\")\r\n                                }\r\n                              >\r\n                                📄 {ct.contenu?.title}\r\n                              </Button>\r\n                            ))}\r\n                          </Stack>\r\n                        </Box>\r\n                      ))}\r\n                    </Box>\r\n                  ))}\r\n                </>\r\n              )}\r\n            </Box>\r\n\r\n            {/* Sidebar for Users */}\r\n            {sidebarOpen[session.id] && (\r\n              <Paper\r\n                elevation={4}\r\n                sx={{\r\n                  minWidth: 350,\r\n                  maxWidth: 400,\r\n                  bgcolor: \"#f8fbff\",\r\n                  borderRadius: 4,\r\n                  p: 3,\r\n                  display: \"flex\",\r\n                  flexDirection: \"column\",\r\n                  alignItems: \"stretch\",\r\n                  boxShadow: \"0 12px 36px 0 rgba(25, 118, 210, 0.09)\",\r\n                }}\r\n              >\r\n                <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" mb={2}>\r\n                  <Typography fontWeight={700} color=\"primary\" fontSize={18}>\r\n                    {t(\"sessions.members\")}\r\n                  </Typography>\r\n                  <Button\r\n                    variant=\"text\"\r\n                    color=\"primary\"\r\n                    onClick={() => handleToggleSidebar(session.id)}\r\n                    sx={{ fontWeight: 600, fontSize: 14, textTransform: \"none\" }}\r\n                  >\r\n                    {t(\"sessions.hide\")}\r\n                  </Button>\r\n                </Box>\r\n\r\n                {/* Add User Input */}\r\n                <Box\r\n                  display=\"flex\"\r\n                  alignItems=\"center\"\r\n                  gap={1}\r\n                  mb={3}\r\n                  bgcolor=\"#eaf0f9\"\r\n                  borderRadius={2}\r\n                  p={1.5}\r\n                >\r\n                  <TextField\r\n                    size=\"small\"\r\n                    type=\"email\"\r\n                    placeholder={t(\"sessions.addByEmail\")}\r\n                    value={showAddUserId === session.id ? userEmail : \"\"}\r\n                    onFocus={() => setShowAddUserId(session.id)}\r\n                    onChange={(e) => setUserEmail(e.target.value)}\r\n                    variant=\"outlined\"\r\n                    sx={{ flex: 1, bgcolor: \"#fff\", borderRadius: 2 }}\r\n                  />\r\n                  <IconButton\r\n                    color=\"primary\"\r\n                    disabled={addLoading}\r\n                    onClick={() => handleAddUser(session.id)}\r\n                    sx={{ bgcolor: \"#1976d2\", color: \"#fff\", \"&:hover\": { bgcolor: \"#1565c0\" } }}\r\n                  >\r\n                    <PersonAddAlt1Icon />\r\n                  </IconButton>\r\n                </Box>\r\n\r\n                {/* Users List */}\r\n                <Stack spacing={2}>\r\n                  {(assignedUsersMap[session.id] || []).length === 0 ? (\r\n                    <Typography color=\"text.secondary\" fontSize={14}>\r\n                      {t(\"sessions.noUsers\")}\r\n                    </Typography>\r\n                  ) : (\r\n                    assignedUsersMap[session.id].map((user) => (\r\n                      <Box\r\n                        key={user.id}\r\n                        display=\"flex\"\r\n                        alignItems=\"center\"\r\n                        gap={2}\r\n                        p={2}\r\n                        bgcolor=\"#fff\"\r\n                        borderRadius={2}\r\n                        sx={{\r\n                          boxShadow: \"0 2px 8px rgba(25,118,210,.04)\",\r\n                          cursor: \"pointer\",\r\n                          transition: \"background .15s\",\r\n                          \"&:hover\": { background: \"#f0f6ff\" }\r\n                        }}\r\n                        onClick={() => navigate(`/ProfilePage/${user.id}`)}\r\n                      >\r\n                        <Avatar\r\n                          src={user.profilePic || undefined}\r\n                          sx={{\r\n                            width: 38, height: 38, fontWeight: 700, fontSize: 16,\r\n                            bgcolor: user.profilePic ? \"transparent\" : \"#B5C7D3\",\r\n                          }}\r\n                        >\r\n                          {!user.profilePic && user.name ? user.name[0].toUpperCase() : null}\r\n                        </Avatar>\r\n                        <Box sx={{ flex: 1 }}>\r\n                          <Typography fontWeight={600} fontSize={14} color=\"#222\">\r\n                            {user.name}\r\n                          </Typography>\r\n                          <Typography fontSize={12} color=\"#999\">\r\n                            {user.role}\r\n                          </Typography>\r\n                        </Box>\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          color=\"error\"\r\n                          onClick={e => {\r\n                            e.stopPropagation();\r\n                            handleRemoveUser(session.id, user.id);\r\n                          }}\r\n                        >\r\n                          <DeleteIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </Box>\r\n                    ))\r\n                  )}\r\n                </Stack>\r\n              </Paper>\r\n            )}\r\n          </Paper>\r\n        ))\r\n      )}\r\n\r\n      {/* Share Modal */}\r\n      <Dialog open={shareModal.open} onClose={() => setShareModal({ open: false, session: null })} maxWidth=\"md\" fullWidth>\r\n        <DialogTitle>\r\n          📤 {t(\"sessions.shareSession\")}\r\n          <IconButton onClick={() => setShareModal({ open: false, session: null })} sx={{ position: 'absolute', right: 8, top: 8 }}>\r\n            <Close />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          {/* Session Preview Widget */}\r\n          <Box\r\n            id=\"session-preview\"\r\n            sx={{\r\n              borderRadius: 4,\r\n              overflow: \"hidden\",\r\n              bgcolor: \"#ffffff\",\r\n              border: \"2px solid #1976d2\",\r\n              boxShadow: 3,\r\n              mb: 3,\r\n              maxWidth: 800,\r\n              mx: \"auto\"\r\n            }}\r\n          >\r\n            <Box\r\n              sx={{\r\n                background: \"linear-gradient(90deg, #1976d2, #42a5f5)\",\r\n                color: \"#fff\",\r\n                p: 3,\r\n                textAlign: \"center\"\r\n              }}\r\n            >\r\n              <Typography variant=\"h5\" fontWeight=\"bold\">\r\n                🎓 {shareModal.session?.name}\r\n              </Typography>\r\n              <Typography variant=\"subtitle1\">\r\n                🚀 {t(\"sessions.newOpportunity\")}\r\n              </Typography>\r\n            </Box>\r\n\r\n            {shareModal.session?.imageUrl && (\r\n              <Box\r\n                sx={{\r\n                  display: \"flex\",\r\n                  justifyContent: \"center\",\r\n                  backgroundColor: \"#e3f2fd\",\r\n                  p: 2\r\n                }}\r\n              >\r\n                <img\r\n                  src={shareModal.session.imageUrl}\r\n                  alt=\"Session\"\r\n                  style={{\r\n                    maxWidth: \"100%\",\r\n                    maxHeight: 240,\r\n                    borderRadius: 12,\r\n                    objectFit: \"cover\",\r\n                    boxShadow: \"0 4px 16px rgba(0,0,0,0.15)\"\r\n                  }}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            <Box sx={{ p: 3 }}>\r\n              <Typography fontSize={16} mb={1}>\r\n                📚 <strong>{t(\"sessions.program\")}</strong> : {shareModal.session?.program?.name}\r\n              </Typography>\r\n              <Typography fontSize={16} mb={2}>\r\n                📅 <strong>{t(\"sessions.period\")}</strong> :{\" \"}\r\n                {shareModal.session?.startDate?.slice(0, 10)} ➜ {shareModal.session?.endDate?.slice(0, 10)}\r\n              </Typography>\r\n\r\n              {shareModal.session?.session2Modules?.length > 0 && (\r\n                <>\r\n                  <Typography fontWeight=\"bold\" fontSize={16} mb={1}>\r\n                    🧱 {t(\"sessions.modulesContent\")}\r\n                  </Typography>\r\n                  <ul style={{ paddingLeft: 20 }}>\r\n                    {shareModal.session.session2Modules.map((mod) => (\r\n                      <li key={mod.id}>\r\n                        <Typography fontSize={14}>✅ {mod.module?.name}</Typography>\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                </>\r\n              )}\r\n\r\n              <Typography fontSize={14} mt={3} color=\"text.secondary\">\r\n                #Formation #Éducation #LMS #Apprentissage #Succès\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          {/* Share Text and Buttons */}\r\n          <TextField\r\n            multiline\r\n            rows={8}\r\n            fullWidth\r\n            value={shareText}\r\n            onChange={(e) => setShareText(e.target.value)}\r\n            variant=\"outlined\"\r\n            label={`📝 ${t(\"sessions.customizePost\")}`}\r\n            sx={{ mb: 3 }}\r\n          />\r\n\r\n          <Stack direction=\"row\" spacing={2} flexWrap=\"wrap\" gap={1} mb={2}>\r\n            <Button variant=\"contained\" startIcon={<Facebook />} onClick={() => handleSocialShare('facebook')} sx={{ bgcolor: '#1877f2' }}>Facebook</Button>\r\n            <Button variant=\"contained\" startIcon={<Twitter />} onClick={() => handleSocialShare('twitter')} sx={{ bgcolor: '#1da1f2' }}>Twitter</Button>\r\n            <Button variant=\"contained\" startIcon={<LinkedIn />} onClick={() => handleSocialShare('linkedin')} sx={{ bgcolor: '#0077b5' }}>LinkedIn</Button>\r\n            <Button variant=\"outlined\" startIcon={<ContentCopy />} onClick={handleCopyText}>📋 {t(\"sessions.copyText\")}</Button>\r\n          </Stack>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Feedback Dialog */}\r\n      <AddSessionFeedback \r\n        open={openFeedbackDialog} \r\n        onClose={() => setOpenFeedbackDialog(false)}\r\n        session={selectedSession}\r\n      />\r\n\r\n      {/* Session Feedback List Dialog */}\r\n      {/* <SessionFeedbackList\r\n        sessionId={selectedSessionForFeedbackList?.id}\r\n        open={openSessionFeedbackList}\r\n        onClose={() => setOpenSessionFeedbackList(false)}\r\n      /> */}\r\n    </Paper>\r\n  );\r\n};\r\n\r\nexport default SessionList;\r\n"], "mappings": ";;AAAC,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SACEC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,UAAU,EACVC,SAAS,EACTC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,SAASC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,qBAAqB;AAC/F,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA;EACxB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC;IAAE8D,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;EAC5E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM;IAAEsE;EAAE,CAAC,GAAG3C,cAAc,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAGzC,WAAW,CAAC,CAAC;EAE9B,MAAM0C,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,GAAG,GAAG,MAAM7C,KAAK,CAAC8C,GAAG,CAAC,gCAAgC,CAAC;MAC7DnB,WAAW,CAACkB,GAAG,CAACE,IAAI,CAAC;MACrB,MAAMC,QAAQ,GAAG,CAAC,CAAC;MACnB,MAAMC,OAAO,CAACC,GAAG,CACfL,GAAG,CAACE,IAAI,CAACI,GAAG,CAAC,MAAOhB,OAAO,IAAK;QAC9B,IAAI;UACF,MAAMiB,IAAI,GAAG,MAAMpD,KAAK,CAAC8C,GAAG,CAAC,kCAAkCX,OAAO,CAACkB,EAAE,QAAQ,CAAC;UAClFL,QAAQ,CAACb,OAAO,CAACkB,EAAE,CAAC,GAAGD,IAAI,CAACL,IAAI,IAAI,EAAE;QACxC,CAAC,CAAC,MAAM;UACNC,QAAQ,CAACb,OAAO,CAACkB,EAAE,CAAC,GAAG,EAAE;QAC3B;MACF,CAAC,CACH,CAAC;MACDxB,mBAAmB,CAACmB,QAAQ,CAAC;IAC/B,CAAC,CAAC,MAAM;MACN/C,KAAK,CAACqD,KAAK,CAACZ,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACtC;EACF,CAAC;EAEDvE,SAAS,CAAC,MAAM;IACdyE,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMW,YAAY,GAAG,MAAOF,EAAE,IAAK;IACjC,IAAI;MACF,MAAMrD,KAAK,CAACwD,MAAM,CAAC,kCAAkCH,EAAE,EAAE,CAAC;MAC1DpD,KAAK,CAACwD,OAAO,CAACf,CAAC,CAAC,wBAAwB,CAAC,CAAC;MAC1CE,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,MAAM;MACN3C,KAAK,CAACqD,KAAK,CAACZ,CAAC,CAAC,sBAAsB,CAAC,CAAC;IACxC;EACF,CAAC;EAED,MAAMgB,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,SAAS,KAAK;IACzD,IAAI;MACF,MAAM5D,KAAK,CAAC6D,KAAK,CAAC,kCAAkCF,SAAS,SAAS,EAAE;QACtEG,MAAM,EAAEF;MACV,CAAC,CAAC;MACF3D,KAAK,CAACwD,OAAO,CAACf,CAAC,CAAC,wBAAwB,CAAC,CAAC;MAC1CE,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,MAAM;MACN3C,KAAK,CAACqD,KAAK,CAACZ,CAAC,CAAC,4BAA4B,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAMqB,gBAAgB,GAAG,MAAAA,CAAOJ,SAAS,EAAEK,MAAM,KAAK;IACpD,IAAI;MACF,MAAMhE,KAAK,CAACwD,MAAM,CAAC,kCAAkCG,SAAS,gBAAgBK,MAAM,EAAE,CAAC;MACvF/D,KAAK,CAACwD,OAAO,CAACf,CAAC,CAAC,sBAAsB,CAAC,CAAC;MACxC,MAAME,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOqB,CAAC,EAAE;MAAA,IAAAC,WAAA,EAAAC,gBAAA;MACVlE,KAAK,CAACqD,KAAK,CACT,EAAAY,WAAA,GAAAD,CAAC,CAACG,QAAQ,cAAAF,WAAA,wBAAAC,gBAAA,GAAVD,WAAA,CAAYnB,IAAI,cAAAoB,gBAAA,uBAAhBA,gBAAA,CAAkBE,OAAO,KAAI3B,CAAC,CAAC,0BAA0B,CAC3D,CAAC;IACH;EACF,CAAC;EAED,MAAM4B,aAAa,GAAG,MAAOX,SAAS,IAAK;IACzC,IAAI,CAACrC,SAAS,EAAE;MACdrB,KAAK,CAACqD,KAAK,CAACZ,CAAC,CAAC,qBAAqB,CAAC,CAAC;MACrC;IACF;IACAjB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMzB,KAAK,CAACuE,IAAI,CAAC,kCAAkCZ,SAAS,WAAW,EAAE;QACvEa,KAAK,EAAElD;MACT,CAAC,CAAC;MACFrB,KAAK,CAACwD,OAAO,CAACf,CAAC,CAAC,oBAAoB,CAAC,CAAC;MACtCrB,gBAAgB,CAAC,IAAI,CAAC;MACtBE,YAAY,CAAC,EAAE,CAAC;MAChB,MAAMqB,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOqB,CAAC,EAAE;MAAA,IAAAQ,YAAA,EAAAC,iBAAA;MACVzE,KAAK,CAACqD,KAAK,CACT,EAAAmB,YAAA,GAAAR,CAAC,CAACG,QAAQ,cAAAK,YAAA,wBAAAC,iBAAA,GAAVD,YAAA,CAAY1B,IAAI,cAAA2B,iBAAA,uBAAhBA,iBAAA,CAAkBL,OAAO,KAAI3B,CAAC,CAAC,uBAAuB,CACxD,CAAC;IACH,CAAC,SAAS;MACRjB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMkD,mBAAmB,GAAIhB,SAAS,IAAK;IACzC5B,cAAc,CAAC6C,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP,CAACjB,SAAS,GAAG,CAACiB,IAAI,CAACjB,SAAS;IAC9B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMkB,WAAW,GAAI1C,OAAO,IAAK;IAAA,IAAA2C,gBAAA,EAAAC,kBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IAC/B,MAAMC,IAAI,GAAG,MAAMxC,CAAC,CAAC,8BAA8B,CAAC,aAAaP,OAAO,CAACgD,IAAI,UAAUzC,CAAC,CAAC,kBAAkB,CAAC,KAAK,EAAAoC,gBAAA,GAAA3C,OAAO,CAACiD,OAAO,cAAAN,gBAAA,uBAAfA,gBAAA,CAAiBK,IAAI,KAAIzC,CAAC,CAAC,kBAAkB,CAAC,QAAQA,CAAC,CAAC,iBAAiB,CAAC,MAAAqC,kBAAA,GAAK5C,OAAO,CAACkD,SAAS,cAAAN,kBAAA,uBAAjBA,kBAAA,CAAmBO,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,OAAAN,gBAAA,GAAM7C,OAAO,CAACoD,OAAO,cAAAP,gBAAA,uBAAfA,gBAAA,CAAiBM,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,EAAAL,qBAAA,GAAA9C,OAAO,CAACqD,eAAe,cAAAP,qBAAA,uBAAvBA,qBAAA,CAAyBQ,MAAM,IAAG,CAAC,GAAG,MAAM/C,CAAC,CAAC,0BAA0B,CAAC,KAAK,GAAGP,OAAO,CAACqD,eAAe,CAACrC,GAAG,CAACuC,GAAG;MAAA,IAAAC,WAAA;MAAA,OAAI,MAAAA,WAAA,GAAKD,GAAG,CAACE,MAAM,cAAAD,WAAA,uBAAVA,WAAA,CAAYR,IAAI,EAAE;IAAA,EAAC,CAACU,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE,MAAMnD,CAAC,CAAC,4BAA4B,CAAC,UAAUA,CAAC,CAAC,sBAAsB,CAAC,iGAAiG;IACvlBL,YAAY,CAAC6C,IAAI,CAAC;IAClBjD,aAAa,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC;IAAQ,CAAC,CAAC;EACxC,CAAC;EAED,MAAM2D,iBAAiB,GAAIC,QAAQ,IAAK;IACtC,MAAMC,WAAW,GAAGC,kBAAkB,CAAC7D,SAAS,CAAC;IACjD,MAAM8D,IAAI,GAAG;MACXC,QAAQ,EAAE,gDAAgDF,kBAAkB,CAACG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,EAAE;MACpGC,OAAO,EAAE,yCAAyCP,WAAW,EAAE;MAC/DQ,QAAQ,EAAE,uDAAuDP,kBAAkB,CAACG,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;IAC3G,CAAC;IACDF,MAAM,CAAClE,IAAI,CAACgE,IAAI,CAACH,QAAQ,CAAC,EAAE,QAAQ,EAAE,sBAAsB,CAAC;EAC/D,CAAC;EAED,MAAMU,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACxE,SAAS,CAAC;MAC9CnC,KAAK,CAACwD,OAAO,CAACf,CAAC,CAAC,qBAAqB,CAAC,CAAC;IACzC,CAAC,CAAC,OAAOmE,GAAG,EAAE;MACZ5G,KAAK,CAACqD,KAAK,CAACZ,CAAC,CAAC,oBAAoB,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAMoE,gBAAgB,GAAI3E,OAAO,IAAK;IACpCM,kBAAkB,CAACN,OAAO,CAAC;IAC3BI,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,oBACElC,OAAA,CAAC/B,KAAK;IAACyI,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,YAAY,EAAE,CAAC;MAAEC,eAAe,EAAE;IAAU,CAAE;IAAAC,QAAA,gBAC7E/G,OAAA,CAAChC,UAAU;MAACgJ,OAAO,EAAC,IAAI;MAACC,UAAU,EAAC,MAAM;MAACC,YAAY;MAAAH,QAAA,GAAC,eACnD,EAAC1E,CAAC,CAAC,sBAAsB,CAAC;IAAA;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,EAEZjG,QAAQ,CAAC+D,MAAM,KAAK,CAAC,gBACpBpF,OAAA,CAAChC,UAAU;MAACuJ,EAAE,EAAE,CAAE;MAACC,KAAK,EAAC,gBAAgB;MAAAT,QAAA,EACtC1E,CAAC,CAAC,qBAAqB;IAAC;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,GAEbjG,QAAQ,CAACyB,GAAG,CAAEhB,OAAO;MAAA,IAAA2F,iBAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,sBAAA;MAAA,oBACnB5H,OAAA,CAAC/B,KAAK;QAEJyI,SAAS,EAAE,CAAE;QACbC,EAAE,EAAE;UACFY,EAAE,EAAE,CAAC;UACLX,CAAC,EAAE,CAAC;UACJC,YAAY,EAAE,CAAC;UACfC,eAAe,EAAE,SAAS;UAC1Be,MAAM,EAAE,mBAAmB;UAC3BC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,KAAK;UACpBC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,YAAY;UACxBC,GAAG,EAAE;QACP,CAAE;QAAAnB,QAAA,gBAGF/G,OAAA,CAAC9B,GAAG;UAACiK,IAAI,EAAE,CAAE;UAAApB,QAAA,GACVjF,OAAO,CAACsG,QAAQ,iBACfpI,OAAA,CAAC9B,GAAG;YAACmK,EAAE,EAAE,CAAE;YAACP,OAAO,EAAC,MAAM;YAACE,cAAc,EAAC,QAAQ;YAAAjB,QAAA,eAChD/G,OAAA;cACEsI,GAAG,EAAExG,OAAO,CAACsG,QAAS;cACtBG,GAAG,EAAC,SAAS;cACbC,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,GAAG;gBACd7B,YAAY,EAAE,EAAE;gBAChB8B,SAAS,EAAE;cACb;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDtH,OAAA,CAAC5B,KAAK;YACJwK,SAAS,EAAC,KAAK;YACfZ,cAAc,EAAC,eAAe;YAC9BC,UAAU,EAAC,QAAQ;YACnBI,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBAEN/G,OAAA,CAAChC,UAAU;cAACgJ,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACO,KAAK,EAAC,SAAS;cAAAT,QAAA,GAAC,eACtD,EAACjF,OAAO,CAACgD,IAAI;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACbtH,OAAA,CAAC5B,KAAK;cAACwK,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAACZ,UAAU,EAAC,QAAQ;cAAAlB,QAAA,gBACpD/G,OAAA,CAACvB,IAAI;gBACHqK,KAAK,EAAEhH,OAAO,CAAC2B,MAAO;gBACtB+D,KAAK,EACH1F,OAAO,CAAC2B,MAAM,KAAK,QAAQ,GACvB,SAAS,GACT3B,OAAO,CAAC2B,MAAM,KAAK,UAAU,GAC7B,SAAS,GACT3B,OAAO,CAAC2B,MAAM,KAAK,WAAW,GAC9B,SAAS,GACT,WACL;gBACDkD,EAAE,EAAE;kBAAEM,UAAU,EAAE,GAAG;kBAAE8B,aAAa,EAAE;gBAAa;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACFtH,OAAA,CAACjB,WAAW;gBAACiK,IAAI,EAAC,OAAO;gBAACrC,EAAE,EAAE;kBAAEsC,QAAQ,EAAE;gBAAI,CAAE;gBAAAlC,QAAA,gBAC9C/G,OAAA,CAAChB,UAAU;kBAACgE,EAAE,EAAE,gBAAgBlB,OAAO,CAACkB,EAAE,EAAG;kBAAA+D,QAAA,EAAE1E,CAAC,CAAC,iBAAiB;gBAAC;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACjFtH,OAAA,CAAClB,MAAM;kBACLoK,OAAO,EAAE,gBAAgBpH,OAAO,CAACkB,EAAE,EAAG;kBACtCmG,KAAK,EAAErH,OAAO,CAAC2B,MAAO;kBACtBqF,KAAK,EAAEzG,CAAC,CAAC,iBAAiB,CAAE;kBAC5B+G,QAAQ,EAAExF,CAAC,IAAIP,kBAAkB,CAACvB,OAAO,CAACkB,EAAE,EAAEY,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;kBAAApC,QAAA,gBAE9D/G,OAAA,CAACnB,QAAQ;oBAACsK,KAAK,EAAC,QAAQ;oBAAApC,QAAA,EAAE1E,CAAC,CAAC,iBAAiB;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC1DtH,OAAA,CAACnB,QAAQ;oBAACsK,KAAK,EAAC,UAAU;oBAAApC,QAAA,EAAE1E,CAAC,CAAC,mBAAmB;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9DtH,OAAA,CAACnB,QAAQ;oBAACsK,KAAK,EAAC,WAAW;oBAAApC,QAAA,EAAE1E,CAAC,CAAC,oBAAoB;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChEtH,OAAA,CAACnB,QAAQ;oBAACsK,KAAK,EAAC,UAAU;oBAAApC,QAAA,EAAE1E,CAAC,CAAC,mBAAmB;kBAAC;oBAAA8E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEP,CAAC7F,WAAW,CAACK,OAAO,CAACkB,EAAE,CAAC,iBACvBhD,OAAA,CAAC5B,KAAK;YAACwK,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACZ,UAAU,EAAC,QAAQ;YAACI,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBAC3D/G,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,UAAU;cAClBQ,KAAK,EAAC,OAAO;cACbwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtJ,OAAA,CAACf,UAAU;gBAAAkI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BiC,OAAO,EAAEA,CAAA,KAAMrG,YAAY,CAACpB,OAAO,CAACkB,EAAE,CAAE;cAAA+D,QAAA,EAEvC1E,CAAC,CAAC,iBAAiB;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACTtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,SAAS;cACfwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtJ,OAAA,CAACb,gBAAgB;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCiC,OAAO,EAAEA,CAAA,KAAMjH,QAAQ,CAAC,aAAaR,OAAO,CAACkB,EAAE,UAAU,CAAE;cAAA+D,QAAA,EAE1D1E,CAAC,CAAC,eAAe;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACTtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,SAAS;cACfwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtJ,OAAA,CAACd,iBAAiB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACjCiC,OAAO,EAAEA,CAAA,KAAMjF,mBAAmB,CAACxC,OAAO,CAACkB,EAAE,CAAE;cAAA+D,QAAA,EAE9C1E,CAAC,CAAC,kBAAkB;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,eACTtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,UAAU;cAClBQ,KAAK,EAAC,WAAW;cACjBwB,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC1C,OAAO,CAAE;cAAAiF,QAAA,GACrC,eACI,EAAC1E,CAAC,CAAC,gBAAgB,CAAC;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACTtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,SAAS;cACfwB,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAMjH,QAAQ,CAAC,aAAaR,OAAO,CAACkB,EAAE,eAAe,CAAE;cAAA+D,QAAA,GACjE,eACI,EAAC1E,CAAC,CAAC,8BAA8B,CAAC;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACTtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,WAAW;cACnBQ,KAAK,EAAC,MAAM;cACZwB,IAAI,EAAC,OAAO;cACZM,SAAS,eAAEtJ,OAAA,CAACP,QAAQ;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBiC,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAAC3E,OAAO,CAAE;cAAAiF,QAAA,GAC1C,eACI,EAAC1E,CAAC,CAAC,mBAAmB,CAAC;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACR,EAGAvG,aAAa,KAAKe,OAAO,CAACkB,EAAE,iBAC3BhD,OAAA,CAAC9B,GAAG;YAACqJ,EAAE,EAAE,CAAE;YAACc,EAAE,EAAE,CAAE;YAACP,OAAO,EAAC,MAAM;YAACI,GAAG,EAAE,CAAE;YAACD,UAAU,EAAC,QAAQ;YAAAlB,QAAA,gBAC3D/G,OAAA,CAACxB,SAAS;cACRgL,IAAI,EAAC,OAAO;cACZC,WAAW,EAAEpH,CAAC,CAAC,+BAA+B,CAAE;cAChD8G,KAAK,EAAElI,SAAU;cACjBmI,QAAQ,EAAExF,CAAC,IAAI1C,YAAY,CAAC0C,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;cAC5CH,IAAI,EAAC,OAAO;cACZrC,EAAE,EAAE;gBAAEsC,QAAQ,EAAE;cAAI;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACFtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,WAAW;cACnBgC,IAAI,EAAC,OAAO;cACZxB,KAAK,EAAC,WAAW;cACjB+B,OAAO,EAAEA,CAAA,KAAMtF,aAAa,CAACnC,OAAO,CAACkB,EAAE,CAAE;cACzC0G,QAAQ,EAAEvI,UAAW;cAAA4F,QAAA,EAEpB5F,UAAU,GAAGkB,CAAC,CAAC,iBAAiB,CAAC,GAAGA,CAAC,CAAC,cAAc;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACTtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,MAAM;cACdgC,IAAI,EAAC,OAAO;cACZO,OAAO,EAAEA,CAAA,KAAM;gBAAEvI,gBAAgB,CAAC,IAAI,CAAC;gBAAEE,YAAY,CAAC,EAAE,CAAC;cAAE,CAAE;cAAA6F,QAAA,EAE5D1E,CAAC,CAAC,iBAAiB;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eAEDtH,OAAA,CAAChC,UAAU;YAACgJ,OAAO,EAAC,OAAO;YAACqB,EAAE,EAAE,GAAI;YAAAtB,QAAA,GAAC,eAChC,EAAC1E,CAAC,CAAC,kBAAkB,CAAC,EAAC,KAAG,eAAArC,OAAA;cAAA+G,QAAA,EAAS,EAAAU,iBAAA,GAAA3F,OAAO,CAACiD,OAAO,cAAA0C,iBAAA,uBAAfA,iBAAA,CAAiB3C,IAAI,KAAIzC,CAAC,CAAC,kBAAkB;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACbtH,OAAA,CAAChC,UAAU;YAACgJ,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,eACvB,EAAC1E,CAAC,CAAC,iBAAiB,CAAC,EAAC,GAAC,eAAArC,OAAA;cAAA+G,QAAA,GAAAW,mBAAA,GAAS5F,OAAO,CAACkD,SAAS,cAAA0C,mBAAA,uBAAjBA,mBAAA,CAAmBzC,KAAK,CAAC,CAAC,EAAE,EAAE;YAAC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KAAC,EAACjF,CAAC,CAAC,aAAa,CAAC,EAAE,GAAG,eACnGrC,OAAA;cAAA+G,QAAA,GAAAY,iBAAA,GAAS7F,OAAO,CAACoD,OAAO,cAAAyC,iBAAA,uBAAfA,iBAAA,CAAiB1C,KAAK,CAAC,CAAC,EAAE,EAAE;YAAC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,EAGZ,EAAAM,sBAAA,GAAA9F,OAAO,CAACqD,eAAe,cAAAyC,sBAAA,uBAAvBA,sBAAA,CAAyBxC,MAAM,IAAG,CAAC,iBAClCpF,OAAA,CAAAE,SAAA;YAAA6G,QAAA,gBACE/G,OAAA,CAAC7B,OAAO;cAACwI,EAAE,EAAE;gBAAEgD,EAAE,EAAE;cAAE;YAAE;cAAAxC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1BtH,OAAA,CAAChC,UAAU;cAACgJ,OAAO,EAAC,WAAW;cAACC,UAAU,EAAC,MAAM;cAAAF,QAAA,GAAC,eAC7C,EAAC1E,CAAC,CAAC,yBAAyB,CAAC;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,EACZxF,OAAO,CAACqD,eAAe,CAACrC,GAAG,CAAEuC,GAAG;cAAA,IAAAuE,YAAA,EAAAC,YAAA;cAAA,oBAC/B7J,OAAA,CAAC9B,GAAG;gBAAcqJ,EAAE,EAAE,CAAE;gBAAAR,QAAA,gBACtB/G,OAAA,CAAChC,UAAU;kBAACiJ,UAAU,EAAC,MAAM;kBAACO,KAAK,EAAC,gBAAgB;kBAAAT,QAAA,GAAC,eAChD,GAAA6C,YAAA,GAACvE,GAAG,CAACE,MAAM,cAAAqE,YAAA,uBAAVA,YAAA,CAAY9E,IAAI;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GAAAuC,YAAA,GACZxE,GAAG,CAACyE,OAAO,cAAAD,YAAA,uBAAXA,YAAA,CAAa/G,GAAG,CAAEiH,CAAC;kBAAA,IAAAC,SAAA,EAAAC,WAAA;kBAAA,oBAClBjK,OAAA,CAAC9B,GAAG;oBAAYgM,EAAE,EAAE,CAAE;oBAAC3C,EAAE,EAAE,CAAE;oBAAAR,QAAA,gBAC3B/G,OAAA,CAAChC,UAAU;sBAACgJ,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAACO,KAAK,EAAC,cAAc;sBAAAT,QAAA,GAAC,eAC9D,GAAAiD,SAAA,GAACD,CAAC,CAACI,MAAM,cAAAH,SAAA,uBAARA,SAAA,CAAUI,KAAK;oBAAA;sBAAAjD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,eACbtH,OAAA,CAAC5B,KAAK;sBAACwK,SAAS,EAAC,KAAK;sBAACC,OAAO,EAAE,CAAE;sBAACtB,EAAE,EAAE,CAAE;sBAAC8C,QAAQ,EAAC,MAAM;sBAAAtD,QAAA,GAAAkD,WAAA,GACtDF,CAAC,CAACO,QAAQ,cAAAL,WAAA,uBAAVA,WAAA,CAAYnH,GAAG,CAAEyH,EAAE;wBAAA,IAAAC,YAAA;wBAAA,oBAClBxK,OAAA,CAAC3B,MAAM;0BAEL2I,OAAO,EAAC,UAAU;0BAClBQ,KAAK,EAAC,MAAM;0BACZwB,IAAI,EAAC,OAAO;0BACZrC,EAAE,EAAE;4BAAE8D,EAAE,EAAE,CAAC;4BAAEpC,EAAE,EAAE,CAAC;4BAAExB,YAAY,EAAE;0BAAE,CAAE;0BACtC0C,OAAO,EAAEA,CAAA;4BAAA,IAAAmB,WAAA;4BAAA,OACP,EAAAA,WAAA,GAAAH,EAAE,CAACI,OAAO,cAAAD,WAAA,uBAAVA,WAAA,CAAYE,OAAO,KACnB7E,MAAM,CAAClE,IAAI,CAAC0I,EAAE,CAACI,OAAO,CAACC,OAAO,EAAE,QAAQ,CAAC;0BAAA,CAC1C;0BAAA7D,QAAA,GACF,eACI,GAAAyD,YAAA,GAACD,EAAE,CAACI,OAAO,cAAAH,YAAA,uBAAVA,YAAA,CAAYJ,KAAK;wBAAA,GAVhBG,EAAE,CAACvH,EAAE;0BAAAmE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAWJ,CAAC;sBAAA,CACV;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA,GApBAyC,CAAC,CAAC/G,EAAE;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBT,CAAC;gBAAA,CACP,CAAC;cAAA,GA3BMjC,GAAG,CAACrC,EAAE;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4BX,CAAC;YAAA,CACP,CAAC;UAAA,eACF,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGL7F,WAAW,CAACK,OAAO,CAACkB,EAAE,CAAC,iBACtBhD,OAAA,CAAC/B,KAAK;UACJyI,SAAS,EAAE,CAAE;UACbC,EAAE,EAAE;YACFsC,QAAQ,EAAE,GAAG;YACbR,QAAQ,EAAE,GAAG;YACboC,OAAO,EAAE,SAAS;YAClBhE,YAAY,EAAE,CAAC;YACfD,CAAC,EAAE,CAAC;YACJkB,OAAO,EAAE,MAAM;YACfC,aAAa,EAAE,QAAQ;YACvBE,UAAU,EAAE,SAAS;YACrB6C,SAAS,EAAE;UACb,CAAE;UAAA/D,QAAA,gBAEF/G,OAAA,CAAC9B,GAAG;YAAC4J,OAAO,EAAC,MAAM;YAACG,UAAU,EAAC,QAAQ;YAACD,cAAc,EAAC,eAAe;YAACK,EAAE,EAAE,CAAE;YAAAtB,QAAA,gBAC3E/G,OAAA,CAAChC,UAAU;cAACiJ,UAAU,EAAE,GAAI;cAACO,KAAK,EAAC,SAAS;cAACuD,QAAQ,EAAE,EAAG;cAAAhE,QAAA,EACvD1E,CAAC,CAAC,kBAAkB;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACbtH,OAAA,CAAC3B,MAAM;cACL2I,OAAO,EAAC,MAAM;cACdQ,KAAK,EAAC,SAAS;cACf+B,OAAO,EAAEA,CAAA,KAAMjF,mBAAmB,CAACxC,OAAO,CAACkB,EAAE,CAAE;cAC/C2D,EAAE,EAAE;gBAAEM,UAAU,EAAE,GAAG;gBAAE8D,QAAQ,EAAE,EAAE;gBAAEhC,aAAa,EAAE;cAAO,CAAE;cAAAhC,QAAA,EAE5D1E,CAAC,CAAC,eAAe;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNtH,OAAA,CAAC9B,GAAG;YACF4J,OAAO,EAAC,MAAM;YACdG,UAAU,EAAC,QAAQ;YACnBC,GAAG,EAAE,CAAE;YACPG,EAAE,EAAE,CAAE;YACNwC,OAAO,EAAC,SAAS;YACjBhE,YAAY,EAAE,CAAE;YAChBD,CAAC,EAAE,GAAI;YAAAG,QAAA,gBAEP/G,OAAA,CAACxB,SAAS;cACRwK,IAAI,EAAC,OAAO;cACZQ,IAAI,EAAC,OAAO;cACZC,WAAW,EAAEpH,CAAC,CAAC,qBAAqB,CAAE;cACtC8G,KAAK,EAAEpI,aAAa,KAAKe,OAAO,CAACkB,EAAE,GAAG/B,SAAS,GAAG,EAAG;cACrD+J,OAAO,EAAEA,CAAA,KAAMhK,gBAAgB,CAACc,OAAO,CAACkB,EAAE,CAAE;cAC5CoG,QAAQ,EAAGxF,CAAC,IAAK1C,YAAY,CAAC0C,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;cAC9CnC,OAAO,EAAC,UAAU;cAClBL,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAE0C,OAAO,EAAE,MAAM;gBAAEhE,YAAY,EAAE;cAAE;YAAE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACFtH,OAAA,CAACzB,UAAU;cACTiJ,KAAK,EAAC,SAAS;cACfkC,QAAQ,EAAEvI,UAAW;cACrBoI,OAAO,EAAEA,CAAA,KAAMtF,aAAa,CAACnC,OAAO,CAACkB,EAAE,CAAE;cACzC2D,EAAE,EAAE;gBAAEkE,OAAO,EAAE,SAAS;gBAAErD,KAAK,EAAE,MAAM;gBAAE,SAAS,EAAE;kBAAEqD,OAAO,EAAE;gBAAU;cAAE,CAAE;cAAA9D,QAAA,eAE7E/G,OAAA,CAACd,iBAAiB;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNtH,OAAA,CAAC5B,KAAK;YAACyK,OAAO,EAAE,CAAE;YAAA9B,QAAA,EACf,CAACxF,gBAAgB,CAACO,OAAO,CAACkB,EAAE,CAAC,IAAI,EAAE,EAAEoC,MAAM,KAAK,CAAC,gBAChDpF,OAAA,CAAChC,UAAU;cAACwJ,KAAK,EAAC,gBAAgB;cAACuD,QAAQ,EAAE,EAAG;cAAAhE,QAAA,EAC7C1E,CAAC,CAAC,kBAAkB;YAAC;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,GAEb/F,gBAAgB,CAACO,OAAO,CAACkB,EAAE,CAAC,CAACF,GAAG,CAAEmI,IAAI,iBACpCjL,OAAA,CAAC9B,GAAG;cAEF4J,OAAO,EAAC,MAAM;cACdG,UAAU,EAAC,QAAQ;cACnBC,GAAG,EAAE,CAAE;cACPtB,CAAC,EAAE,CAAE;cACLiE,OAAO,EAAC,MAAM;cACdhE,YAAY,EAAE,CAAE;cAChBF,EAAE,EAAE;gBACFmE,SAAS,EAAE,gCAAgC;gBAC3CI,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,iBAAiB;gBAC7B,SAAS,EAAE;kBAAEC,UAAU,EAAE;gBAAU;cACrC,CAAE;cACF7B,OAAO,EAAEA,CAAA,KAAMjH,QAAQ,CAAC,gBAAgB2I,IAAI,CAACjI,EAAE,EAAE,CAAE;cAAA+D,QAAA,gBAEnD/G,OAAA,CAAC1B,MAAM;gBACLgK,GAAG,EAAE2C,IAAI,CAACI,UAAU,IAAIC,SAAU;gBAClC3E,EAAE,EAAE;kBACF4E,KAAK,EAAE,EAAE;kBAAEC,MAAM,EAAE,EAAE;kBAAEvE,UAAU,EAAE,GAAG;kBAAE8D,QAAQ,EAAE,EAAE;kBACpDF,OAAO,EAAEI,IAAI,CAACI,UAAU,GAAG,aAAa,GAAG;gBAC7C,CAAE;gBAAAtE,QAAA,EAED,CAACkE,IAAI,CAACI,UAAU,IAAIJ,IAAI,CAACnG,IAAI,GAAGmG,IAAI,CAACnG,IAAI,CAAC,CAAC,CAAC,CAAC2G,WAAW,CAAC,CAAC,GAAG;cAAI;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACTtH,OAAA,CAAC9B,GAAG;gBAACyI,EAAE,EAAE;kBAAEwB,IAAI,EAAE;gBAAE,CAAE;gBAAApB,QAAA,gBACnB/G,OAAA,CAAChC,UAAU;kBAACiJ,UAAU,EAAE,GAAI;kBAAC8D,QAAQ,EAAE,EAAG;kBAACvD,KAAK,EAAC,MAAM;kBAAAT,QAAA,EACpDkE,IAAI,CAACnG;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACbtH,OAAA,CAAChC,UAAU;kBAAC+M,QAAQ,EAAE,EAAG;kBAACvD,KAAK,EAAC,MAAM;kBAAAT,QAAA,EACnCkE,IAAI,CAACS;gBAAI;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNtH,OAAA,CAACzB,UAAU;gBACTyK,IAAI,EAAC,OAAO;gBACZxB,KAAK,EAAC,OAAO;gBACb+B,OAAO,EAAE3F,CAAC,IAAI;kBACZA,CAAC,CAAC+H,eAAe,CAAC,CAAC;kBACnBjI,gBAAgB,CAAC5B,OAAO,CAACkB,EAAE,EAAEiI,IAAI,CAACjI,EAAE,CAAC;gBACvC,CAAE;gBAAA+D,QAAA,eAEF/G,OAAA,CAACf,UAAU;kBAAC8L,QAAQ,EAAC;gBAAO;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA,GAzCR2D,IAAI,CAACjI,EAAE;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0CT,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR;MAAA,GArUIxF,OAAO,CAACkB,EAAE;QAAAmE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsUV,CAAC;IAAA,CACT,CACF,eAGDtH,OAAA,CAACtB,MAAM;MAACmD,IAAI,EAAEF,UAAU,CAACE,IAAK;MAAC+J,OAAO,EAAEA,CAAA,KAAMhK,aAAa,CAAC;QAAEC,IAAI,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAE;MAAC2G,QAAQ,EAAC,IAAI;MAACoD,SAAS;MAAA9E,QAAA,gBAClH/G,OAAA,CAACrB,WAAW;QAAAoI,QAAA,GAAC,eACR,EAAC1E,CAAC,CAAC,uBAAuB,CAAC,eAC9BrC,OAAA,CAACzB,UAAU;UAACgL,OAAO,EAAEA,CAAA,KAAM3H,aAAa,CAAC;YAAEC,IAAI,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAK,CAAC,CAAE;UAAC6E,EAAE,EAAE;YAAEmF,QAAQ,EAAE,UAAU;YAAEC,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAjF,QAAA,eACvH/G,OAAA,CAACZ,KAAK;YAAA+H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdtH,OAAA,CAACpB,aAAa;QAAAmI,QAAA,gBAEZ/G,OAAA,CAAC9B,GAAG;UACF8E,EAAE,EAAC,iBAAiB;UACpB2D,EAAE,EAAE;YACFE,YAAY,EAAE,CAAC;YACfoF,QAAQ,EAAE,QAAQ;YAClBpB,OAAO,EAAE,SAAS;YAClBhD,MAAM,EAAE,mBAAmB;YAC3BiD,SAAS,EAAE,CAAC;YACZzC,EAAE,EAAE,CAAC;YACLI,QAAQ,EAAE,GAAG;YACbyD,EAAE,EAAE;UACN,CAAE;UAAAnF,QAAA,gBAEF/G,OAAA,CAAC9B,GAAG;YACFyI,EAAE,EAAE;cACFyE,UAAU,EAAE,0CAA0C;cACtD5D,KAAK,EAAE,MAAM;cACbZ,CAAC,EAAE,CAAC;cACJuF,SAAS,EAAE;YACb,CAAE;YAAApF,QAAA,gBAEF/G,OAAA,CAAChC,UAAU;cAACgJ,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAAAF,QAAA,GAAC,eACtC,GAAA1G,mBAAA,GAACsB,UAAU,CAACG,OAAO,cAAAzB,mBAAA,uBAAlBA,mBAAA,CAAoByE,IAAI;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACbtH,OAAA,CAAChC,UAAU;cAACgJ,OAAO,EAAC,WAAW;cAAAD,QAAA,GAAC,eAC3B,EAAC1E,CAAC,CAAC,yBAAyB,CAAC;YAAA;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,EAEL,EAAAhH,oBAAA,GAAAqB,UAAU,CAACG,OAAO,cAAAxB,oBAAA,uBAAlBA,oBAAA,CAAoB8H,QAAQ,kBAC3BpI,OAAA,CAAC9B,GAAG;YACFyI,EAAE,EAAE;cACFmB,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBlB,eAAe,EAAE,SAAS;cAC1BF,CAAC,EAAE;YACL,CAAE;YAAAG,QAAA,eAEF/G,OAAA;cACEsI,GAAG,EAAE3G,UAAU,CAACG,OAAO,CAACsG,QAAS;cACjCG,GAAG,EAAC,SAAS;cACbC,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,GAAG;gBACd7B,YAAY,EAAE,EAAE;gBAChB8B,SAAS,EAAE,OAAO;gBAClBmC,SAAS,EAAE;cACb;YAAE;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAEDtH,OAAA,CAAC9B,GAAG;YAACyI,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAG,QAAA,gBAChB/G,OAAA,CAAChC,UAAU;cAAC+M,QAAQ,EAAE,EAAG;cAAC1C,EAAE,EAAE,CAAE;cAAAtB,QAAA,GAAC,eAC5B,eAAA/G,OAAA;gBAAA+G,QAAA,EAAS1E,CAAC,CAAC,kBAAkB;cAAC;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,OAAG,GAAA/G,oBAAA,GAACoB,UAAU,CAACG,OAAO,cAAAvB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBwE,OAAO,cAAAvE,qBAAA,uBAA3BA,qBAAA,CAA6BsE,IAAI;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACbtH,OAAA,CAAChC,UAAU;cAAC+M,QAAQ,EAAE,EAAG;cAAC1C,EAAE,EAAE,CAAE;cAAAtB,QAAA,GAAC,eAC5B,eAAA/G,OAAA;gBAAA+G,QAAA,EAAS1E,CAAC,CAAC,iBAAiB;cAAC;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,MAAE,EAAC,GAAG,GAAA7G,oBAAA,GAC/CkB,UAAU,CAACG,OAAO,cAAArB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBuE,SAAS,cAAAtE,qBAAA,uBAA7BA,qBAAA,CAA+BuE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAC,UAAG,GAAAtE,oBAAA,GAACgB,UAAU,CAACG,OAAO,cAAAnB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBuE,OAAO,cAAAtE,qBAAA,uBAA3BA,qBAAA,CAA6BqE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EAEZ,EAAAzG,oBAAA,GAAAc,UAAU,CAACG,OAAO,cAAAjB,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBsE,eAAe,cAAArE,qBAAA,uBAAnCA,qBAAA,CAAqCsE,MAAM,IAAG,CAAC,iBAC9CpF,OAAA,CAAAE,SAAA;cAAA6G,QAAA,gBACE/G,OAAA,CAAChC,UAAU;gBAACiJ,UAAU,EAAC,MAAM;gBAAC8D,QAAQ,EAAE,EAAG;gBAAC1C,EAAE,EAAE,CAAE;gBAAAtB,QAAA,GAAC,eAC9C,EAAC1E,CAAC,CAAC,yBAAyB,CAAC;cAAA;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACbtH,OAAA;gBAAIwI,KAAK,EAAE;kBAAE4D,WAAW,EAAE;gBAAG,CAAE;gBAAArF,QAAA,EAC5BpF,UAAU,CAACG,OAAO,CAACqD,eAAe,CAACrC,GAAG,CAAEuC,GAAG;kBAAA,IAAAgH,YAAA;kBAAA,oBAC1CrM,OAAA;oBAAA+G,QAAA,eACE/G,OAAA,CAAChC,UAAU;sBAAC+M,QAAQ,EAAE,EAAG;sBAAAhE,QAAA,GAAC,SAAE,GAAAsF,YAAA,GAAChH,GAAG,CAACE,MAAM,cAAA8G,YAAA,uBAAVA,YAAA,CAAYvH,IAAI;oBAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa;kBAAC,GADpDjC,GAAG,CAACrC,EAAE;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CAAC;gBAAA,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,eACL,CACH,eAEDtH,OAAA,CAAChC,UAAU;cAAC+M,QAAQ,EAAE,EAAG;cAACxD,EAAE,EAAE,CAAE;cAACC,KAAK,EAAC,gBAAgB;cAAAT,QAAA,EAAC;YAExD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtH,OAAA,CAACxB,SAAS;UACR8N,SAAS;UACTC,IAAI,EAAE,CAAE;UACRV,SAAS;UACT1C,KAAK,EAAEpH,SAAU;UACjBqH,QAAQ,EAAGxF,CAAC,IAAK5B,YAAY,CAAC4B,CAAC,CAACyF,MAAM,CAACF,KAAK,CAAE;UAC9CnC,OAAO,EAAC,UAAU;UAClB8B,KAAK,EAAE,MAAMzG,CAAC,CAAC,wBAAwB,CAAC,EAAG;UAC3CsE,EAAE,EAAE;YAAE0B,EAAE,EAAE;UAAE;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEFtH,OAAA,CAAC5B,KAAK;UAACwK,SAAS,EAAC,KAAK;UAACC,OAAO,EAAE,CAAE;UAACwB,QAAQ,EAAC,MAAM;UAACnC,GAAG,EAAE,CAAE;UAACG,EAAE,EAAE,CAAE;UAAAtB,QAAA,gBAC/D/G,OAAA,CAAC3B,MAAM;YAAC2I,OAAO,EAAC,WAAW;YAACsC,SAAS,eAAEtJ,OAAA,CAACX,QAAQ;cAAA8H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAAC,UAAU,CAAE;YAACkB,EAAE,EAAE;cAAEkE,OAAO,EAAE;YAAU,CAAE;YAAA9D,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChJtH,OAAA,CAAC3B,MAAM;YAAC2I,OAAO,EAAC,WAAW;YAACsC,SAAS,eAAEtJ,OAAA,CAACV,OAAO;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAAC,SAAS,CAAE;YAACkB,EAAE,EAAE;cAAEkE,OAAO,EAAE;YAAU,CAAE;YAAA9D,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC7ItH,OAAA,CAAC3B,MAAM;YAAC2I,OAAO,EAAC,WAAW;YAACsC,SAAS,eAAEtJ,OAAA,CAACT,QAAQ;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEA,CAAA,KAAM9D,iBAAiB,CAAC,UAAU,CAAE;YAACkB,EAAE,EAAE;cAAEkE,OAAO,EAAE;YAAU,CAAE;YAAA9D,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAChJtH,OAAA,CAAC3B,MAAM;YAAC2I,OAAO,EAAC,UAAU;YAACsC,SAAS,eAAEtJ,OAAA,CAACR,WAAW;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACiC,OAAO,EAAEnD,cAAe;YAAAW,QAAA,GAAC,eAAG,EAAC1E,CAAC,CAAC,mBAAmB,CAAC;UAAA;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtH,OAAA,CAACF,kBAAkB;MACjB+B,IAAI,EAAEI,kBAAmB;MACzB2J,OAAO,EAAEA,CAAA,KAAM1J,qBAAqB,CAAC,KAAK,CAAE;MAC5CJ,OAAO,EAAEK;IAAgB;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAQG,CAAC;AAEZ,CAAC;AAAClH,EAAA,CA5lBID,WAAW;EAAA,QAWDT,cAAc,EACXG,WAAW;AAAA;AAAA2M,EAAA,GAZxBrM,WAAW;AA8lBjB,eAAeA,WAAW;AAAC,IAAAqM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}