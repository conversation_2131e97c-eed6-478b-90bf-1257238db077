{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\SeanceFormateurPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { Box, Container, Typography, Button } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { useParams } from \"react-router-dom\";\nimport AddSeanceFormateurView from \"./users/views/AddSeanceFormateurView\";\nimport SeanceFormateurList from \"./users/views/SeanceFormateurList\";\nimport AnimerSeanceView from \"./users/views/AnimerSeanceView\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SeanceFormateurPage = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const {\n    sessionId\n  } = useParams(); // 🔸 session2Id from URL\n\n  const [seances, setSeances] = useState([]);\n  const [selectedSeance, setSelectedSeance] = useState(null);\n  const [sessionDetails, setSessionDetails] = useState(null);\n  const fetchSeances = useCallback(async () => {\n    try {\n      const res = await axios.get(`http://localhost:8000/seance-formateur/session/${sessionId}`);\n      setSeances(res.data);\n    } catch (err) {\n      console.error(\"❌ Erreur chargement séances\", err);\n    }\n  }, [sessionId]);\n  const fetchSessionDetails = useCallback(async () => {\n    console.log(\"🔍 Session ID:\", sessionId);\n    try {\n      const res = await axios.get(`http://localhost:8000/seance-formateur/details/${sessionId}`);\n      console.log(\"✅ Session details:\", res.data);\n      setSessionDetails(res.data);\n    } catch (error) {\n      console.error(\"❌ Erreur chargement session:\", error);\n    }\n  }, [sessionId]);\n  useEffect(() => {\n    fetchSeances();\n    fetchSessionDetails();\n  }, [fetchSeances, fetchSessionDetails]);\n  const handleAnimer = seance => setSelectedSeance(seance);\n  const handleRetour = () => setSelectedSeance(null);\n  const handleDelete = async id => {\n    if (window.confirm(\"Confirmer la suppression de cette séance ?\")) {\n      await axios.delete(`http://localhost:8000/seance-formateur/${id}`);\n      fetchSeances();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      mt: 4,\n      children: [sessionDetails !== null && sessionDetails !== void 0 && sessionDetails.name ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          gutterBottom: true,\n          children: [\"\\uD83C\\uDF93 \", sessionDetails.name, \" \\uD83C\\uDF93\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), sessionDetails.averageRating !== undefined && sessionDetails.averageRating !== null && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: [\"\\u2B50 Note moyenne selon les seances: \", sessionDetails.averageRating.toFixed(2), \" / 5\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Chargement de la session...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 11\n      }, this), selectedSeance ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"flex-end\",\n          mb: 2,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleRetour,\n            variant: \"outlined\",\n            children: [\"\\u2B05\\uFE0F \", t('common.back')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(AnimerSeanceView, {\n          seance: selectedSeance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(AddSeanceFormateurView, {\n          onSeanceCreated: fetchSeances\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 4,\n          children: /*#__PURE__*/_jsxDEV(SeanceFormateurList, {\n            seances: seances,\n            onAnimer: handleAnimer,\n            onDelete: handleDelete\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(SeanceFormateurPage, \"/Drwhwdkg+rvbezYQpUXpaMCKHs=\", false, function () {\n  return [useTranslation, useParams];\n});\n_c = SeanceFormateurPage;\nexport default SeanceFormateurPage;\nvar _c;\n$RefreshReg$(_c, \"SeanceFormateurPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Container", "Typography", "<PERSON><PERSON>", "useTranslation", "axios", "useParams", "AddSeanceFormateurView", "SeanceFormateurList", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SeanceFormateurPage", "_s", "t", "sessionId", "seances", "setSeances", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedSeance", "sessionDetails", "setSessionDetails", "fetchSeances", "res", "get", "data", "err", "console", "error", "fetchSessionDetails", "log", "handleAnimer", "seance", "handleRetour", "handleDelete", "id", "window", "confirm", "delete", "children", "mt", "name", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "averageRating", "undefined", "color", "sx", "mb", "toFixed", "display", "justifyContent", "onClick", "onSeanceCreated", "onAnimer", "onDelete", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/SeanceFormateurPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport { Box, Container, Typo<PERSON>, Button } from \"@mui/material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { useParams } from \"react-router-dom\";\r\n\r\nimport AddSeanceFormateurView from \"./users/views/AddSeanceFormateurView\";\r\nimport SeanceFormateurList from \"./users/views/SeanceFormateurList\";\r\nimport AnimerSeanceView from \"./users/views/AnimerSeanceView\";\r\n\r\nconst SeanceFormateurPage = () => {\r\n  const { t } = useTranslation();\r\n  const { sessionId } = useParams(); // 🔸 session2Id from URL\r\n\r\n  const [seances, setSeances] = useState([]);\r\n  const [selectedSeance, setSelectedSeance] = useState(null);\r\n  const [sessionDetails, setSessionDetails] = useState(null);\r\n\r\n  const fetchSeances = useCallback(async () => {\r\n    try {\r\n      const res = await axios.get(`http://localhost:8000/seance-formateur/session/${sessionId}`);\r\n      setSeances(res.data);\r\n    } catch (err) {\r\n      console.error(\"❌ Erreur chargement séances\", err);\r\n    }\r\n  }, [sessionId]);\r\n\r\n  const fetchSessionDetails = useCallback(async () => {\r\n    console.log(\"🔍 Session ID:\", sessionId);\r\n    try {\r\n      const res = await axios.get(`http://localhost:8000/seance-formateur/details/${sessionId}`);\r\n      console.log(\"✅ Session details:\", res.data);\r\n      setSessionDetails(res.data);\r\n    } catch (error) {\r\n      console.error(\"❌ Erreur chargement session:\", error);\r\n    }\r\n  }, [sessionId]);\r\n\r\n  useEffect(() => {\r\n    fetchSeances();\r\n    fetchSessionDetails();\r\n  }, [fetchSeances, fetchSessionDetails]);\r\n\r\n  const handleAnimer = (seance) => setSelectedSeance(seance);\r\n  const handleRetour = () => setSelectedSeance(null);\r\n\r\n  const handleDelete = async (id) => {\r\n    if (window.confirm(\"Confirmer la suppression de cette séance ?\")) {\r\n      await axios.delete(`http://localhost:8000/seance-formateur/${id}`);\r\n      fetchSeances();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container>\r\n      <Box mt={4}>\r\n        {/* ✅ Affichage du nom de la session */}\r\n        {sessionDetails?.name ? (\r\n          <>\r\n            <Typography variant=\"h4\" gutterBottom>\r\n              🎓 {sessionDetails.name} 🎓\r\n            </Typography>\r\n          {sessionDetails.averageRating !== undefined && sessionDetails.averageRating !== null && (\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\r\n              ⭐ Note moyenne selon les seances: {sessionDetails.averageRating.toFixed(2)} / 5\r\n            </Typography>\r\n          )}\r\n            \r\n          </>\r\n        ) : (\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            Chargement de la session...\r\n          </Typography>\r\n        )}\r\n\r\n        {selectedSeance ? (\r\n          <>\r\n            <Box display=\"flex\" justifyContent=\"flex-end\" mb={2}>\r\n              <Button onClick={handleRetour} variant=\"outlined\">\r\n                ⬅️ {t('common.back')}\r\n              </Button>\r\n            </Box>\r\n            <AnimerSeanceView seance={selectedSeance} />\r\n          </>\r\n        ) : (\r\n          <>\r\n            <AddSeanceFormateurView onSeanceCreated={fetchSeances} />\r\n            <Box mt={4}>\r\n              <SeanceFormateurList\r\n                seances={seances}\r\n                onAnimer={handleAnimer}\r\n                onDelete={handleDelete}\r\n              />\r\n            </Box>\r\n          </>\r\n        )}\r\n      </Box>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default SeanceFormateurPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,GAAG,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAClE,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,QAAQ,kBAAkB;AAE5C,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,mBAAmB,MAAM,mCAAmC;AACnE,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC;EAAE,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAEa;EAAU,CAAC,GAAGX,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEnC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAM2B,YAAY,GAAGzB,WAAW,CAAC,YAAY;IAC3C,IAAI;MACF,MAAM0B,GAAG,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,kDAAkDT,SAAS,EAAE,CAAC;MAC1FE,UAAU,CAACM,GAAG,CAACE,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEF,GAAG,CAAC;IACnD;EACF,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAEf,MAAMc,mBAAmB,GAAGhC,WAAW,CAAC,YAAY;IAClD8B,OAAO,CAACG,GAAG,CAAC,gBAAgB,EAAEf,SAAS,CAAC;IACxC,IAAI;MACF,MAAMQ,GAAG,GAAG,MAAMpB,KAAK,CAACqB,GAAG,CAAC,kDAAkDT,SAAS,EAAE,CAAC;MAC1FY,OAAO,CAACG,GAAG,CAAC,oBAAoB,EAAEP,GAAG,CAACE,IAAI,CAAC;MAC3CJ,iBAAiB,CAACE,GAAG,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAACb,SAAS,CAAC,CAAC;EAEfnB,SAAS,CAAC,MAAM;IACd0B,YAAY,CAAC,CAAC;IACdO,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACP,YAAY,EAAEO,mBAAmB,CAAC,CAAC;EAEvC,MAAME,YAAY,GAAIC,MAAM,IAAKb,iBAAiB,CAACa,MAAM,CAAC;EAC1D,MAAMC,YAAY,GAAGA,CAAA,KAAMd,iBAAiB,CAAC,IAAI,CAAC;EAElD,MAAMe,YAAY,GAAG,MAAOC,EAAE,IAAK;IACjC,IAAIC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;MAChE,MAAMlC,KAAK,CAACmC,MAAM,CAAC,0CAA0CH,EAAE,EAAE,CAAC;MAClEb,YAAY,CAAC,CAAC;IAChB;EACF,CAAC;EAED,oBACEb,OAAA,CAACV,SAAS;IAAAwC,QAAA,eACR9B,OAAA,CAACX,GAAG;MAAC0C,EAAE,EAAE,CAAE;MAAAD,QAAA,GAERnB,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAEqB,IAAI,gBACnBhC,OAAA,CAAAE,SAAA;QAAA4B,QAAA,gBACE9B,OAAA,CAACT,UAAU;UAAC0C,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAJ,QAAA,GAAC,eACjC,EAACnB,cAAc,CAACqB,IAAI,EAAC,eAC1B;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACd3B,cAAc,CAAC4B,aAAa,KAAKC,SAAS,IAAI7B,cAAc,CAAC4B,aAAa,KAAK,IAAI,iBAClFvC,OAAA,CAACT,UAAU;UAAC0C,OAAO,EAAC,OAAO;UAACQ,KAAK,EAAC,gBAAgB;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,GAAC,yCAC9B,EAACnB,cAAc,CAAC4B,aAAa,CAACK,OAAO,CAAC,CAAC,CAAC,EAAC,MAC7E;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA,eAEC,CAAC,gBAEHtC,OAAA,CAACT,UAAU;QAAC0C,OAAO,EAAC,IAAI;QAACQ,KAAK,EAAC,gBAAgB;QAAAX,QAAA,EAAC;MAEhD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb,EAEA7B,cAAc,gBACbT,OAAA,CAAAE,SAAA;QAAA4B,QAAA,gBACE9B,OAAA,CAACX,GAAG;UAACwD,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,UAAU;UAACH,EAAE,EAAE,CAAE;UAAAb,QAAA,eAClD9B,OAAA,CAACR,MAAM;YAACuD,OAAO,EAAEvB,YAAa;YAACS,OAAO,EAAC,UAAU;YAAAH,QAAA,GAAC,eAC7C,EAACzB,CAAC,CAAC,aAAa,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNtC,OAAA,CAACF,gBAAgB;UAACyB,MAAM,EAAEd;QAAe;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eAC5C,CAAC,gBAEHtC,OAAA,CAAAE,SAAA;QAAA4B,QAAA,gBACE9B,OAAA,CAACJ,sBAAsB;UAACoD,eAAe,EAAEnC;QAAa;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzDtC,OAAA,CAACX,GAAG;UAAC0C,EAAE,EAAE,CAAE;UAAAD,QAAA,eACT9B,OAAA,CAACH,mBAAmB;YAClBU,OAAO,EAAEA,OAAQ;YACjB0C,QAAQ,EAAE3B,YAAa;YACvB4B,QAAQ,EAAEzB;UAAa;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,eACN,CACH;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAClC,EAAA,CAzFID,mBAAmB;EAAA,QACTV,cAAc,EACNE,SAAS;AAAA;AAAAwD,EAAA,GAF3BhD,mBAAmB;AA2FzB,eAAeA,mBAAmB;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}