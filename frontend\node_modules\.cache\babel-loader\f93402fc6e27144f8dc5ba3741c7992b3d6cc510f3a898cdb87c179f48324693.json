{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\pages\\\\users\\\\views\\\\seancefeedbacklist.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { Box, Typography, Paper, Dialog, DialogTitle, DialogContent, Button, IconButton, Card, CardHeader, CardContent, Grid } from \"@mui/material\";\nimport { useTranslation } from 'react-i18next';\nimport axios from \"axios\";\nimport { DataGrid } from '@mui/x-data-grid';\nimport { Feedback as FeedbackIcon, Close } from \"@mui/icons-material\";\n\n// Fonction pour obtenir l'emoji et le commentaire selon la note\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getEmojiAndComment = rating => {\n  const ratingNum = Number(rating);\n  if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {\n    return {\n      emoji: '',\n      comment: ''\n    };\n  }\n  const emojiData = [{\n    emoji: '😞',\n    comment: 'Très insatisfait'\n  }, {\n    emoji: '😐',\n    comment: 'Insatisfait'\n  }, {\n    emoji: '🙂',\n    comment: 'Neutre'\n  }, {\n    emoji: '😊',\n    comment: 'Satisfait'\n  }, {\n    emoji: '🤩',\n    comment: 'Très satisfait'\n  }];\n  return emojiData[ratingNum - 1] || {\n    emoji: '',\n    comment: ''\n  };\n};\n\n// Helper: createAnswersFromFeedback - Extrait toutes les parties de réponse\nfunction createAnswersFromFeedback(feedback) {\n  if (!feedback) return [];\n  const answers = [];\n\n  // Section Session\n  if (feedback.sessionRating) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.sessionRating);\n    answers.push({\n      question: '📚 Note de la session',\n      answer: feedback.sessionRating,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.contentQuality) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.contentQuality);\n    answers.push({\n      question: '📖 Qualité du contenu',\n      answer: feedback.contentQuality,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.sessionOrganization) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.sessionOrganization);\n    answers.push({\n      question: '🗂️ Organisation de la session',\n      answer: feedback.sessionOrganization,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.objectivesAchieved) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.objectivesAchieved);\n    answers.push({\n      question: '🎯 Objectifs atteints',\n      answer: feedback.objectivesAchieved,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.sessionDuration) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.sessionDuration);\n    answers.push({\n      question: '⏰ Durée de la séance',\n      answer: feedback.sessionDuration,\n      emoji,\n      comment\n    });\n  }\n\n  // Section Formateur\n  if (feedback.trainerRating) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.trainerRating);\n    answers.push({\n      question: '👨‍🏫 Note du formateur',\n      answer: feedback.trainerRating,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.trainerClarity) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.trainerClarity);\n    answers.push({\n      question: '🔍 Clarté du formateur',\n      answer: feedback.trainerClarity,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.trainerAvailability) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.trainerAvailability);\n    answers.push({\n      question: '🤝 Disponibilité du formateur',\n      answer: feedback.trainerAvailability,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.trainerPedagogy) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.trainerPedagogy);\n    answers.push({\n      question: '🎓 Pédagogie du formateur',\n      answer: feedback.trainerPedagogy,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.trainerInteraction) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.trainerInteraction);\n    answers.push({\n      question: '💬 Interaction du formateur',\n      answer: feedback.trainerInteraction,\n      emoji,\n      comment\n    });\n  }\n\n  // Section Équipe\n  if (feedback.teamRating) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.teamRating);\n    answers.push({\n      question: '👥 Note de l\\'équipe',\n      answer: feedback.teamRating,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.teamCollaboration) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.teamCollaboration);\n    answers.push({\n      question: '🤝 Collaboration de l\\'équipe',\n      answer: feedback.teamCollaboration,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.teamParticipation) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.teamParticipation);\n    answers.push({\n      question: '🙋‍♂️ Participation de l\\'équipe',\n      answer: feedback.teamParticipation,\n      emoji,\n      comment\n    });\n  }\n  if (feedback.teamCommunication) {\n    const {\n      emoji,\n      comment\n    } = getEmojiAndComment(feedback.teamCommunication);\n    answers.push({\n      question: '📢 Communication de l\\'équipe',\n      answer: feedback.teamCommunication,\n      emoji,\n      comment\n    });\n  }\n\n  // Commentaires et suggestions\n  if (feedback.sessionComments) answers.push({\n    question: '💭 Commentaires sur la session',\n    answer: feedback.sessionComments\n  });\n  if (feedback.trainerComments) answers.push({\n    question: '💭 Commentaires sur le formateur',\n    answer: feedback.trainerComments\n  });\n  if (feedback.teamComments) answers.push({\n    question: '💭 Commentaires sur l\\'équipe',\n    answer: feedback.teamComments\n  });\n  if (feedback.suggestions) answers.push({\n    question: '💡 Suggestions d\\'amélioration',\n    answer: feedback.suggestions\n  });\n  if (feedback.wouldRecommend) answers.push({\n    question: '👍 Recommanderiez-vous cette formation ?',\n    answer: feedback.wouldRecommend\n  });\n\n  // Fallback: si aucune donnée structurée, utiliser fullFeedback\n  if (answers.length === 0 && feedback.fullFeedback) {\n    answers.push({\n      question: '💭 Feedback général',\n      answer: feedback.fullFeedback\n    });\n  }\n  return answers;\n}\n\n// Helper: renderStars\nfunction renderStars(rating) {\n  const rounded = Math.round(rating);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: '2rem',\n        color: i < rounded ? '#ffc107' : '#e0e0e0'\n      },\n      children: i < rating ? '★' : '☆'\n    }, i, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 9\n    }, this))\n  }, void 0, false);\n}\nconst FeedbackList = () => {\n  _s();\n  const {\n    t\n  } = useTranslation('seances');\n  const {\n    id: seanceId\n  } = useParams();\n  const [feedbacks, setFeedbacks] = useState([]);\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\n  const reloadFeedbacks = useCallback(() => {\n    if (seanceId) {\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`).then(res => {\n        console.log(\"Feedbacks reçus:\", res.data);\n        // Map the data according to backend response structure\n        const mapped = res.data.map((fb, idx) => ({\n          ...fb,\n          id: fb.id || idx,\n          studentName: fb.studentName || '',\n          studentEmail: fb.studentEmail || '',\n          fullFeedback: fb.fullFeedback || '',\n          averageRating: fb.averageRating,\n          userId: fb.userId\n        }));\n        console.log(\"Feedbacks mappés:\", mapped); // Debug output\n        setFeedbacks(mapped);\n      }).catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\n    }\n  }, [seanceId]);\n  useEffect(() => {\n    reloadFeedbacks();\n  }, [reloadFeedbacks]);\n  const feedbackColumns = [{\n    field: 'id',\n    headerName: t('id'),\n    width: 70\n  }, {\n    field: 'studentName',\n    headerName: t('studentName'),\n    width: 200\n  }, {\n    field: 'studentEmail',\n    headerName: t('studentEmail'),\n    width: 250\n  }, {\n    field: 'fullFeedback',\n    headerName: t('fullFeedback'),\n    width: 250,\n    renderCell: params => /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"contained\",\n      size: \"small\",\n      onClick: async () => {\n        try {\n          // Récupérer les détails complets du feedback\n          const response = await axios.get(`http://localhost:8000/feedback/seance/details/${seanceId}/${params.row.userId || params.row.id}`);\n          setSelectedFeedback(response.data);\n          setFeedbackDialogOpen(true);\n        } catch (error) {\n          console.error('Erreur lors de la récupération des détails:', error);\n          // Fallback: utiliser les données de base\n          setSelectedFeedback(params.row);\n          setFeedbackDialogOpen(true);\n        }\n      },\n      sx: {\n        background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',\n        color: 'white',\n        fontWeight: 'bold',\n        borderRadius: 2,\n        textTransform: 'none',\n        minWidth: 'auto',\n        px: 2,\n        py: 1,\n        fontSize: '0.8rem',\n        boxShadow: '0 3px 5px 2px rgba(102, 126, 234, .3)',\n        '&:hover': {\n          background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',\n          boxShadow: '0 4px 8px 3px rgba(102, 126, 234, .4)',\n          transform: 'translateY(-1px)'\n        },\n        transition: 'all 0.3s ease-in-out'\n      },\n      children: [\"\\uD83D\\uDCCB \", t('showMore')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this)\n  }, {\n    field: 'averageRating',\n    headerName: t('averageRating'),\n    width: 180,\n    renderCell: params => {\n      const avg = params.row.averageRating;\n      if (avg === null || avg === undefined) return t('noRating');\n      const rounded = Math.round(avg);\n      const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n      const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: 22\n          },\n          children: moodEmojis[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            marginLeft: 4\n          },\n          children: moodLabels[rounded - 1]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: '#888',\n            marginLeft: 4\n          },\n          children: [\"(\", avg.toFixed(2), \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 2,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(FeedbackIcon, {\n        fontSize: \"large\",\n        sx: {\n          verticalAlign: 'middle',\n          mr: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), t('feedbackList')]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          height: 500,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(DataGrid, {\n          rows: feedbacks,\n          columns: feedbackColumns,\n          pageSize: 7,\n          rowsPerPageOptions: [5, 10, 20],\n          disableSelectionOnClick: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: feedbackDialogOpen,\n      onClose: () => setFeedbackDialogOpen(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      sx: {\n        '& .MuiDialog-paper': {\n          maxHeight: \"90vh\",\n          overflow: \"auto\",\n          borderRadius: 3\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          color: \"white\",\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          pr: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            fontWeight: \"bold\",\n            children: [\"\\uD83D\\uDCCB \", t('feedbackFrom'), \" \", selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              opacity: 0.9,\n              mt: 0.5\n            },\n            children: selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.studentEmail\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setFeedbackDialogOpen(false),\n          sx: {\n            color: \"white\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Close, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          p: 3\n        },\n        children: selectedFeedback ? (() => {\n          const answers = createAnswersFromFeedback(selectedFeedback);\n          if (answers.length === 0) {\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: t('noFeedbackData')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 19\n            }, this);\n          }\n\n          // Calculer la note moyenne\n          const numericAnswers = answers.map(qa => Number(qa.answer)).filter(val => !isNaN(val) && val >= 1 && val <= 5);\n          const averageRating = (selectedFeedback === null || selectedFeedback === void 0 ? void 0 : selectedFeedback.averageRating) || (numericAnswers.length > 0 ? numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length : 0);\n          const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\n          const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\n          return /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [averageRating > 0 && /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                mb: 3,\n                bgcolor: 'primary.main',\n                color: 'white'\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"\\uD83D\\uDCCA \\xC9valuation Moyenne\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h2\",\n                  fontWeight: \"bold\",\n                  children: [averageRating.toFixed(1), \"/5\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'center',\n                    mb: 1\n                  },\n                  children: renderStars(averageRating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    opacity: 0.9\n                  },\n                  children: moodLabels[Math.round(averageRating) - 1]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 21\n            }, this), (() => {\n              // Définition des sections thématiques avec emojis et couleurs\n              const sections = [{\n                title: t('sessionSection'),\n                emoji: '📚',\n                color: 'primary.light',\n                keywords: ['note de la session', 'organisation', 'objectifs', 'durée', 'durée de la séance', 'qualité du contenu', 'commentaires sur la session']\n              }, {\n                title: t('trainerSection'),\n                emoji: '👨‍🏫',\n                color: 'success.light',\n                keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\n              }, {\n                title: t('teamSection'),\n                emoji: '👥',\n                color: 'info.light',\n                keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\n              }, {\n                title: t('suggestionsSection'),\n                emoji: '💡',\n                color: 'warning.light',\n                keywords: ['suggestions', 'amélioration', 'recommanderait']\n              }];\n\n              // Grouper les réponses par section avec un matching robuste\n              function normalize(str) {\n                return str.toLowerCase().normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\n                .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\n              }\n              const groupedAnswers = answers.length > 0 ? sections.map(section => ({\n                ...section,\n                answers: answers.filter(qa => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\n              })) : [];\n\n              // Réponses non classées\n              const otherAnswers = answers.length > 0 ? answers.filter(qa => !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))) : [];\n              return /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [groupedAnswers.map((section, idx) => section.answers.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n                    sx: {\n                      bgcolor: section.color,\n                      color: 'white'\n                    },\n                    title: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: '1.2rem'\n                        },\n                        children: section.emoji\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 484,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: section.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 483,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 480,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: /*#__PURE__*/_jsxDEV(Grid, {\n                      container: true,\n                      spacing: 2,\n                      children: section.answers.map((qa, qidx) => {\n                        let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                        let value = isNumeric ? Number(qa.answer) : null;\n                        return /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 12,\n                          sm: isNumeric ? 6 : 12,\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              p: 2,\n                              bgcolor: 'grey.50',\n                              borderRadius: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              fontWeight: \"600\",\n                              gutterBottom: true,\n                              children: qa.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 497,\n                              columnNumber: 37\n                            }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: 1\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Box, {\n                                sx: {\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 1\n                                },\n                                children: [qa.emoji && /*#__PURE__*/_jsxDEV(Typography, {\n                                  sx: {\n                                    fontSize: '1.5rem'\n                                  },\n                                  children: qa.emoji\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 504,\n                                  columnNumber: 45\n                                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"body2\",\n                                  fontWeight: \"600\",\n                                  children: qa.comment || moodLabels[value - 1]\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 508,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  color: \"text.secondary\",\n                                  sx: {\n                                    ml: 1\n                                  },\n                                  children: [\"(\", value, \"/5)\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 511,\n                                  columnNumber: 43\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 502,\n                                columnNumber: 41\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 501,\n                              columnNumber: 39\n                            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              style: {\n                                whiteSpace: 'pre-line'\n                              },\n                              children: qa.answer || t('noAnswer')\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 517,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 496,\n                            columnNumber: 35\n                          }, this)\n                        }, qidx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 495,\n                          columnNumber: 33\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 25\n                  }, this)]\n                }, idx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 23\n                }, this)), otherAnswers.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    mb: 3\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n                    sx: {\n                      bgcolor: 'grey.600',\n                      color: 'white'\n                    },\n                    title: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        sx: {\n                          fontSize: '1.2rem'\n                        },\n                        children: \"\\uD83D\\uDCDD\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 536,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: t('otherSection')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 535,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: /*#__PURE__*/_jsxDEV(Grid, {\n                      container: true,\n                      spacing: 2,\n                      children: otherAnswers.map((qa, qidx) => {\n                        let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\n                        let value = isNumeric ? Number(qa.answer) : null;\n                        return /*#__PURE__*/_jsxDEV(Grid, {\n                          item: true,\n                          xs: 12,\n                          sm: isNumeric ? 6 : 12,\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              p: 2,\n                              bgcolor: 'grey.50',\n                              borderRadius: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              fontWeight: \"600\",\n                              gutterBottom: true,\n                              children: qa.question\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 549,\n                              columnNumber: 35\n                            }, this), isNumeric ? /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: 1\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Box, {\n                                sx: {\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: 1\n                                },\n                                children: [qa.emoji && /*#__PURE__*/_jsxDEV(Typography, {\n                                  sx: {\n                                    fontSize: '1.5rem'\n                                  },\n                                  children: qa.emoji\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 556,\n                                  columnNumber: 43\n                                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"body2\",\n                                  fontWeight: \"600\",\n                                  children: qa.comment || moodLabels[value - 1]\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 560,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  color: \"text.secondary\",\n                                  sx: {\n                                    ml: 1\n                                  },\n                                  children: [\"(\", value, \"/5)\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 563,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 554,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 553,\n                              columnNumber: 37\n                            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              style: {\n                                whiteSpace: 'pre-line'\n                              },\n                              children: qa.answer || t('noAnswer')\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 569,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 548,\n                            columnNumber: 33\n                          }, this)\n                        }, qidx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 547,\n                          columnNumber: 31\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true);\n            })()]\n          }, void 0, true);\n        })() : /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.secondary\",\n          sx: {\n            textAlign: 'center',\n            py: 3\n          },\n          children: \"Aucune donn\\xE9e de feedback disponible\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 316,\n    columnNumber: 5\n  }, this);\n};\n_s(FeedbackList, \"SV/2zisFQIcPlPFCxlZYYMqyRBY=\", false, function () {\n  return [useTranslation, useParams];\n});\n_c = FeedbackList;\nexport default FeedbackList;\nvar _c;\n$RefreshReg$(_c, \"FeedbackList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "useParams", "Box", "Typography", "Paper", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "useTranslation", "axios", "DataGrid", "<PERSON><PERSON><PERSON>", "FeedbackIcon", "Close", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getEmojiAndComment", "rating", "ratingNum", "Number", "isNaN", "emoji", "comment", "emojiData", "createAnswersFromFeedback", "feedback", "answers", "sessionRating", "push", "question", "answer", "contentQuality", "sessionOrganization", "objectivesAchieved", "sessionDuration", "trainerRating", "trainerClarity", "trainerAvailability", "trainerPedagogy", "trainerInteraction", "teamRating", "teamCollaboration", "teamParticipation", "teamCommunication", "sessionComments", "trainerComments", "teamComments", "suggestions", "wouldRecommend", "length", "fullFeedback", "renderStars", "rounded", "Math", "round", "children", "Array", "map", "_", "i", "style", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FeedbackList", "_s", "t", "id", "seanceId", "feedbacks", "setFeedbacks", "selectedFeedback", "setSelectedFeedback", "feedbackDialogOpen", "setFeedbackDialogOpen", "reloadFeedbacks", "get", "then", "res", "console", "log", "data", "mapped", "fb", "idx", "studentName", "studentEmail", "averageRating", "userId", "catch", "err", "error", "feedbackColumns", "field", "headerName", "width", "renderCell", "params", "variant", "size", "onClick", "response", "row", "sx", "background", "fontWeight", "borderRadius", "textTransform", "min<PERSON><PERSON><PERSON>", "px", "py", "boxShadow", "transform", "transition", "avg", "undefined", "moodEmojis", "<PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "gap", "marginLeft", "toFixed", "p", "mb", "verticalAlign", "mr", "height", "rows", "columns", "pageSize", "rowsPerPageOptions", "disableSelectionOnClick", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "maxHeight", "overflow", "justifyContent", "pr", "component", "opacity", "mt", "textAlign", "numericAnswers", "qa", "filter", "val", "reduce", "a", "b", "bgcolor", "gutterBottom", "sections", "title", "keywords", "normalize", "str", "toLowerCase", "replace", "groupedAnswers", "section", "some", "keyword", "includes", "otherAnswers", "container", "spacing", "qidx", "isNumeric", "value", "item", "xs", "sm", "ml", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/pages/users/views/seancefeedbacklist.js"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Dialog,\r\n  DialogTitle,\r\n  DialogContent,\r\n  Button,\r\n  IconButton,\r\n  Card,\r\n  CardHeader,\r\n  CardContent,\r\n  Grid,\r\n} from \"@mui/material\";\r\nimport { useTranslation } from 'react-i18next';\r\nimport axios from \"axios\";\r\nimport { DataGrid } from '@mui/x-data-grid';\r\nimport { Feedback as FeedbackIcon, Close } from \"@mui/icons-material\";\r\n\r\n// Fonction pour obtenir l'emoji et le commentaire selon la note\r\nconst getEmojiAndComment = (rating) => {\r\n  const ratingNum = Number(rating);\r\n  if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {\r\n    return { emoji: '', comment: '' };\r\n  }\r\n\r\n  const emojiData = [\r\n    { emoji: '😞', comment: 'Très insatisfait' },\r\n    { emoji: '😐', comment: 'Insatisfait' },\r\n    { emoji: '🙂', comment: 'Neutre' },\r\n    { emoji: '😊', comment: 'Satisfait' },\r\n    { emoji: '🤩', comment: 'Très satisfait' }\r\n  ];\r\n\r\n  return emojiData[ratingNum - 1] || { emoji: '', comment: '' };\r\n};\r\n\r\n// Helper: createAnswersFromFeedback - Extrait toutes les parties de réponse\r\nfunction createAnswersFromFeedback(feedback) {\r\n  if (!feedback) return [];\r\n\r\n  const answers = [];\r\n\r\n  // Section Session\r\n  if (feedback.sessionRating) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.sessionRating);\r\n    answers.push({\r\n      question: '📚 Note de la session',\r\n      answer: feedback.sessionRating,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.contentQuality) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.contentQuality);\r\n    answers.push({\r\n      question: '📖 Qualité du contenu',\r\n      answer: feedback.contentQuality,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.sessionOrganization) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.sessionOrganization);\r\n    answers.push({\r\n      question: '🗂️ Organisation de la session',\r\n      answer: feedback.sessionOrganization,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.objectivesAchieved) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.objectivesAchieved);\r\n    answers.push({\r\n      question: '🎯 Objectifs atteints',\r\n      answer: feedback.objectivesAchieved,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.sessionDuration) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.sessionDuration);\r\n    answers.push({\r\n      question: '⏰ Durée de la séance',\r\n      answer: feedback.sessionDuration,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n\r\n  // Section Formateur\r\n  if (feedback.trainerRating) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.trainerRating);\r\n    answers.push({\r\n      question: '👨‍🏫 Note du formateur',\r\n      answer: feedback.trainerRating,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.trainerClarity) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.trainerClarity);\r\n    answers.push({\r\n      question: '🔍 Clarté du formateur',\r\n      answer: feedback.trainerClarity,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.trainerAvailability) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.trainerAvailability);\r\n    answers.push({\r\n      question: '🤝 Disponibilité du formateur',\r\n      answer: feedback.trainerAvailability,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.trainerPedagogy) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.trainerPedagogy);\r\n    answers.push({\r\n      question: '🎓 Pédagogie du formateur',\r\n      answer: feedback.trainerPedagogy,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.trainerInteraction) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.trainerInteraction);\r\n    answers.push({\r\n      question: '💬 Interaction du formateur',\r\n      answer: feedback.trainerInteraction,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n\r\n  // Section Équipe\r\n  if (feedback.teamRating) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.teamRating);\r\n    answers.push({\r\n      question: '👥 Note de l\\'équipe',\r\n      answer: feedback.teamRating,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.teamCollaboration) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.teamCollaboration);\r\n    answers.push({\r\n      question: '🤝 Collaboration de l\\'équipe',\r\n      answer: feedback.teamCollaboration,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.teamParticipation) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.teamParticipation);\r\n    answers.push({\r\n      question: '🙋‍♂️ Participation de l\\'équipe',\r\n      answer: feedback.teamParticipation,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n  if (feedback.teamCommunication) {\r\n    const { emoji, comment } = getEmojiAndComment(feedback.teamCommunication);\r\n    answers.push({\r\n      question: '📢 Communication de l\\'équipe',\r\n      answer: feedback.teamCommunication,\r\n      emoji,\r\n      comment\r\n    });\r\n  }\r\n\r\n  // Commentaires et suggestions\r\n  if (feedback.sessionComments) answers.push({ question: '💭 Commentaires sur la session', answer: feedback.sessionComments });\r\n  if (feedback.trainerComments) answers.push({ question: '💭 Commentaires sur le formateur', answer: feedback.trainerComments });\r\n  if (feedback.teamComments) answers.push({ question: '💭 Commentaires sur l\\'équipe', answer: feedback.teamComments });\r\n  if (feedback.suggestions) answers.push({ question: '💡 Suggestions d\\'amélioration', answer: feedback.suggestions });\r\n  if (feedback.wouldRecommend) answers.push({ question: '👍 Recommanderiez-vous cette formation ?', answer: feedback.wouldRecommend });\r\n\r\n  // Fallback: si aucune donnée structurée, utiliser fullFeedback\r\n  if (answers.length === 0 && feedback.fullFeedback) {\r\n    answers.push({ question: '💭 Feedback général', answer: feedback.fullFeedback });\r\n  }\r\n\r\n  return answers;\r\n}\r\n\r\n// Helper: renderStars\r\nfunction renderStars(rating) {\r\n  const rounded = Math.round(rating);\r\n  return (\r\n    <>\r\n      {[...Array(5)].map((_, i) => (\r\n        <span\r\n          key={i}\r\n          style={{\r\n            fontSize: '2rem',\r\n            color: i < rounded ? '#ffc107' : '#e0e0e0'\r\n          }}\r\n        >\r\n          {i < rating ? '★' : '☆'}\r\n        </span>\r\n      ))}\r\n    </>\r\n  );\r\n}\r\n\r\nconst FeedbackList = () => {\r\n  const { t } = useTranslation('seances');\r\n  const { id: seanceId } = useParams();\r\n  const [feedbacks, setFeedbacks] = useState([]);\r\n  const [selectedFeedback, setSelectedFeedback] = useState(null);\r\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\r\n\r\n  const reloadFeedbacks = useCallback(() => {\r\n    if (seanceId) {\r\n      axios.get(`http://localhost:8000/feedback/seance/feedbacklist/${seanceId}`)\r\n        .then(res => {\r\n          console.log(\"Feedbacks reçus:\", res.data);\r\n          // Map the data according to backend response structure\r\n          const mapped = res.data.map((fb, idx) => ({\r\n            ...fb,\r\n            id: fb.id || idx,\r\n            studentName: fb.studentName || '',\r\n            studentEmail: fb.studentEmail || '',\r\n            fullFeedback: fb.fullFeedback || '',\r\n            averageRating: fb.averageRating,\r\n            userId: fb.userId\r\n          }));\r\n          console.log(\"Feedbacks mappés:\", mapped); // Debug output\r\n          setFeedbacks(mapped);\r\n        })\r\n        .catch(err => console.error(\"Erreur chargement feedbacklist:\", err));\r\n    }\r\n  }, [seanceId]);\r\n\r\n  useEffect(() => {\r\n    reloadFeedbacks();\r\n  }, [reloadFeedbacks]);\r\n\r\n  const feedbackColumns = [\r\n    { field: 'id', headerName: t('id'), width: 70 },\r\n    { field: 'studentName', headerName: t('studentName'), width: 200 },\r\n    { field: 'studentEmail', headerName: t('studentEmail'), width: 250 },\r\n    {\r\n      field: 'fullFeedback',\r\n      headerName: t('fullFeedback'),\r\n      width: 250,\r\n      renderCell: (params) => (\r\n        <Button\r\n          variant=\"contained\"\r\n          size=\"small\"\r\n          onClick={async () => {\r\n            try {\r\n              // Récupérer les détails complets du feedback\r\n              const response = await axios.get(`http://localhost:8000/feedback/seance/details/${seanceId}/${params.row.userId || params.row.id}`);\r\n              setSelectedFeedback(response.data);\r\n              setFeedbackDialogOpen(true);\r\n            } catch (error) {\r\n              console.error('Erreur lors de la récupération des détails:', error);\r\n              // Fallback: utiliser les données de base\r\n              setSelectedFeedback(params.row);\r\n              setFeedbackDialogOpen(true);\r\n            }\r\n          }}\r\n          sx={{\r\n            background: 'linear-gradient(45deg, #667eea 30%, #764ba2 90%)',\r\n            color: 'white',\r\n            fontWeight: 'bold',\r\n            borderRadius: 2,\r\n            textTransform: 'none',\r\n            minWidth: 'auto',\r\n            px: 2,\r\n            py: 1,\r\n            fontSize: '0.8rem',\r\n            boxShadow: '0 3px 5px 2px rgba(102, 126, 234, .3)',\r\n            '&:hover': {\r\n              background: 'linear-gradient(45deg, #5a6fd8 30%, #6a4190 90%)',\r\n              boxShadow: '0 4px 8px 3px rgba(102, 126, 234, .4)',\r\n              transform: 'translateY(-1px)',\r\n            },\r\n            transition: 'all 0.3s ease-in-out',\r\n          }}\r\n        >\r\n          📋 {t('showMore')}\r\n        </Button>\r\n      ),\r\n    },\r\n    {\r\n      field: 'averageRating',\r\n      headerName: t('averageRating'),\r\n      width: 180,\r\n      renderCell: (params) => {\r\n        const avg = params.row.averageRating;\r\n        if (avg === null || avg === undefined) return t('noRating');\r\n        const rounded = Math.round(avg);\r\n        const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n        const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n        return (\r\n          <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>\r\n            <span style={{ fontSize: 22 }}>{moodEmojis[rounded - 1]}</span>\r\n            <span style={{ fontWeight: 'bold', marginLeft: 4 }}>{moodLabels[rounded - 1]}</span>\r\n            <span style={{ color: '#888', marginLeft: 4 }}>({avg.toFixed(2)})</span>\r\n          </span>\r\n        );\r\n      }\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <Box p={2}>\r\n      <Typography variant=\"h4\" mb={3}>\r\n        <FeedbackIcon fontSize=\"large\" sx={{ verticalAlign: 'middle', mr: 2 }} />\r\n        {t('feedbackList')}\r\n      </Typography>\r\n\r\n      <Paper sx={{ p: 3 }}>\r\n        <Box sx={{ height: 500, width: '100%' }}>\r\n          <DataGrid\r\n            rows={feedbacks}\r\n            columns={feedbackColumns}\r\n            pageSize={7}\r\n            rowsPerPageOptions={[5, 10, 20]}\r\n            disableSelectionOnClick\r\n          />\r\n        </Box>\r\n      </Paper>\r\n\r\n      {/* Feedback Dialog */}\r\n      <Dialog\r\n        open={feedbackDialogOpen}\r\n        onClose={() => setFeedbackDialogOpen(false)}\r\n        maxWidth=\"md\"\r\n        fullWidth\r\n        sx={{\r\n          '& .MuiDialog-paper': {\r\n            maxHeight: \"90vh\",\r\n            overflow: \"auto\",\r\n            borderRadius: 3,\r\n          }\r\n        }}\r\n      >\r\n        <DialogTitle\r\n          sx={{\r\n            background: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\r\n            color: \"white\",\r\n            display: \"flex\",\r\n            justifyContent: \"space-between\",\r\n            alignItems: \"center\",\r\n            pr: 1,\r\n          }}\r\n        >\r\n          <Box>\r\n            <Typography variant=\"h5\" component=\"h1\" fontWeight=\"bold\">\r\n              📋 {t('feedbackFrom')} {selectedFeedback?.studentName}\r\n            </Typography>\r\n            <Typography variant=\"body2\" sx={{ opacity: 0.9, mt: 0.5 }}>\r\n              {selectedFeedback?.studentEmail}\r\n            </Typography>\r\n          </Box>\r\n          <IconButton onClick={() => setFeedbackDialogOpen(false)} sx={{ color: \"white\" }}>\r\n            <Close />\r\n          </IconButton>\r\n        </DialogTitle>\r\n        <DialogContent sx={{ p: 3 }}>\r\n            {selectedFeedback ? (() => {\r\n              const answers = createAnswersFromFeedback(selectedFeedback);\r\n\r\n              if (answers.length === 0) {\r\n                return (\r\n                  <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                    {t('noFeedbackData')}\r\n                  </Typography>\r\n                );\r\n              }\r\n\r\n              // Calculer la note moyenne\r\n              const numericAnswers = answers\r\n                .map(qa => Number(qa.answer))\r\n                .filter(val => !isNaN(val) && val >= 1 && val <= 5);\r\n              const averageRating = selectedFeedback?.averageRating ||\r\n                (numericAnswers.length > 0 ? numericAnswers.reduce((a, b) => a + b, 0) / numericAnswers.length : 0);\r\n\r\n              const moodEmojis = [\"😞\", \"😐\", \"🙂\", \"😊\", \"🤩\"];\r\n              const moodLabels = [t('veryDissatisfied'), t('dissatisfied'), t('neutral'), t('satisfied'), t('verySatisfied')];\r\n\r\n              return (\r\n                <>\r\n                  {/* Évaluation moyenne */}\r\n                  {averageRating > 0 && (\r\n                    <Card sx={{ mb: 3, bgcolor: 'primary.main', color: 'white' }}>\r\n                      <CardContent sx={{ textAlign: 'center' }}>\r\n                        <Typography variant=\"h6\" gutterBottom>\r\n                          📊 Évaluation Moyenne\r\n                        </Typography>\r\n                        <Typography variant=\"h2\" fontWeight=\"bold\">\r\n                          {averageRating.toFixed(1)}/5\r\n                        </Typography>\r\n                        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>\r\n                          {renderStars(averageRating)}\r\n                        </Box>\r\n                        <Typography variant=\"subtitle1\" sx={{ opacity: 0.9 }}>\r\n                          {moodLabels[Math.round(averageRating) - 1]}\r\n                        </Typography>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n\r\n\r\n\r\n                  {(() => {\r\n                    // Définition des sections thématiques avec emojis et couleurs\r\n                    const sections = [\r\n                      {\r\n                        title: t('sessionSection'),\r\n                        emoji: '📚',\r\n                        color: 'primary.light',\r\n                        keywords: [\r\n                          'note de la session',\r\n                          'organisation',\r\n                          'objectifs',\r\n                          'durée',\r\n                          'durée de la séance',\r\n                          'qualité du contenu',\r\n                          'commentaires sur la session'\r\n                        ]\r\n                      },\r\n                      {\r\n                        title: t('trainerSection'),\r\n                        emoji: '👨‍🏫',\r\n                        color: 'success.light',\r\n                        keywords: ['note du formateur', 'clarté', 'disponibilité', 'pédagogie', 'interaction', 'commentaires sur le formateur']\r\n                      },\r\n                      {\r\n                        title: t('teamSection'),\r\n                        emoji: '👥',\r\n                        color: 'info.light',\r\n                        keywords: ['note de l\\'équipe', 'collaboration', 'participation', 'communication', 'commentaires sur l\\'équipe']\r\n                      },\r\n                      {\r\n                        title: t('suggestionsSection'),\r\n                        emoji: '💡',\r\n                        color: 'warning.light',\r\n                        keywords: ['suggestions', 'amélioration', 'recommanderait']\r\n                      }\r\n                    ];\r\n\r\n                    // Grouper les réponses par section avec un matching robuste\r\n                    function normalize(str) {\r\n                      return str\r\n                        .toLowerCase()\r\n                        .normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') // retire les accents\r\n                        .replace(/[^a-z0-9]/g, ''); // retire tout sauf lettres/chiffres\r\n                    }\r\n\r\n                    const groupedAnswers = answers.length > 0 ? sections.map(section => ({\r\n                      ...section,\r\n                      answers: answers.filter(qa =>\r\n                        section.keywords.some(keyword =>\r\n                          normalize(qa.question).includes(normalize(keyword))\r\n                        )\r\n                      )\r\n                    })) : [];\r\n\r\n                    // Réponses non classées\r\n                    const otherAnswers = answers.length > 0 ? answers.filter(qa =>\r\n                      !sections.some(section => section.keywords.some(keyword => normalize(qa.question).includes(normalize(keyword))))\r\n                    ) : [];\r\n\r\n                    return (\r\n                <>\r\n                  {groupedAnswers.map((section, idx) =>\r\n                    section.answers.length > 0 && (\r\n                      <Card key={idx} sx={{ mb: 3 }}>\r\n                        <CardHeader\r\n                          sx={{ bgcolor: section.color, color: 'white' }}\r\n                          title={\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                              <Typography sx={{ fontSize: '1.2rem' }}>{section.emoji}</Typography>\r\n                              <Typography variant=\"h6\">{section.title}</Typography>\r\n                            </Box>\r\n                          }\r\n                        />\r\n                        <CardContent>\r\n                          <Grid container spacing={2}>\r\n                            {section.answers.map((qa, qidx) => {\r\n                              let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                              let value = isNumeric ? Number(qa.answer) : null;\r\n                              return (\r\n                                <Grid item xs={12} sm={isNumeric ? 6 : 12} key={qidx}>\r\n                                  <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\r\n                                    <Typography variant=\"body2\" fontWeight=\"600\" gutterBottom>\r\n                                      {qa.question}\r\n                                    </Typography>\r\n                                    {isNumeric ? (\r\n                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                          {qa.emoji && (\r\n                                            <Typography sx={{ fontSize: '1.5rem' }}>\r\n                                              {qa.emoji}\r\n                                            </Typography>\r\n                                          )}\r\n                                          <Typography variant=\"body2\" fontWeight=\"600\">\r\n                                            {qa.comment || moodLabels[value - 1]}\r\n                                          </Typography>\r\n                                          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ ml: 1 }}>\r\n                                            ({value}/5)\r\n                                          </Typography>\r\n                                        </Box>\r\n                                      </Box>\r\n                                    ) : (\r\n                                      <Typography variant=\"body2\" style={{ whiteSpace: 'pre-line' }}>\r\n                                        {qa.answer || t('noAnswer')}\r\n                                      </Typography>\r\n                                    )}\r\n                                  </Box>\r\n                                </Grid>\r\n                              );\r\n                            })}\r\n                          </Grid>\r\n                        </CardContent>\r\n                      </Card>\r\n                    )\r\n                  )}\r\n                  {otherAnswers.length > 0 && (\r\n                    <Card sx={{ mb: 3 }}>\r\n                      <CardHeader\r\n                        sx={{ bgcolor: 'grey.600', color: 'white' }}\r\n                        title={\r\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                            <Typography sx={{ fontSize: '1.2rem' }}>📝</Typography>\r\n                            <Typography variant=\"h6\">{t('otherSection')}</Typography>\r\n                          </Box>\r\n                        }\r\n                      />\r\n                      <CardContent>\r\n                        <Grid container spacing={2}>\r\n                          {otherAnswers.map((qa, qidx) => {\r\n                            let isNumeric = !isNaN(Number(qa.answer)) && Number(qa.answer) >= 1 && Number(qa.answer) <= 5;\r\n                            let value = isNumeric ? Number(qa.answer) : null;\r\n                            return (\r\n                              <Grid item xs={12} sm={isNumeric ? 6 : 12} key={qidx}>\r\n                                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\r\n                                  <Typography variant=\"body2\" fontWeight=\"600\" gutterBottom>\r\n                                    {qa.question}\r\n                                  </Typography>\r\n                                  {isNumeric ? (\r\n                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n                                        {qa.emoji && (\r\n                                          <Typography sx={{ fontSize: '1.5rem' }}>\r\n                                            {qa.emoji}\r\n                                          </Typography>\r\n                                        )}\r\n                                        <Typography variant=\"body2\" fontWeight=\"600\">\r\n                                          {qa.comment || moodLabels[value - 1]}\r\n                                        </Typography>\r\n                                        <Typography variant=\"caption\" color=\"text.secondary\" sx={{ ml: 1 }}>\r\n                                          ({value}/5)\r\n                                        </Typography>\r\n                                      </Box>\r\n                                    </Box>\r\n                                  ) : (\r\n                                    <Typography variant=\"body2\" style={{ whiteSpace: 'pre-line' }}>\r\n                                      {qa.answer || t('noAnswer')}\r\n                                    </Typography>\r\n                                  )}\r\n                                </Box>\r\n                              </Grid>\r\n                            );\r\n                          })}\r\n                        </Grid>\r\n                      </CardContent>\r\n                    </Card>\r\n                  )}\r\n                    </>\r\n                    );\r\n                  })()}\r\n                </>\r\n              );\r\n            })() : (\r\n              <Typography color=\"text.secondary\" sx={{ textAlign: 'center', py: 3 }}>\r\n                Aucune donnée de feedback disponible\r\n              </Typography>\r\n            )}\r\n        </DialogContent>\r\n      </Dialog>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default FeedbackList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,IAAI,EACJC,UAAU,EACVC,WAAW,EACXC,IAAI,QACC,eAAe;AACtB,SAASC,cAAc,QAAQ,eAAe;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,QAAQ,IAAIC,YAAY,EAAEC,KAAK,QAAQ,qBAAqB;;AAErE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;EACrC,MAAMC,SAAS,GAAGC,MAAM,CAACF,MAAM,CAAC;EAChC,IAAIG,KAAK,CAACF,SAAS,CAAC,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,CAAC,EAAE;IACtD,OAAO;MAAEG,KAAK,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAC;EACnC;EAEA,MAAMC,SAAS,GAAG,CAChB;IAAEF,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAmB,CAAC,EAC5C;IAAED,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAc,CAAC,EACvC;IAAED,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAS,CAAC,EAClC;IAAED,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAY,CAAC,EACrC;IAAED,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAiB,CAAC,CAC3C;EAED,OAAOC,SAAS,CAACL,SAAS,GAAG,CAAC,CAAC,IAAI;IAAEG,KAAK,EAAE,EAAE;IAAEC,OAAO,EAAE;EAAG,CAAC;AAC/D,CAAC;;AAED;AACA,SAASE,yBAAyBA,CAACC,QAAQ,EAAE;EAC3C,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EAExB,MAAMC,OAAO,GAAG,EAAE;;EAElB;EACA,IAAID,QAAQ,CAACE,aAAa,EAAE;IAC1B,MAAM;MAAEN,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACE,aAAa,CAAC;IACrED,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,uBAAuB;MACjCC,MAAM,EAAEL,QAAQ,CAACE,aAAa;MAC9BN,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACM,cAAc,EAAE;IAC3B,MAAM;MAAEV,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACM,cAAc,CAAC;IACtEL,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,uBAAuB;MACjCC,MAAM,EAAEL,QAAQ,CAACM,cAAc;MAC/BV,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACO,mBAAmB,EAAE;IAChC,MAAM;MAAEX,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACO,mBAAmB,CAAC;IAC3EN,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,gCAAgC;MAC1CC,MAAM,EAAEL,QAAQ,CAACO,mBAAmB;MACpCX,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACQ,kBAAkB,EAAE;IAC/B,MAAM;MAAEZ,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACQ,kBAAkB,CAAC;IAC1EP,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,uBAAuB;MACjCC,MAAM,EAAEL,QAAQ,CAACQ,kBAAkB;MACnCZ,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACS,eAAe,EAAE;IAC5B,MAAM;MAAEb,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACS,eAAe,CAAC;IACvER,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,sBAAsB;MAChCC,MAAM,EAAEL,QAAQ,CAACS,eAAe;MAChCb,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIG,QAAQ,CAACU,aAAa,EAAE;IAC1B,MAAM;MAAEd,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACU,aAAa,CAAC;IACrET,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,yBAAyB;MACnCC,MAAM,EAAEL,QAAQ,CAACU,aAAa;MAC9Bd,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACW,cAAc,EAAE;IAC3B,MAAM;MAAEf,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACW,cAAc,CAAC;IACtEV,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,wBAAwB;MAClCC,MAAM,EAAEL,QAAQ,CAACW,cAAc;MAC/Bf,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACY,mBAAmB,EAAE;IAChC,MAAM;MAAEhB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACY,mBAAmB,CAAC;IAC3EX,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,+BAA+B;MACzCC,MAAM,EAAEL,QAAQ,CAACY,mBAAmB;MACpChB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACa,eAAe,EAAE;IAC5B,MAAM;MAAEjB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACa,eAAe,CAAC;IACvEZ,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,2BAA2B;MACrCC,MAAM,EAAEL,QAAQ,CAACa,eAAe;MAChCjB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACc,kBAAkB,EAAE;IAC/B,MAAM;MAAElB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACc,kBAAkB,CAAC;IAC1Eb,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,6BAA6B;MACvCC,MAAM,EAAEL,QAAQ,CAACc,kBAAkB;MACnClB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIG,QAAQ,CAACe,UAAU,EAAE;IACvB,MAAM;MAAEnB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACe,UAAU,CAAC;IAClEd,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,sBAAsB;MAChCC,MAAM,EAAEL,QAAQ,CAACe,UAAU;MAC3BnB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACgB,iBAAiB,EAAE;IAC9B,MAAM;MAAEpB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACgB,iBAAiB,CAAC;IACzEf,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,+BAA+B;MACzCC,MAAM,EAAEL,QAAQ,CAACgB,iBAAiB;MAClCpB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACiB,iBAAiB,EAAE;IAC9B,MAAM;MAAErB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACiB,iBAAiB,CAAC;IACzEhB,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,kCAAkC;MAC5CC,MAAM,EAAEL,QAAQ,CAACiB,iBAAiB;MAClCrB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,QAAQ,CAACkB,iBAAiB,EAAE;IAC9B,MAAM;MAAEtB,KAAK;MAAEC;IAAQ,CAAC,GAAGN,kBAAkB,CAACS,QAAQ,CAACkB,iBAAiB,CAAC;IACzEjB,OAAO,CAACE,IAAI,CAAC;MACXC,QAAQ,EAAE,+BAA+B;MACzCC,MAAM,EAAEL,QAAQ,CAACkB,iBAAiB;MAClCtB,KAAK;MACLC;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIG,QAAQ,CAACmB,eAAe,EAAElB,OAAO,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE,gCAAgC;IAAEC,MAAM,EAAEL,QAAQ,CAACmB;EAAgB,CAAC,CAAC;EAC5H,IAAInB,QAAQ,CAACoB,eAAe,EAAEnB,OAAO,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE,kCAAkC;IAAEC,MAAM,EAAEL,QAAQ,CAACoB;EAAgB,CAAC,CAAC;EAC9H,IAAIpB,QAAQ,CAACqB,YAAY,EAAEpB,OAAO,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE,+BAA+B;IAAEC,MAAM,EAAEL,QAAQ,CAACqB;EAAa,CAAC,CAAC;EACrH,IAAIrB,QAAQ,CAACsB,WAAW,EAAErB,OAAO,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE,gCAAgC;IAAEC,MAAM,EAAEL,QAAQ,CAACsB;EAAY,CAAC,CAAC;EACpH,IAAItB,QAAQ,CAACuB,cAAc,EAAEtB,OAAO,CAACE,IAAI,CAAC;IAAEC,QAAQ,EAAE,0CAA0C;IAAEC,MAAM,EAAEL,QAAQ,CAACuB;EAAe,CAAC,CAAC;;EAEpI;EACA,IAAItB,OAAO,CAACuB,MAAM,KAAK,CAAC,IAAIxB,QAAQ,CAACyB,YAAY,EAAE;IACjDxB,OAAO,CAACE,IAAI,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAEC,MAAM,EAAEL,QAAQ,CAACyB;IAAa,CAAC,CAAC;EAClF;EAEA,OAAOxB,OAAO;AAChB;;AAEA;AACA,SAASyB,WAAWA,CAAClC,MAAM,EAAE;EAC3B,MAAMmC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACrC,MAAM,CAAC;EAClC,oBACEJ,OAAA,CAAAE,SAAA;IAAAwC,QAAA,EACG,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtB9C,OAAA;MAEE+C,KAAK,EAAE;QACLC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAEH,CAAC,GAAGP,OAAO,GAAG,SAAS,GAAG;MACnC,CAAE;MAAAG,QAAA,EAEDI,CAAC,GAAG1C,MAAM,GAAG,GAAG,GAAG;IAAG,GANlB0C,CAAC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOF,CACP;EAAC,gBACF,CAAC;AAEP;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEC;EAAE,CAAC,GAAG/D,cAAc,CAAC,SAAS,CAAC;EACvC,MAAM;IAAEgE,EAAE,EAAEC;EAAS,CAAC,GAAG9E,SAAS,CAAC,CAAC;EACpC,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAMuF,eAAe,GAAGtF,WAAW,CAAC,MAAM;IACxC,IAAI+E,QAAQ,EAAE;MACZhE,KAAK,CAACwE,GAAG,CAAC,sDAAsDR,QAAQ,EAAE,CAAC,CACxES,IAAI,CAACC,GAAG,IAAI;QACXC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,GAAG,CAACG,IAAI,CAAC;QACzC;QACA,MAAMC,MAAM,GAAGJ,GAAG,CAACG,IAAI,CAAC3B,GAAG,CAAC,CAAC6B,EAAE,EAAEC,GAAG,MAAM;UACxC,GAAGD,EAAE;UACLhB,EAAE,EAAEgB,EAAE,CAAChB,EAAE,IAAIiB,GAAG;UAChBC,WAAW,EAAEF,EAAE,CAACE,WAAW,IAAI,EAAE;UACjCC,YAAY,EAAEH,EAAE,CAACG,YAAY,IAAI,EAAE;UACnCvC,YAAY,EAAEoC,EAAE,CAACpC,YAAY,IAAI,EAAE;UACnCwC,aAAa,EAAEJ,EAAE,CAACI,aAAa;UAC/BC,MAAM,EAAEL,EAAE,CAACK;QACb,CAAC,CAAC,CAAC;QACHT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,MAAM,CAAC,CAAC,CAAC;QAC1CZ,YAAY,CAACY,MAAM,CAAC;MACtB,CAAC,CAAC,CACDO,KAAK,CAACC,GAAG,IAAIX,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAED,GAAG,CAAC,CAAC;IACxE;EACF,CAAC,EAAE,CAACtB,QAAQ,CAAC,CAAC;EAEdjF,SAAS,CAAC,MAAM;IACdwF,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAErB,MAAMiB,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,IAAI;IAAEC,UAAU,EAAE5B,CAAC,CAAC,IAAI,CAAC;IAAE6B,KAAK,EAAE;EAAG,CAAC,EAC/C;IAAEF,KAAK,EAAE,aAAa;IAAEC,UAAU,EAAE5B,CAAC,CAAC,aAAa,CAAC;IAAE6B,KAAK,EAAE;EAAI,CAAC,EAClE;IAAEF,KAAK,EAAE,cAAc;IAAEC,UAAU,EAAE5B,CAAC,CAAC,cAAc,CAAC;IAAE6B,KAAK,EAAE;EAAI,CAAC,EACpE;IACEF,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE5B,CAAC,CAAC,cAAc,CAAC;IAC7B6B,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,iBACjBvF,OAAA,CAACb,MAAM;MACLqG,OAAO,EAAC,WAAW;MACnBC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAE,MAAAA,CAAA,KAAY;QACnB,IAAI;UACF;UACA,MAAMC,QAAQ,GAAG,MAAMjG,KAAK,CAACwE,GAAG,CAAC,iDAAiDR,QAAQ,IAAI6B,MAAM,CAACK,GAAG,CAACd,MAAM,IAAIS,MAAM,CAACK,GAAG,CAACnC,EAAE,EAAE,CAAC;UACnIK,mBAAmB,CAAC6B,QAAQ,CAACpB,IAAI,CAAC;UAClCP,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAC,CAAC,OAAOiB,KAAK,EAAE;UACdZ,OAAO,CAACY,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;UACnE;UACAnB,mBAAmB,CAACyB,MAAM,CAACK,GAAG,CAAC;UAC/B5B,qBAAqB,CAAC,IAAI,CAAC;QAC7B;MACF,CAAE;MACF6B,EAAE,EAAE;QACFC,UAAU,EAAE,kDAAkD;QAC9D7C,KAAK,EAAE,OAAO;QACd8C,UAAU,EAAE,MAAM;QAClBC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,MAAM;QACrBC,QAAQ,EAAE,MAAM;QAChBC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLpD,QAAQ,EAAE,QAAQ;QAClBqD,SAAS,EAAE,uCAAuC;QAClD,SAAS,EAAE;UACTP,UAAU,EAAE,kDAAkD;UAC9DO,SAAS,EAAE,uCAAuC;UAClDC,SAAS,EAAE;QACb,CAAC;QACDC,UAAU,EAAE;MACd,CAAE;MAAA7D,QAAA,GACH,eACI,EAACc,CAAC,CAAC,UAAU,CAAC;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX;EAEZ,CAAC,EACD;IACE8B,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE5B,CAAC,CAAC,eAAe,CAAC;IAC9B6B,KAAK,EAAE,GAAG;IACVC,UAAU,EAAGC,MAAM,IAAK;MACtB,MAAMiB,GAAG,GAAGjB,MAAM,CAACK,GAAG,CAACf,aAAa;MACpC,IAAI2B,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,EAAE,OAAOjD,CAAC,CAAC,UAAU,CAAC;MAC3D,MAAMjB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC+D,GAAG,CAAC;MAC/B,MAAME,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACjD,MAAMC,UAAU,GAAG,CAACnD,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/G,oBACExD,OAAA;QAAM+C,KAAK,EAAE;UAAE6D,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAApE,QAAA,gBAC7D1C,OAAA;UAAM+C,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAAN,QAAA,EAAEgE,UAAU,CAACnE,OAAO,GAAG,CAAC;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC/DrD,OAAA;UAAM+C,KAAK,EAAE;YAAEgD,UAAU,EAAE,MAAM;YAAEgB,UAAU,EAAE;UAAE,CAAE;UAAArE,QAAA,EAAEiE,UAAU,CAACpE,OAAO,GAAG,CAAC;QAAC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpFrD,OAAA;UAAM+C,KAAK,EAAE;YAAEE,KAAK,EAAE,MAAM;YAAE8D,UAAU,EAAE;UAAE,CAAE;UAAArE,QAAA,GAAC,GAAC,EAAC8D,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC;IAEX;EACF,CAAC,CACF;EAED,oBACErD,OAAA,CAACnB,GAAG;IAACoI,CAAC,EAAE,CAAE;IAAAvE,QAAA,gBACR1C,OAAA,CAAClB,UAAU;MAAC0G,OAAO,EAAC,IAAI;MAAC0B,EAAE,EAAE,CAAE;MAAAxE,QAAA,gBAC7B1C,OAAA,CAACH,YAAY;QAACmD,QAAQ,EAAC,OAAO;QAAC6C,EAAE,EAAE;UAAEsB,aAAa,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAlE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACxEG,CAAC,CAAC,cAAc,CAAC;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEbrD,OAAA,CAACjB,KAAK;MAAC8G,EAAE,EAAE;QAAEoB,CAAC,EAAE;MAAE,CAAE;MAAAvE,QAAA,eAClB1C,OAAA,CAACnB,GAAG;QAACgH,EAAE,EAAE;UAAEwB,MAAM,EAAE,GAAG;UAAEhC,KAAK,EAAE;QAAO,CAAE;QAAA3C,QAAA,eACtC1C,OAAA,CAACL,QAAQ;UACP2H,IAAI,EAAE3D,SAAU;UAChB4D,OAAO,EAAErC,eAAgB;UACzBsC,QAAQ,EAAE,CAAE;UACZC,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;UAChCC,uBAAuB;QAAA;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRrD,OAAA,CAAChB,MAAM;MACL2I,IAAI,EAAE5D,kBAAmB;MACzB6D,OAAO,EAAEA,CAAA,KAAM5D,qBAAqB,CAAC,KAAK,CAAE;MAC5C6D,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTjC,EAAE,EAAE;QACF,oBAAoB,EAAE;UACpBkC,SAAS,EAAE,MAAM;UACjBC,QAAQ,EAAE,MAAM;UAChBhC,YAAY,EAAE;QAChB;MACF,CAAE;MAAAtD,QAAA,gBAEF1C,OAAA,CAACf,WAAW;QACV4G,EAAE,EAAE;UACFC,UAAU,EAAE,mDAAmD;UAC/D7C,KAAK,EAAE,OAAO;UACd2D,OAAO,EAAE,MAAM;UACfqB,cAAc,EAAE,eAAe;UAC/BpB,UAAU,EAAE,QAAQ;UACpBqB,EAAE,EAAE;QACN,CAAE;QAAAxF,QAAA,gBAEF1C,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAAC0G,OAAO,EAAC,IAAI;YAAC2C,SAAS,EAAC,IAAI;YAACpC,UAAU,EAAC,MAAM;YAAArD,QAAA,GAAC,eACrD,EAACc,CAAC,CAAC,cAAc,CAAC,EAAC,GAAC,EAACK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEc,WAAW;UAAA;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACbrD,OAAA,CAAClB,UAAU;YAAC0G,OAAO,EAAC,OAAO;YAACK,EAAE,EAAE;cAAEuC,OAAO,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAI,CAAE;YAAA3F,QAAA,EACvDmB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEe;UAAY;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNrD,OAAA,CAACZ,UAAU;UAACsG,OAAO,EAAEA,CAAA,KAAM1B,qBAAqB,CAAC,KAAK,CAAE;UAAC6B,EAAE,EAAE;YAAE5C,KAAK,EAAE;UAAQ,CAAE;UAAAP,QAAA,eAC9E1C,OAAA,CAACF,KAAK;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdrD,OAAA,CAACd,aAAa;QAAC2G,EAAE,EAAE;UAAEoB,CAAC,EAAE;QAAE,CAAE;QAAAvE,QAAA,EACvBmB,gBAAgB,GAAG,CAAC,MAAM;UACzB,MAAMhD,OAAO,GAAGF,yBAAyB,CAACkD,gBAAgB,CAAC;UAE3D,IAAIhD,OAAO,CAACuB,MAAM,KAAK,CAAC,EAAE;YACxB,oBACEpC,OAAA,CAAClB,UAAU;cAACmE,KAAK,EAAC,gBAAgB;cAAC4C,EAAE,EAAE;gBAAEyC,SAAS,EAAE,QAAQ;gBAAElC,EAAE,EAAE;cAAE,CAAE;cAAA1D,QAAA,EACnEc,CAAC,CAAC,gBAAgB;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAEjB;;UAEA;UACA,MAAMkF,cAAc,GAAG1H,OAAO,CAC3B+B,GAAG,CAAC4F,EAAE,IAAIlI,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,CAAC,CAC5BwH,MAAM,CAACC,GAAG,IAAI,CAACnI,KAAK,CAACmI,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,CAAC;UACrD,MAAM7D,aAAa,GAAG,CAAAhB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgB,aAAa,MAClD0D,cAAc,CAACnG,MAAM,GAAG,CAAC,GAAGmG,cAAc,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAAGN,cAAc,CAACnG,MAAM,GAAG,CAAC,CAAC;UAErG,MAAMsE,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;UACjD,MAAMC,UAAU,GAAG,CAACnD,CAAC,CAAC,kBAAkB,CAAC,EAAEA,CAAC,CAAC,cAAc,CAAC,EAAEA,CAAC,CAAC,SAAS,CAAC,EAAEA,CAAC,CAAC,WAAW,CAAC,EAAEA,CAAC,CAAC,eAAe,CAAC,CAAC;UAE/G,oBACExD,OAAA,CAAAE,SAAA;YAAAwC,QAAA,GAEGmC,aAAa,GAAG,CAAC,iBAChB7E,OAAA,CAACX,IAAI;cAACwG,EAAE,EAAE;gBAAEqB,EAAE,EAAE,CAAC;gBAAE4B,OAAO,EAAE,cAAc;gBAAE7F,KAAK,EAAE;cAAQ,CAAE;cAAAP,QAAA,eAC3D1C,OAAA,CAACT,WAAW;gBAACsG,EAAE,EAAE;kBAAEyC,SAAS,EAAE;gBAAS,CAAE;gBAAA5F,QAAA,gBACvC1C,OAAA,CAAClB,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACuD,YAAY;kBAAArG,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrD,OAAA,CAAClB,UAAU;kBAAC0G,OAAO,EAAC,IAAI;kBAACO,UAAU,EAAC,MAAM;kBAAArD,QAAA,GACvCmC,aAAa,CAACmC,OAAO,CAAC,CAAC,CAAC,EAAC,IAC5B;gBAAA;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrD,OAAA,CAACnB,GAAG;kBAACgH,EAAE,EAAE;oBAAEe,OAAO,EAAE,MAAM;oBAAEqB,cAAc,EAAE,QAAQ;oBAAEf,EAAE,EAAE;kBAAE,CAAE;kBAAAxE,QAAA,EAC3DJ,WAAW,CAACuC,aAAa;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACNrD,OAAA,CAAClB,UAAU;kBAAC0G,OAAO,EAAC,WAAW;kBAACK,EAAE,EAAE;oBAAEuC,OAAO,EAAE;kBAAI,CAAE;kBAAA1F,QAAA,EAClDiE,UAAU,CAACnE,IAAI,CAACC,KAAK,CAACoC,aAAa,CAAC,GAAG,CAAC;gBAAC;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP,EAIA,CAAC,MAAM;cACN;cACA,MAAM2F,QAAQ,GAAG,CACf;gBACEC,KAAK,EAAEzF,CAAC,CAAC,gBAAgB,CAAC;gBAC1BhD,KAAK,EAAE,IAAI;gBACXyC,KAAK,EAAE,eAAe;gBACtBiG,QAAQ,EAAE,CACR,oBAAoB,EACpB,cAAc,EACd,WAAW,EACX,OAAO,EACP,oBAAoB,EACpB,oBAAoB,EACpB,6BAA6B;cAEjC,CAAC,EACD;gBACED,KAAK,EAAEzF,CAAC,CAAC,gBAAgB,CAAC;gBAC1BhD,KAAK,EAAE,OAAO;gBACdyC,KAAK,EAAE,eAAe;gBACtBiG,QAAQ,EAAE,CAAC,mBAAmB,EAAE,QAAQ,EAAE,eAAe,EAAE,WAAW,EAAE,aAAa,EAAE,+BAA+B;cACxH,CAAC,EACD;gBACED,KAAK,EAAEzF,CAAC,CAAC,aAAa,CAAC;gBACvBhD,KAAK,EAAE,IAAI;gBACXyC,KAAK,EAAE,YAAY;gBACnBiG,QAAQ,EAAE,CAAC,mBAAmB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,4BAA4B;cACjH,CAAC,EACD;gBACED,KAAK,EAAEzF,CAAC,CAAC,oBAAoB,CAAC;gBAC9BhD,KAAK,EAAE,IAAI;gBACXyC,KAAK,EAAE,eAAe;gBACtBiG,QAAQ,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB;cAC5D,CAAC,CACF;;cAED;cACA,SAASC,SAASA,CAACC,GAAG,EAAE;gBACtB,OAAOA,GAAG,CACPC,WAAW,CAAC,CAAC,CACbF,SAAS,CAAC,KAAK,CAAC,CAACG,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;gBAAA,CACjDA,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;cAChC;cAEA,MAAMC,cAAc,GAAG1I,OAAO,CAACuB,MAAM,GAAG,CAAC,GAAG4G,QAAQ,CAACpG,GAAG,CAAC4G,OAAO,KAAK;gBACnE,GAAGA,OAAO;gBACV3I,OAAO,EAAEA,OAAO,CAAC4H,MAAM,CAACD,EAAE,IACxBgB,OAAO,CAACN,QAAQ,CAACO,IAAI,CAACC,OAAO,IAC3BP,SAAS,CAACX,EAAE,CAACxH,QAAQ,CAAC,CAAC2I,QAAQ,CAACR,SAAS,CAACO,OAAO,CAAC,CACpD,CACF;cACF,CAAC,CAAC,CAAC,GAAG,EAAE;;cAER;cACA,MAAME,YAAY,GAAG/I,OAAO,CAACuB,MAAM,GAAG,CAAC,GAAGvB,OAAO,CAAC4H,MAAM,CAACD,EAAE,IACzD,CAACQ,QAAQ,CAACS,IAAI,CAACD,OAAO,IAAIA,OAAO,CAACN,QAAQ,CAACO,IAAI,CAACC,OAAO,IAAIP,SAAS,CAACX,EAAE,CAACxH,QAAQ,CAAC,CAAC2I,QAAQ,CAACR,SAAS,CAACO,OAAO,CAAC,CAAC,CAAC,CACjH,CAAC,GAAG,EAAE;cAEN,oBACJ1J,OAAA,CAAAE,SAAA;gBAAAwC,QAAA,GACG6G,cAAc,CAAC3G,GAAG,CAAC,CAAC4G,OAAO,EAAE9E,GAAG,KAC/B8E,OAAO,CAAC3I,OAAO,CAACuB,MAAM,GAAG,CAAC,iBACxBpC,OAAA,CAACX,IAAI;kBAAWwG,EAAE,EAAE;oBAAEqB,EAAE,EAAE;kBAAE,CAAE;kBAAAxE,QAAA,gBAC5B1C,OAAA,CAACV,UAAU;oBACTuG,EAAE,EAAE;sBAAEiD,OAAO,EAAEU,OAAO,CAACvG,KAAK;sBAAEA,KAAK,EAAE;oBAAQ,CAAE;oBAC/CgG,KAAK,eACHjJ,OAAA,CAACnB,GAAG;sBAACgH,EAAE,EAAE;wBAAEe,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBACzD1C,OAAA,CAAClB,UAAU;wBAAC+G,EAAE,EAAE;0BAAE7C,QAAQ,EAAE;wBAAS,CAAE;wBAAAN,QAAA,EAAE8G,OAAO,CAAChJ;sBAAK;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eACpErD,OAAA,CAAClB,UAAU;wBAAC0G,OAAO,EAAC,IAAI;wBAAA9C,QAAA,EAAE8G,OAAO,CAACP;sBAAK;wBAAA/F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFrD,OAAA,CAACT,WAAW;oBAAAmD,QAAA,eACV1C,OAAA,CAACR,IAAI;sBAACqK,SAAS;sBAACC,OAAO,EAAE,CAAE;sBAAApH,QAAA,EACxB8G,OAAO,CAAC3I,OAAO,CAAC+B,GAAG,CAAC,CAAC4F,EAAE,EAAEuB,IAAI,KAAK;wBACjC,IAAIC,SAAS,GAAG,CAACzJ,KAAK,CAACD,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,CAAC,IAAIX,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,IAAI,CAAC,IAAIX,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,IAAI,CAAC;wBAC7F,IAAIgJ,KAAK,GAAGD,SAAS,GAAG1J,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,GAAG,IAAI;wBAChD,oBACEjB,OAAA,CAACR,IAAI;0BAAC0K,IAAI;0BAACC,EAAE,EAAE,EAAG;0BAACC,EAAE,EAAEJ,SAAS,GAAG,CAAC,GAAG,EAAG;0BAAAtH,QAAA,eACxC1C,OAAA,CAACnB,GAAG;4BAACgH,EAAE,EAAE;8BAAEoB,CAAC,EAAE,CAAC;8BAAE6B,OAAO,EAAE,SAAS;8BAAE9C,YAAY,EAAE;4BAAE,CAAE;4BAAAtD,QAAA,gBACrD1C,OAAA,CAAClB,UAAU;8BAAC0G,OAAO,EAAC,OAAO;8BAACO,UAAU,EAAC,KAAK;8BAACgD,YAAY;8BAAArG,QAAA,EACtD8F,EAAE,CAACxH;4BAAQ;8BAAAkC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,EACZ2G,SAAS,gBACRhK,OAAA,CAACnB,GAAG;8BAACgH,EAAE,EAAE;gCAAEe,OAAO,EAAE,MAAM;gCAAEC,UAAU,EAAE,QAAQ;gCAAEC,GAAG,EAAE;8BAAE,CAAE;8BAAApE,QAAA,eACzD1C,OAAA,CAACnB,GAAG;gCAACgH,EAAE,EAAE;kCAAEe,OAAO,EAAE,MAAM;kCAAEC,UAAU,EAAE,QAAQ;kCAAEC,GAAG,EAAE;gCAAE,CAAE;gCAAApE,QAAA,GACxD8F,EAAE,CAAChI,KAAK,iBACPR,OAAA,CAAClB,UAAU;kCAAC+G,EAAE,EAAE;oCAAE7C,QAAQ,EAAE;kCAAS,CAAE;kCAAAN,QAAA,EACpC8F,EAAE,CAAChI;gCAAK;kCAAA0C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC,CACb,eACDrD,OAAA,CAAClB,UAAU;kCAAC0G,OAAO,EAAC,OAAO;kCAACO,UAAU,EAAC,KAAK;kCAAArD,QAAA,EACzC8F,EAAE,CAAC/H,OAAO,IAAIkG,UAAU,CAACsD,KAAK,GAAG,CAAC;gCAAC;kCAAA/G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACbrD,OAAA,CAAClB,UAAU;kCAAC0G,OAAO,EAAC,SAAS;kCAACvC,KAAK,EAAC,gBAAgB;kCAAC4C,EAAE,EAAE;oCAAEwE,EAAE,EAAE;kCAAE,CAAE;kCAAA3H,QAAA,GAAC,GACjE,EAACuH,KAAK,EAAC,KACV;gCAAA;kCAAA/G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAENrD,OAAA,CAAClB,UAAU;8BAAC0G,OAAO,EAAC,OAAO;8BAACzC,KAAK,EAAE;gCAAEuH,UAAU,EAAE;8BAAW,CAAE;8BAAA5H,QAAA,EAC3D8F,EAAE,CAACvH,MAAM,IAAIuC,CAAC,CAAC,UAAU;4BAAC;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC,GA1BwC0G,IAAI;0BAAA7G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA2B9C,CAAC;sBAEX,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA,GA/CLqB,GAAG;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgDR,CAEV,CAAC,EACAuG,YAAY,CAACxH,MAAM,GAAG,CAAC,iBACtBpC,OAAA,CAACX,IAAI;kBAACwG,EAAE,EAAE;oBAAEqB,EAAE,EAAE;kBAAE,CAAE;kBAAAxE,QAAA,gBAClB1C,OAAA,CAACV,UAAU;oBACTuG,EAAE,EAAE;sBAAEiD,OAAO,EAAE,UAAU;sBAAE7F,KAAK,EAAE;oBAAQ,CAAE;oBAC5CgG,KAAK,eACHjJ,OAAA,CAACnB,GAAG;sBAACgH,EAAE,EAAE;wBAAEe,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEC,GAAG,EAAE;sBAAE,CAAE;sBAAApE,QAAA,gBACzD1C,OAAA,CAAClB,UAAU;wBAAC+G,EAAE,EAAE;0BAAE7C,QAAQ,EAAE;wBAAS,CAAE;wBAAAN,QAAA,EAAC;sBAAE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACvDrD,OAAA,CAAClB,UAAU;wBAAC0G,OAAO,EAAC,IAAI;wBAAA9C,QAAA,EAAEc,CAAC,CAAC,cAAc;sBAAC;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFrD,OAAA,CAACT,WAAW;oBAAAmD,QAAA,eACV1C,OAAA,CAACR,IAAI;sBAACqK,SAAS;sBAACC,OAAO,EAAE,CAAE;sBAAApH,QAAA,EACxBkH,YAAY,CAAChH,GAAG,CAAC,CAAC4F,EAAE,EAAEuB,IAAI,KAAK;wBAC9B,IAAIC,SAAS,GAAG,CAACzJ,KAAK,CAACD,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,CAAC,IAAIX,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,IAAI,CAAC,IAAIX,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,IAAI,CAAC;wBAC7F,IAAIgJ,KAAK,GAAGD,SAAS,GAAG1J,MAAM,CAACkI,EAAE,CAACvH,MAAM,CAAC,GAAG,IAAI;wBAChD,oBACEjB,OAAA,CAACR,IAAI;0BAAC0K,IAAI;0BAACC,EAAE,EAAE,EAAG;0BAACC,EAAE,EAAEJ,SAAS,GAAG,CAAC,GAAG,EAAG;0BAAAtH,QAAA,eACxC1C,OAAA,CAACnB,GAAG;4BAACgH,EAAE,EAAE;8BAAEoB,CAAC,EAAE,CAAC;8BAAE6B,OAAO,EAAE,SAAS;8BAAE9C,YAAY,EAAE;4BAAE,CAAE;4BAAAtD,QAAA,gBACrD1C,OAAA,CAAClB,UAAU;8BAAC0G,OAAO,EAAC,OAAO;8BAACO,UAAU,EAAC,KAAK;8BAACgD,YAAY;8BAAArG,QAAA,EACtD8F,EAAE,CAACxH;4BAAQ;8BAAAkC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF,CAAC,EACZ2G,SAAS,gBACRhK,OAAA,CAACnB,GAAG;8BAACgH,EAAE,EAAE;gCAAEe,OAAO,EAAE,MAAM;gCAAEC,UAAU,EAAE,QAAQ;gCAAEC,GAAG,EAAE;8BAAE,CAAE;8BAAApE,QAAA,eACzD1C,OAAA,CAACnB,GAAG;gCAACgH,EAAE,EAAE;kCAAEe,OAAO,EAAE,MAAM;kCAAEC,UAAU,EAAE,QAAQ;kCAAEC,GAAG,EAAE;gCAAE,CAAE;gCAAApE,QAAA,GACxD8F,EAAE,CAAChI,KAAK,iBACPR,OAAA,CAAClB,UAAU;kCAAC+G,EAAE,EAAE;oCAAE7C,QAAQ,EAAE;kCAAS,CAAE;kCAAAN,QAAA,EACpC8F,EAAE,CAAChI;gCAAK;kCAAA0C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACC,CACb,eACDrD,OAAA,CAAClB,UAAU;kCAAC0G,OAAO,EAAC,OAAO;kCAACO,UAAU,EAAC,KAAK;kCAAArD,QAAA,EACzC8F,EAAE,CAAC/H,OAAO,IAAIkG,UAAU,CAACsD,KAAK,GAAG,CAAC;gCAAC;kCAAA/G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1B,CAAC,eACbrD,OAAA,CAAClB,UAAU;kCAAC0G,OAAO,EAAC,SAAS;kCAACvC,KAAK,EAAC,gBAAgB;kCAAC4C,EAAE,EAAE;oCAAEwE,EAAE,EAAE;kCAAE,CAAE;kCAAA3H,QAAA,GAAC,GACjE,EAACuH,KAAK,EAAC,KACV;gCAAA;kCAAA/G,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACV;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,gBAENrD,OAAA,CAAClB,UAAU;8BAAC0G,OAAO,EAAC,OAAO;8BAACzC,KAAK,EAAE;gCAAEuH,UAAU,EAAE;8BAAW,CAAE;8BAAA5H,QAAA,EAC3D8F,EAAE,CAACvH,MAAM,IAAIuC,CAAC,CAAC,UAAU;4BAAC;8BAAAN,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CACb;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE;wBAAC,GA1BwC0G,IAAI;0BAAA7G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA2B9C,CAAC;sBAEX,CAAC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACP;cAAA,eACG,CAAC;YAEL,CAAC,EAAE,CAAC;UAAA,eACJ,CAAC;QAEP,CAAC,EAAE,CAAC,gBACFrD,OAAA,CAAClB,UAAU;UAACmE,KAAK,EAAC,gBAAgB;UAAC4C,EAAE,EAAE;YAAEyC,SAAS,EAAE,QAAQ;YAAElC,EAAE,EAAE;UAAE,CAAE;UAAA1D,QAAA,EAAC;QAEvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,CA9XID,YAAY;EAAA,QACF7D,cAAc,EACHb,SAAS;AAAA;AAAA2L,EAAA,GAF9BjH,YAAY;AAgYlB,eAAeA,YAAY;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}