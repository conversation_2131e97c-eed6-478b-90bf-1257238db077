{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mka-lms-2025\\\\frontend\\\\src\\\\components\\\\Whiteboard.js\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from \"react\";\nimport { io } from \"socket.io-client\";\nimport { useTranslation } from \"react-i18next\";\nimport { Box, Paper, IconButton, Tooltip, Stack, Divider, Fade } from \"@mui/material\";\nimport EditIcon from \"@mui/icons-material/Edit\";\nimport TextFieldsIcon from \"@mui/icons-material/TextFields\";\nimport TableChartIcon from \"@mui/icons-material/TableChart\";\nimport UndoIcon from \"@mui/icons-material/Undo\";\nimport RedoIcon from \"@mui/icons-material/Redo\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SOCKET_URL = \"http://localhost:8000/whiteboard\";\nconst TOOLS = {\n  PEN: \"pen\",\n  TEXT: \"text\",\n  TABLE: \"table\"\n};\nexport default function Whiteboard({\n  seanceId,\n  userId\n}) {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const canvasRef = useRef(null);\n  const [socket, setSocket] = useState(null);\n  const [tool, setTool] = useState(TOOLS.PEN);\n  const [drawing, setDrawing] = useState(false);\n  const [actions, setActions] = useState([]);\n  const [color, setColor] = useState(\"#1976d2\");\n  const [currentText, setCurrentText] = useState(\"\");\n  const [showTextInput, setShowTextInput] = useState(false);\n  const [textPos, setTextPos] = useState({\n    x: 0,\n    y: 0\n  });\n  const [undoStack, setUndoStack] = useState([]);\n  useEffect(() => {\n    const s = io(SOCKET_URL, {\n      transports: [\"websocket\"]\n    });\n    setSocket(s);\n    s.on(\"connect\", () => {\n      console.log(\"✅ Socket.io connecté à\", SOCKET_URL);\n    });\n    s.on(\"connect_error\", err => {\n      console.error(\"❌ Erreur de connexion socket.io :\", err);\n    });\n    s.emit(\"join-seance\", seanceId);\n    s.on(\"whiteboard-sync\", actions => setActions(actions));\n    s.on(\"whiteboard-action\", action => setActions(prev => [...prev, action]));\n    return () => s.disconnect();\n  }, [seanceId]);\n  const handlePointerDown = e => {\n    if (tool === TOOLS.PEN) {\n      setDrawing(true);\n      const x = e.nativeEvent.offsetX;\n      const y = e.nativeEvent.offsetY;\n      const newStroke = {\n        type: \"draw\",\n        data: {\n          color,\n          points: [[x, y]]\n        },\n        seanceId,\n        createdById: userId\n      };\n      setActions(prev => [...prev, newStroke]);\n      if (socket) socket.emit(\"whiteboard-action\", newStroke);\n    }\n    if (tool === TOOLS.TEXT) {\n      const x = e.nativeEvent.offsetX;\n      const y = e.nativeEvent.offsetY;\n      setTextPos({\n        x,\n        y\n      });\n      setShowTextInput(true);\n    }\n    if (tool === TOOLS.TABLE) {\n      const x = e.nativeEvent.offsetX;\n      const y = e.nativeEvent.offsetY;\n      const newTable = {\n        type: \"table\",\n        data: {\n          x,\n          y,\n          rows: 3,\n          cols: 3\n        },\n        seanceId,\n        createdById: userId\n      };\n      setActions(prev => [...prev, newTable]);\n      if (socket) socket.emit(\"whiteboard-action\", newTable);\n    }\n  };\n  const handlePointerMove = e => {\n    if (!drawing || tool !== TOOLS.PEN) return;\n    const x = e.nativeEvent.offsetX;\n    const y = e.nativeEvent.offsetY;\n    setActions(prev => {\n      const last = prev[prev.length - 1];\n      if (last && last.type === \"draw\") {\n        last.data.points.push([x, y]);\n        return [...prev.slice(0, -1), last];\n      }\n      return prev;\n    });\n  };\n  const handlePointerUp = () => {\n    setDrawing(false);\n  };\n  const handleTextSubmit = e => {\n    e.preventDefault();\n    if (!currentText.trim()) return;\n    const newText = {\n      type: \"text\",\n      data: {\n        x: textPos.x,\n        y: textPos.y,\n        value: currentText,\n        color\n      },\n      seanceId,\n      createdById: userId\n    };\n    setActions(prev => [...prev, newText]);\n    if (socket) socket.emit(\"whiteboard-action\", newText);\n    setCurrentText(\"\");\n    setShowTextInput(false);\n  };\n  const handleUndo = () => {\n    if (actions.length === 0) return;\n    setUndoStack(prev => [...prev, actions[actions.length - 1]]);\n    setActions(prev => prev.slice(0, -1));\n  };\n  const handleRedo = () => {\n    if (undoStack.length === 0) return;\n    setActions(prev => [...prev, undoStack[undoStack.length - 1]]);\n    setUndoStack(prev => prev.slice(0, -1));\n  };\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext(\"2d\");\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n    const gridSize = 24;\n    ctx.save();\n    ctx.strokeStyle = \"#e9e9f1\";\n    ctx.lineWidth = 1;\n    for (let x = 0; x < canvas.width; x += gridSize) {\n      ctx.beginPath();\n      ctx.moveTo(x, 0);\n      ctx.lineTo(x, canvas.height);\n      ctx.stroke();\n    }\n    for (let y = 0; y < canvas.height; y += gridSize) {\n      ctx.beginPath();\n      ctx.moveTo(0, y);\n      ctx.lineTo(canvas.width, y);\n      ctx.stroke();\n    }\n    ctx.restore();\n    actions.forEach(action => {\n      if (action.type === \"draw\") {\n        const {\n          color,\n          points\n        } = action.data;\n        ctx.strokeStyle = color;\n        ctx.lineWidth = 3;\n        ctx.lineCap = \"round\";\n        ctx.beginPath();\n        points.forEach(([x, y], i) => {\n          if (i === 0) ctx.moveTo(x, y);else ctx.lineTo(x, y);\n        });\n        ctx.stroke();\n      }\n      if (action.type === \"text\") {\n        const {\n          x,\n          y,\n          value,\n          color\n        } = action.data;\n        ctx.font = \"bold 20px Poppins, Arial\";\n        ctx.fillStyle = color || \"#222\";\n        ctx.fillText(value, x, y);\n      }\n      if (action.type === \"table\") {\n        const {\n          x,\n          y,\n          rows,\n          cols\n        } = action.data;\n        const cellSize = 44;\n        ctx.save();\n        ctx.strokeStyle = \"#b8b8d4\";\n        ctx.lineWidth = 1.5;\n        for (let i = 0; i <= rows; i++) {\n          ctx.beginPath();\n          ctx.moveTo(x, y + i * cellSize);\n          ctx.lineTo(x + cols * cellSize, y + i * cellSize);\n          ctx.stroke();\n        }\n        for (let j = 0; j <= cols; j++) {\n          ctx.beginPath();\n          ctx.moveTo(x + j * cellSize, y);\n          ctx.lineTo(x + j * cellSize, y + rows * cellSize);\n          ctx.stroke();\n        }\n        ctx.restore();\n      }\n    });\n  }, [actions]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3,\n      background: \"#f6f9ff\",\n      borderRadius: 5,\n      boxShadow: 3,\n      minHeight: 650,\n      width: \"100%\",\n      display: \"flex\",\n      flexDirection: \"column\",\n      alignItems: \"flex-start\",\n      position: \"relative\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      alignItems: \"center\",\n      spacing: 1,\n      mb: 2,\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"https://cdn-icons-png.flaticon.com/512/2659/2659360.png\",\n        alt: \"\",\n        style: {\n          width: 38,\n          marginRight: 7\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          fontSize: 32,\n          fontWeight: 700,\n          fontFamily: \"Poppins, Arial\"\n        },\n        children: t('whiteboard.collaborativeWhiteboard')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 5,\n      sx: {\n        position: \"absolute\",\n        left: 18,\n        top: 88,\n        zIndex: 10,\n        p: 1,\n        borderRadius: 4,\n        background: \"#fff\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        gap: 0.5,\n        boxShadow: \"0 4px 18px #dde2f3bb\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: t('whiteboard.tools.pen'),\n        placement: \"right\",\n        TransitionComponent: Fade,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: tool === TOOLS.PEN ? \"primary\" : \"default\",\n          onClick: () => setTool(TOOLS.PEN),\n          children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: t('whiteboard.tools.text'),\n        placement: \"right\",\n        TransitionComponent: Fade,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: tool === TOOLS.TEXT ? \"primary\" : \"default\",\n          onClick: () => setTool(TOOLS.TEXT),\n          children: /*#__PURE__*/_jsxDEV(TextFieldsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: t('whiteboard.tools.table'),\n        placement: \"right\",\n        TransitionComponent: Fade,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          color: tool === TOOLS.TABLE ? \"primary\" : \"default\",\n          onClick: () => setTool(TOOLS.TABLE),\n          children: /*#__PURE__*/_jsxDEV(TableChartIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 1,\n          width: 26\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: t('whiteboard.tools.color'),\n        placement: \"right\",\n        TransitionComponent: Fade,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"color\",\n            value: color,\n            onChange: e => setColor(e.target.value),\n            style: {\n              width: 34,\n              height: 34,\n              border: \"none\",\n              borderRadius: 8,\n              cursor: \"pointer\",\n              background: \"none\",\n              padding: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 1,\n          width: 26\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: t('whiteboard.tools.undo'),\n        placement: \"right\",\n        TransitionComponent: Fade,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleUndo,\n          disabled: actions.length === 0,\n          children: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: t('whiteboard.tools.redo'),\n        placement: \"right\",\n        TransitionComponent: Fade,\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleRedo,\n          disabled: undoStack.length === 0,\n          children: /*#__PURE__*/_jsxDEV(RedoIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      width: 900,\n      height: 500,\n      onPointerDown: handlePointerDown,\n      onPointerMove: handlePointerMove,\n      onPointerUp: handlePointerUp,\n      style: {\n        border: \"2px solid #e1e8f5\",\n        borderRadius: 12,\n        cursor: tool === TOOLS.PEN ? \"crosshair\" : \"default\",\n        marginLeft: 80,\n        background: \"#fff\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), showTextInput && /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleTextSubmit,\n      sx: {\n        position: \"absolute\",\n        left: textPos.x + 80,\n        top: textPos.y + 140,\n        zIndex: 20\n      },\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: currentText,\n        onChange: e => setCurrentText(e.target.value),\n        onBlur: () => setShowTextInput(false),\n        autoFocus: true,\n        style: {\n          border: \"2px solid #1976d2\",\n          borderRadius: 6,\n          padding: \"8px 12px\",\n          fontSize: 16,\n          fontFamily: \"Poppins, Arial\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n}\n_s(Whiteboard, \"cMyLn5SQq6QcOpY748rYdCvd9Xc=\", false, function () {\n  return [useTranslation];\n});\n_c = Whiteboard;\nvar _c;\n$RefreshReg$(_c, \"Whiteboard\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "io", "useTranslation", "Box", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Divider", "Fade", "EditIcon", "TextFieldsIcon", "TableChartIcon", "UndoIcon", "RedoIcon", "jsxDEV", "_jsxDEV", "SOCKET_URL", "TOOLS", "PEN", "TEXT", "TABLE", "Whiteboard", "seanceId", "userId", "_s", "t", "canvasRef", "socket", "setSocket", "tool", "setTool", "drawing", "setDrawing", "actions", "setActions", "color", "setColor", "currentText", "setCurrentText", "showTextInput", "setShowTextInput", "textPos", "setTextPos", "x", "y", "undoStack", "setUndoStack", "s", "transports", "on", "console", "log", "err", "error", "emit", "action", "prev", "disconnect", "handlePointerDown", "e", "nativeEvent", "offsetX", "offsetY", "newStroke", "type", "data", "points", "createdById", "newTable", "rows", "cols", "handlePointerMove", "last", "length", "push", "slice", "handlePointerUp", "handleTextSubmit", "preventDefault", "trim", "newText", "value", "handleUndo", "handleRedo", "canvas", "current", "ctx", "getContext", "clearRect", "width", "height", "gridSize", "save", "strokeStyle", "lineWidth", "beginPath", "moveTo", "lineTo", "stroke", "restore", "for<PERSON>ach", "lineCap", "i", "font", "fillStyle", "fillText", "cellSize", "j", "sx", "p", "background", "borderRadius", "boxShadow", "minHeight", "display", "flexDirection", "alignItems", "position", "children", "direction", "spacing", "mb", "src", "alt", "style", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "fontFamily", "elevation", "left", "top", "zIndex", "gap", "title", "placement", "TransitionComponent", "onClick", "my", "onChange", "target", "border", "cursor", "padding", "disabled", "ref", "onPointerDown", "onPointerMove", "onPointerUp", "marginLeft", "component", "onSubmit", "onBlur", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/mka-lms-2025/frontend/src/components/Whiteboard.js"], "sourcesContent": ["import React, { useRef, useEffect, useState } from \"react\";\nimport { io } from \"socket.io-client\";\nimport { useTranslation } from \"react-i18next\";\nimport {\n  Box,\n  Paper,\n  IconButton,\n  Tooltip,\n  Stack,\n  Divider,\n  Fade\n} from \"@mui/material\";\nimport EditIcon from \"@mui/icons-material/Edit\";\nimport TextFieldsIcon from \"@mui/icons-material/TextFields\";\nimport TableChartIcon from \"@mui/icons-material/TableChart\";\nimport UndoIcon from \"@mui/icons-material/Undo\";\nimport RedoIcon from \"@mui/icons-material/Redo\";\n\nconst SOCKET_URL = \"http://localhost:8000/whiteboard\";\n\nconst TOOLS = {\n  PEN: \"pen\",\n  TEXT: \"text\",\n  TABLE: \"table\",\n};\n\nexport default function Whiteboard({ seanceId, userId }) {\n  const { t } = useTranslation();\n  const canvasRef = useRef(null);\n  const [socket, setSocket] = useState(null);\n  const [tool, setTool] = useState(TOOLS.PEN);\n  const [drawing, setDrawing] = useState(false);\n  const [actions, setActions] = useState([]);\n  const [color, setColor] = useState(\"#1976d2\");\n  const [currentText, setCurrentText] = useState(\"\");\n  const [showTextInput, setShowTextInput] = useState(false);\n  const [textPos, setTextPos] = useState({ x: 0, y: 0 });\n  const [undoStack, setUndoStack] = useState([]);\n\n  useEffect(() => {\n    const s = io(SOCKET_URL, { transports: [\"websocket\"] });\n    setSocket(s);\n\n    s.on(\"connect\", () => {\n      console.log(\"✅ Socket.io connecté à\", SOCKET_URL);\n    });\n    s.on(\"connect_error\", (err) => {\n      console.error(\"❌ Erreur de connexion socket.io :\", err);\n    });\n\n    s.emit(\"join-seance\", seanceId);\n\n    s.on(\"whiteboard-sync\", (actions) => setActions(actions));\n    s.on(\"whiteboard-action\", (action) => setActions((prev) => [...prev, action]));\n\n    return () => s.disconnect();\n  }, [seanceId]);\n\n  const handlePointerDown = (e) => {\n    if (tool === TOOLS.PEN) {\n      setDrawing(true);\n      const x = e.nativeEvent.offsetX;\n      const y = e.nativeEvent.offsetY;\n      const newStroke = { type: \"draw\", data: { color, points: [[x, y]] }, seanceId, createdById: userId };\n      setActions((prev) => [...prev, newStroke]);\n      if (socket) socket.emit(\"whiteboard-action\", newStroke);\n    }\n    if (tool === TOOLS.TEXT) {\n      const x = e.nativeEvent.offsetX;\n      const y = e.nativeEvent.offsetY;\n      setTextPos({ x, y });\n      setShowTextInput(true);\n    }\n    if (tool === TOOLS.TABLE) {\n      const x = e.nativeEvent.offsetX;\n      const y = e.nativeEvent.offsetY;\n      const newTable = {\n        type: \"table\",\n        data: { x, y, rows: 3, cols: 3 },\n        seanceId,\n        createdById: userId,\n      };\n      setActions((prev) => [...prev, newTable]);\n      if (socket) socket.emit(\"whiteboard-action\", newTable);\n    }\n  };\n\n  const handlePointerMove = (e) => {\n    if (!drawing || tool !== TOOLS.PEN) return;\n    const x = e.nativeEvent.offsetX;\n    const y = e.nativeEvent.offsetY;\n    setActions((prev) => {\n      const last = prev[prev.length - 1];\n      if (last && last.type === \"draw\") {\n        last.data.points.push([x, y]);\n        return [...prev.slice(0, -1), last];\n      }\n      return prev;\n    });\n  };\n\n  const handlePointerUp = () => {\n    setDrawing(false);\n  };\n\n  const handleTextSubmit = (e) => {\n    e.preventDefault();\n    if (!currentText.trim()) return;\n    const newText = {\n      type: \"text\",\n      data: { x: textPos.x, y: textPos.y, value: currentText, color },\n      seanceId,\n      createdById: userId,\n    };\n    setActions((prev) => [...prev, newText]);\n    if (socket) socket.emit(\"whiteboard-action\", newText);\n    setCurrentText(\"\");\n    setShowTextInput(false);\n  };\n\n  const handleUndo = () => {\n    if (actions.length === 0) return;\n    setUndoStack((prev) => [...prev, actions[actions.length - 1]]);\n    setActions((prev) => prev.slice(0, -1));\n  };\n\n  const handleRedo = () => {\n    if (undoStack.length === 0) return;\n    setActions((prev) => [...prev, undoStack[undoStack.length - 1]]);\n    setUndoStack((prev) => prev.slice(0, -1));\n  };\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext(\"2d\");\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    const gridSize = 24;\n    ctx.save();\n    ctx.strokeStyle = \"#e9e9f1\";\n    ctx.lineWidth = 1;\n    for (let x = 0; x < canvas.width; x += gridSize) {\n      ctx.beginPath();\n      ctx.moveTo(x, 0);\n      ctx.lineTo(x, canvas.height);\n      ctx.stroke();\n    }\n    for (let y = 0; y < canvas.height; y += gridSize) {\n      ctx.beginPath();\n      ctx.moveTo(0, y);\n      ctx.lineTo(canvas.width, y);\n      ctx.stroke();\n    }\n    ctx.restore();\n\n    actions.forEach((action) => {\n      if (action.type === \"draw\") {\n        const { color, points } = action.data;\n        ctx.strokeStyle = color;\n        ctx.lineWidth = 3;\n        ctx.lineCap = \"round\";\n        ctx.beginPath();\n        points.forEach(([x, y], i) => {\n          if (i === 0) ctx.moveTo(x, y);\n          else ctx.lineTo(x, y);\n        });\n        ctx.stroke();\n      }\n      if (action.type === \"text\") {\n        const { x, y, value, color } = action.data;\n        ctx.font = \"bold 20px Poppins, Arial\";\n        ctx.fillStyle = color || \"#222\";\n        ctx.fillText(value, x, y);\n      }\n      if (action.type === \"table\") {\n        const { x, y, rows, cols } = action.data;\n        const cellSize = 44;\n        ctx.save();\n        ctx.strokeStyle = \"#b8b8d4\";\n        ctx.lineWidth = 1.5;\n        for (let i = 0; i <= rows; i++) {\n          ctx.beginPath();\n          ctx.moveTo(x, y + i * cellSize);\n          ctx.lineTo(x + cols * cellSize, y + i * cellSize);\n          ctx.stroke();\n        }\n        for (let j = 0; j <= cols; j++) {\n          ctx.beginPath();\n          ctx.moveTo(x + j * cellSize, y);\n          ctx.lineTo(x + j * cellSize, y + rows * cellSize);\n          ctx.stroke();\n        }\n        ctx.restore();\n      }\n    });\n  }, [actions]);\n\n  return (\n    <Box\n      sx={{\n        p: 3,\n        background: \"#f6f9ff\",\n        borderRadius: 5,\n        boxShadow: 3,\n        minHeight: 650,\n        width: \"100%\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"flex-start\",\n        position: \"relative\",\n      }}\n    >\n      <Stack direction=\"row\" alignItems=\"center\" spacing={1} mb={2}>\n        <img\n          src=\"https://cdn-icons-png.flaticon.com/512/2659/2659360.png\"\n          alt=\"\"\n          style={{ width: 38, marginRight: 7 }}\n        />\n        <span style={{ fontSize: 32, fontWeight: 700, fontFamily: \"Poppins, Arial\" }}>\n          {t('whiteboard.collaborativeWhiteboard')}\n        </span>\n      </Stack>\n      \n      <Paper\n        elevation={5}\n        sx={{\n          position: \"absolute\",\n          left: 18,\n          top: 88,\n          zIndex: 10,\n          p: 1,\n          borderRadius: 4,\n          background: \"#fff\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\",\n          gap: 0.5,\n          boxShadow: \"0 4px 18px #dde2f3bb\",\n        }}\n      >\n        <Tooltip title={t('whiteboard.tools.pen')} placement=\"right\" TransitionComponent={Fade}>\n          <IconButton color={tool === TOOLS.PEN ? \"primary\" : \"default\"} onClick={() => setTool(TOOLS.PEN)}>\n            <EditIcon />\n          </IconButton>\n        </Tooltip>\n        <Tooltip title={t('whiteboard.tools.text')} placement=\"right\" TransitionComponent={Fade}>\n          <IconButton color={tool === TOOLS.TEXT ? \"primary\" : \"default\"} onClick={() => setTool(TOOLS.TEXT)}>\n            <TextFieldsIcon />\n          </IconButton>\n        </Tooltip>\n        <Tooltip title={t('whiteboard.tools.table')} placement=\"right\" TransitionComponent={Fade}>\n          <IconButton color={tool === TOOLS.TABLE ? \"primary\" : \"default\"} onClick={() => setTool(TOOLS.TABLE)}>\n            <TableChartIcon />\n          </IconButton>\n        </Tooltip>\n        <Divider sx={{ my: 1, width: 26 }} />\n        <Tooltip title={t('whiteboard.tools.color')} placement=\"right\" TransitionComponent={Fade}>\n          <Box>\n            <input\n              type=\"color\"\n              value={color}\n              onChange={(e) => setColor(e.target.value)}\n              style={{\n                width: 34,\n                height: 34,\n                border: \"none\",\n                borderRadius: 8,\n                cursor: \"pointer\",\n                background: \"none\",\n                padding: 0,\n              }}\n            />\n          </Box>\n        </Tooltip>\n        <Divider sx={{ my: 1, width: 26 }} />\n        <Tooltip title={t('whiteboard.tools.undo')} placement=\"right\" TransitionComponent={Fade}>\n          <IconButton onClick={handleUndo} disabled={actions.length === 0}>\n            <UndoIcon />\n          </IconButton>\n        </Tooltip>\n        <Tooltip title={t('whiteboard.tools.redo')} placement=\"right\" TransitionComponent={Fade}>\n          <IconButton onClick={handleRedo} disabled={undoStack.length === 0}>\n            <RedoIcon />\n          </IconButton>\n        </Tooltip>\n      </Paper>\n\n      <canvas\n        ref={canvasRef}\n        width={900}\n        height={500}\n        onPointerDown={handlePointerDown}\n        onPointerMove={handlePointerMove}\n        onPointerUp={handlePointerUp}\n        style={{\n          border: \"2px solid #e1e8f5\",\n          borderRadius: 12,\n          cursor: tool === TOOLS.PEN ? \"crosshair\" : \"default\",\n          marginLeft: 80,\n          background: \"#fff\",\n        }}\n      />\n\n      {showTextInput && (\n        <Box\n          component=\"form\"\n          onSubmit={handleTextSubmit}\n          sx={{\n            position: \"absolute\",\n            left: textPos.x + 80,\n            top: textPos.y + 140,\n            zIndex: 20,\n          }}\n        >\n          <input\n            type=\"text\"\n            value={currentText}\n            onChange={(e) => setCurrentText(e.target.value)}\n            onBlur={() => setShowTextInput(false)}\n            autoFocus\n            style={{\n              border: \"2px solid #1976d2\",\n              borderRadius: 6,\n              padding: \"8px 12px\",\n              fontSize: 16,\n              fontFamily: \"Poppins, Arial\",\n            }}\n          />\n        </Box>\n      )}\n    </Box>\n  );\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SAASC,cAAc,QAAQ,eAAe;AAC9C,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,QAAQ,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,UAAU,GAAG,kCAAkC;AAErD,MAAMC,KAAK,GAAG;EACZC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACT,CAAC;AAED,eAAe,SAASC,UAAUA,CAAC;EAAEC,QAAQ;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EACvD,MAAM;IAAEC;EAAE,CAAC,GAAGxB,cAAc,CAAC,CAAC;EAC9B,MAAMyB,SAAS,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAG/B,QAAQ,CAACkB,KAAK,CAACC,GAAG,CAAC;EAC3C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,SAAS,CAAC;EAC7C,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC;IAAE4C,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EACtD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACd,MAAMiD,CAAC,GAAG/C,EAAE,CAACgB,UAAU,EAAE;MAAEgC,UAAU,EAAE,CAAC,WAAW;IAAE,CAAC,CAAC;IACvDpB,SAAS,CAACmB,CAAC,CAAC;IAEZA,CAAC,CAACE,EAAE,CAAC,SAAS,EAAE,MAAM;MACpBC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEnC,UAAU,CAAC;IACnD,CAAC,CAAC;IACF+B,CAAC,CAACE,EAAE,CAAC,eAAe,EAAGG,GAAG,IAAK;MAC7BF,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAED,GAAG,CAAC;IACzD,CAAC,CAAC;IAEFL,CAAC,CAACO,IAAI,CAAC,aAAa,EAAEhC,QAAQ,CAAC;IAE/ByB,CAAC,CAACE,EAAE,CAAC,iBAAiB,EAAGhB,OAAO,IAAKC,UAAU,CAACD,OAAO,CAAC,CAAC;IACzDc,CAAC,CAACE,EAAE,CAAC,mBAAmB,EAAGM,MAAM,IAAKrB,UAAU,CAAEsB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAED,MAAM,CAAC,CAAC,CAAC;IAE9E,OAAO,MAAMR,CAAC,CAACU,UAAU,CAAC,CAAC;EAC7B,CAAC,EAAE,CAACnC,QAAQ,CAAC,CAAC;EAEd,MAAMoC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,IAAI9B,IAAI,KAAKZ,KAAK,CAACC,GAAG,EAAE;MACtBc,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,CAAC,GAAGgB,CAAC,CAACC,WAAW,CAACC,OAAO;MAC/B,MAAMjB,CAAC,GAAGe,CAAC,CAACC,WAAW,CAACE,OAAO;MAC/B,MAAMC,SAAS,GAAG;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAE;UAAE9B,KAAK;UAAE+B,MAAM,EAAE,CAAC,CAACvB,CAAC,EAAEC,CAAC,CAAC;QAAE,CAAC;QAAEtB,QAAQ;QAAE6C,WAAW,EAAE5C;MAAO,CAAC;MACpGW,UAAU,CAAEsB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEO,SAAS,CAAC,CAAC;MAC1C,IAAIpC,MAAM,EAAEA,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAES,SAAS,CAAC;IACzD;IACA,IAAIlC,IAAI,KAAKZ,KAAK,CAACE,IAAI,EAAE;MACvB,MAAMwB,CAAC,GAAGgB,CAAC,CAACC,WAAW,CAACC,OAAO;MAC/B,MAAMjB,CAAC,GAAGe,CAAC,CAACC,WAAW,CAACE,OAAO;MAC/BpB,UAAU,CAAC;QAAEC,CAAC;QAAEC;MAAE,CAAC,CAAC;MACpBJ,gBAAgB,CAAC,IAAI,CAAC;IACxB;IACA,IAAIX,IAAI,KAAKZ,KAAK,CAACG,KAAK,EAAE;MACxB,MAAMuB,CAAC,GAAGgB,CAAC,CAACC,WAAW,CAACC,OAAO;MAC/B,MAAMjB,CAAC,GAAGe,CAAC,CAACC,WAAW,CAACE,OAAO;MAC/B,MAAMM,QAAQ,GAAG;QACfJ,IAAI,EAAE,OAAO;QACbC,IAAI,EAAE;UAAEtB,CAAC;UAAEC,CAAC;UAAEyB,IAAI,EAAE,CAAC;UAAEC,IAAI,EAAE;QAAE,CAAC;QAChChD,QAAQ;QACR6C,WAAW,EAAE5C;MACf,CAAC;MACDW,UAAU,CAAEsB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEY,QAAQ,CAAC,CAAC;MACzC,IAAIzC,MAAM,EAAEA,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAEc,QAAQ,CAAC;IACxD;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAIZ,CAAC,IAAK;IAC/B,IAAI,CAAC5B,OAAO,IAAIF,IAAI,KAAKZ,KAAK,CAACC,GAAG,EAAE;IACpC,MAAMyB,CAAC,GAAGgB,CAAC,CAACC,WAAW,CAACC,OAAO;IAC/B,MAAMjB,CAAC,GAAGe,CAAC,CAACC,WAAW,CAACE,OAAO;IAC/B5B,UAAU,CAAEsB,IAAI,IAAK;MACnB,MAAMgB,IAAI,GAAGhB,IAAI,CAACA,IAAI,CAACiB,MAAM,GAAG,CAAC,CAAC;MAClC,IAAID,IAAI,IAAIA,IAAI,CAACR,IAAI,KAAK,MAAM,EAAE;QAChCQ,IAAI,CAACP,IAAI,CAACC,MAAM,CAACQ,IAAI,CAAC,CAAC/B,CAAC,EAAEC,CAAC,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAGY,IAAI,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC;MACrC;MACA,OAAOhB,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IAC5B5C,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAM6C,gBAAgB,GAAIlB,CAAC,IAAK;IAC9BA,CAAC,CAACmB,cAAc,CAAC,CAAC;IAClB,IAAI,CAACzC,WAAW,CAAC0C,IAAI,CAAC,CAAC,EAAE;IACzB,MAAMC,OAAO,GAAG;MACdhB,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE;QAAEtB,CAAC,EAAEF,OAAO,CAACE,CAAC;QAAEC,CAAC,EAAEH,OAAO,CAACG,CAAC;QAAEqC,KAAK,EAAE5C,WAAW;QAAEF;MAAM,CAAC;MAC/Db,QAAQ;MACR6C,WAAW,EAAE5C;IACf,CAAC;IACDW,UAAU,CAAEsB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEwB,OAAO,CAAC,CAAC;IACxC,IAAIrD,MAAM,EAAEA,MAAM,CAAC2B,IAAI,CAAC,mBAAmB,EAAE0B,OAAO,CAAC;IACrD1C,cAAc,CAAC,EAAE,CAAC;IAClBE,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAM0C,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIjD,OAAO,CAACwC,MAAM,KAAK,CAAC,EAAE;IAC1B3B,YAAY,CAAEU,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEvB,OAAO,CAACA,OAAO,CAACwC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9DvC,UAAU,CAAEsB,IAAI,IAAKA,IAAI,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACzC,CAAC;EAED,MAAMQ,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItC,SAAS,CAAC4B,MAAM,KAAK,CAAC,EAAE;IAC5BvC,UAAU,CAAEsB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEX,SAAS,CAACA,SAAS,CAAC4B,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAChE3B,YAAY,CAAEU,IAAI,IAAKA,IAAI,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC;EAED7E,SAAS,CAAC,MAAM;IACd,MAAMsF,MAAM,GAAG1D,SAAS,CAAC2D,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IACb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnCD,GAAG,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACM,MAAM,CAAC;IAEhD,MAAMC,QAAQ,GAAG,EAAE;IACnBL,GAAG,CAACM,IAAI,CAAC,CAAC;IACVN,GAAG,CAACO,WAAW,GAAG,SAAS;IAC3BP,GAAG,CAACQ,SAAS,GAAG,CAAC;IACjB,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,MAAM,CAACK,KAAK,EAAE9C,CAAC,IAAIgD,QAAQ,EAAE;MAC/CL,GAAG,CAACS,SAAS,CAAC,CAAC;MACfT,GAAG,CAACU,MAAM,CAACrD,CAAC,EAAE,CAAC,CAAC;MAChB2C,GAAG,CAACW,MAAM,CAACtD,CAAC,EAAEyC,MAAM,CAACM,MAAM,CAAC;MAC5BJ,GAAG,CAACY,MAAM,CAAC,CAAC;IACd;IACA,KAAK,IAAItD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,MAAM,CAACM,MAAM,EAAE9C,CAAC,IAAI+C,QAAQ,EAAE;MAChDL,GAAG,CAACS,SAAS,CAAC,CAAC;MACfT,GAAG,CAACU,MAAM,CAAC,CAAC,EAAEpD,CAAC,CAAC;MAChB0C,GAAG,CAACW,MAAM,CAACb,MAAM,CAACK,KAAK,EAAE7C,CAAC,CAAC;MAC3B0C,GAAG,CAACY,MAAM,CAAC,CAAC;IACd;IACAZ,GAAG,CAACa,OAAO,CAAC,CAAC;IAEblE,OAAO,CAACmE,OAAO,CAAE7C,MAAM,IAAK;MAC1B,IAAIA,MAAM,CAACS,IAAI,KAAK,MAAM,EAAE;QAC1B,MAAM;UAAE7B,KAAK;UAAE+B;QAAO,CAAC,GAAGX,MAAM,CAACU,IAAI;QACrCqB,GAAG,CAACO,WAAW,GAAG1D,KAAK;QACvBmD,GAAG,CAACQ,SAAS,GAAG,CAAC;QACjBR,GAAG,CAACe,OAAO,GAAG,OAAO;QACrBf,GAAG,CAACS,SAAS,CAAC,CAAC;QACf7B,MAAM,CAACkC,OAAO,CAAC,CAAC,CAACzD,CAAC,EAAEC,CAAC,CAAC,EAAE0D,CAAC,KAAK;UAC5B,IAAIA,CAAC,KAAK,CAAC,EAAEhB,GAAG,CAACU,MAAM,CAACrD,CAAC,EAAEC,CAAC,CAAC,CAAC,KACzB0C,GAAG,CAACW,MAAM,CAACtD,CAAC,EAAEC,CAAC,CAAC;QACvB,CAAC,CAAC;QACF0C,GAAG,CAACY,MAAM,CAAC,CAAC;MACd;MACA,IAAI3C,MAAM,CAACS,IAAI,KAAK,MAAM,EAAE;QAC1B,MAAM;UAAErB,CAAC;UAAEC,CAAC;UAAEqC,KAAK;UAAE9C;QAAM,CAAC,GAAGoB,MAAM,CAACU,IAAI;QAC1CqB,GAAG,CAACiB,IAAI,GAAG,0BAA0B;QACrCjB,GAAG,CAACkB,SAAS,GAAGrE,KAAK,IAAI,MAAM;QAC/BmD,GAAG,CAACmB,QAAQ,CAACxB,KAAK,EAAEtC,CAAC,EAAEC,CAAC,CAAC;MAC3B;MACA,IAAIW,MAAM,CAACS,IAAI,KAAK,OAAO,EAAE;QAC3B,MAAM;UAAErB,CAAC;UAAEC,CAAC;UAAEyB,IAAI;UAAEC;QAAK,CAAC,GAAGf,MAAM,CAACU,IAAI;QACxC,MAAMyC,QAAQ,GAAG,EAAE;QACnBpB,GAAG,CAACM,IAAI,CAAC,CAAC;QACVN,GAAG,CAACO,WAAW,GAAG,SAAS;QAC3BP,GAAG,CAACQ,SAAS,GAAG,GAAG;QACnB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIjC,IAAI,EAAEiC,CAAC,EAAE,EAAE;UAC9BhB,GAAG,CAACS,SAAS,CAAC,CAAC;UACfT,GAAG,CAACU,MAAM,CAACrD,CAAC,EAAEC,CAAC,GAAG0D,CAAC,GAAGI,QAAQ,CAAC;UAC/BpB,GAAG,CAACW,MAAM,CAACtD,CAAC,GAAG2B,IAAI,GAAGoC,QAAQ,EAAE9D,CAAC,GAAG0D,CAAC,GAAGI,QAAQ,CAAC;UACjDpB,GAAG,CAACY,MAAM,CAAC,CAAC;QACd;QACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIrC,IAAI,EAAEqC,CAAC,EAAE,EAAE;UAC9BrB,GAAG,CAACS,SAAS,CAAC,CAAC;UACfT,GAAG,CAACU,MAAM,CAACrD,CAAC,GAAGgE,CAAC,GAAGD,QAAQ,EAAE9D,CAAC,CAAC;UAC/B0C,GAAG,CAACW,MAAM,CAACtD,CAAC,GAAGgE,CAAC,GAAGD,QAAQ,EAAE9D,CAAC,GAAGyB,IAAI,GAAGqC,QAAQ,CAAC;UACjDpB,GAAG,CAACY,MAAM,CAAC,CAAC;QACd;QACAZ,GAAG,CAACa,OAAO,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClE,OAAO,CAAC,CAAC;EAEb,oBACElB,OAAA,CAACb,GAAG;IACF0G,EAAE,EAAE;MACFC,CAAC,EAAE,CAAC;MACJC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,CAAC;MACfC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,GAAG;MACdxB,KAAK,EAAE,MAAM;MACbyB,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,YAAY;MACxBC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEFvG,OAAA,CAACT,KAAK;MAACiH,SAAS,EAAC,KAAK;MAACH,UAAU,EAAC,QAAQ;MAACI,OAAO,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,gBAC3DvG,OAAA;QACE2G,GAAG,EAAC,yDAAyD;QAC7DC,GAAG,EAAC,EAAE;QACNC,KAAK,EAAE;UAAEnC,KAAK,EAAE,EAAE;UAAEoC,WAAW,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eACFlH,OAAA;QAAM6G,KAAK,EAAE;UAAEM,QAAQ,EAAE,EAAE;UAAEC,UAAU,EAAE,GAAG;UAAEC,UAAU,EAAE;QAAiB,CAAE;QAAAd,QAAA,EAC1E7F,CAAC,CAAC,oCAAoC;MAAC;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAERlH,OAAA,CAACZ,KAAK;MACJkI,SAAS,EAAE,CAAE;MACbzB,EAAE,EAAE;QACFS,QAAQ,EAAE,UAAU;QACpBiB,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,EAAE;QACPC,MAAM,EAAE,EAAE;QACV3B,CAAC,EAAE,CAAC;QACJE,YAAY,EAAE,CAAC;QACfD,UAAU,EAAE,MAAM;QAClBI,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE,QAAQ;QACpBqB,GAAG,EAAE,GAAG;QACRzB,SAAS,EAAE;MACb,CAAE;MAAAM,QAAA,gBAEFvG,OAAA,CAACV,OAAO;QAACqI,KAAK,EAAEjH,CAAC,CAAC,sBAAsB,CAAE;QAACkH,SAAS,EAAC,OAAO;QAACC,mBAAmB,EAAEpI,IAAK;QAAA8G,QAAA,eACrFvG,OAAA,CAACX,UAAU;UAAC+B,KAAK,EAAEN,IAAI,KAAKZ,KAAK,CAACC,GAAG,GAAG,SAAS,GAAG,SAAU;UAAC2H,OAAO,EAAEA,CAAA,KAAM/G,OAAO,CAACb,KAAK,CAACC,GAAG,CAAE;UAAAoG,QAAA,eAC/FvG,OAAA,CAACN,QAAQ;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACVlH,OAAA,CAACV,OAAO;QAACqI,KAAK,EAAEjH,CAAC,CAAC,uBAAuB,CAAE;QAACkH,SAAS,EAAC,OAAO;QAACC,mBAAmB,EAAEpI,IAAK;QAAA8G,QAAA,eACtFvG,OAAA,CAACX,UAAU;UAAC+B,KAAK,EAAEN,IAAI,KAAKZ,KAAK,CAACE,IAAI,GAAG,SAAS,GAAG,SAAU;UAAC0H,OAAO,EAAEA,CAAA,KAAM/G,OAAO,CAACb,KAAK,CAACE,IAAI,CAAE;UAAAmG,QAAA,eACjGvG,OAAA,CAACL,cAAc;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACVlH,OAAA,CAACV,OAAO;QAACqI,KAAK,EAAEjH,CAAC,CAAC,wBAAwB,CAAE;QAACkH,SAAS,EAAC,OAAO;QAACC,mBAAmB,EAAEpI,IAAK;QAAA8G,QAAA,eACvFvG,OAAA,CAACX,UAAU;UAAC+B,KAAK,EAAEN,IAAI,KAAKZ,KAAK,CAACG,KAAK,GAAG,SAAS,GAAG,SAAU;UAACyH,OAAO,EAAEA,CAAA,KAAM/G,OAAO,CAACb,KAAK,CAACG,KAAK,CAAE;UAAAkG,QAAA,eACnGvG,OAAA,CAACJ,cAAc;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACVlH,OAAA,CAACR,OAAO;QAACqG,EAAE,EAAE;UAAEkC,EAAE,EAAE,CAAC;UAAErD,KAAK,EAAE;QAAG;MAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrClH,OAAA,CAACV,OAAO;QAACqI,KAAK,EAAEjH,CAAC,CAAC,wBAAwB,CAAE;QAACkH,SAAS,EAAC,OAAO;QAACC,mBAAmB,EAAEpI,IAAK;QAAA8G,QAAA,eACvFvG,OAAA,CAACb,GAAG;UAAAoH,QAAA,eACFvG,OAAA;YACEiD,IAAI,EAAC,OAAO;YACZiB,KAAK,EAAE9C,KAAM;YACb4G,QAAQ,EAAGpF,CAAC,IAAKvB,QAAQ,CAACuB,CAAC,CAACqF,MAAM,CAAC/D,KAAK,CAAE;YAC1C2C,KAAK,EAAE;cACLnC,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVuD,MAAM,EAAE,MAAM;cACdlC,YAAY,EAAE,CAAC;cACfmC,MAAM,EAAE,SAAS;cACjBpC,UAAU,EAAE,MAAM;cAClBqC,OAAO,EAAE;YACX;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACVlH,OAAA,CAACR,OAAO;QAACqG,EAAE,EAAE;UAAEkC,EAAE,EAAE,CAAC;UAAErD,KAAK,EAAE;QAAG;MAAE;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrClH,OAAA,CAACV,OAAO;QAACqI,KAAK,EAAEjH,CAAC,CAAC,uBAAuB,CAAE;QAACkH,SAAS,EAAC,OAAO;QAACC,mBAAmB,EAAEpI,IAAK;QAAA8G,QAAA,eACtFvG,OAAA,CAACX,UAAU;UAACyI,OAAO,EAAE3D,UAAW;UAACkE,QAAQ,EAAEnH,OAAO,CAACwC,MAAM,KAAK,CAAE;UAAA6C,QAAA,eAC9DvG,OAAA,CAACH,QAAQ;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACVlH,OAAA,CAACV,OAAO;QAACqI,KAAK,EAAEjH,CAAC,CAAC,uBAAuB,CAAE;QAACkH,SAAS,EAAC,OAAO;QAACC,mBAAmB,EAAEpI,IAAK;QAAA8G,QAAA,eACtFvG,OAAA,CAACX,UAAU;UAACyI,OAAO,EAAE1D,UAAW;UAACiE,QAAQ,EAAEvG,SAAS,CAAC4B,MAAM,KAAK,CAAE;UAAA6C,QAAA,eAChEvG,OAAA,CAACF,QAAQ;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAERlH,OAAA;MACEsI,GAAG,EAAE3H,SAAU;MACf+D,KAAK,EAAE,GAAI;MACXC,MAAM,EAAE,GAAI;MACZ4D,aAAa,EAAE5F,iBAAkB;MACjC6F,aAAa,EAAEhF,iBAAkB;MACjCiF,WAAW,EAAE5E,eAAgB;MAC7BgD,KAAK,EAAE;QACLqB,MAAM,EAAE,mBAAmB;QAC3BlC,YAAY,EAAE,EAAE;QAChBmC,MAAM,EAAErH,IAAI,KAAKZ,KAAK,CAACC,GAAG,GAAG,WAAW,GAAG,SAAS;QACpDuI,UAAU,EAAE,EAAE;QACd3C,UAAU,EAAE;MACd;IAAE;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAED1F,aAAa,iBACZxB,OAAA,CAACb,GAAG;MACFwJ,SAAS,EAAC,MAAM;MAChBC,QAAQ,EAAE9E,gBAAiB;MAC3B+B,EAAE,EAAE;QACFS,QAAQ,EAAE,UAAU;QACpBiB,IAAI,EAAE7F,OAAO,CAACE,CAAC,GAAG,EAAE;QACpB4F,GAAG,EAAE9F,OAAO,CAACG,CAAC,GAAG,GAAG;QACpB4F,MAAM,EAAE;MACV,CAAE;MAAAlB,QAAA,eAEFvG,OAAA;QACEiD,IAAI,EAAC,MAAM;QACXiB,KAAK,EAAE5C,WAAY;QACnB0G,QAAQ,EAAGpF,CAAC,IAAKrB,cAAc,CAACqB,CAAC,CAACqF,MAAM,CAAC/D,KAAK,CAAE;QAChD2E,MAAM,EAAEA,CAAA,KAAMpH,gBAAgB,CAAC,KAAK,CAAE;QACtCqH,SAAS;QACTjC,KAAK,EAAE;UACLqB,MAAM,EAAE,mBAAmB;UAC3BlC,YAAY,EAAE,CAAC;UACfoC,OAAO,EAAE,UAAU;UACnBjB,QAAQ,EAAE,EAAE;UACZE,UAAU,EAAE;QACd;MAAE;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACzG,EAAA,CAnTuBH,UAAU;EAAA,QAClBpB,cAAc;AAAA;AAAA6J,EAAA,GADNzI,UAAU;AAAA,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}